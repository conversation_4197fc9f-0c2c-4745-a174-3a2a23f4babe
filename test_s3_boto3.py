"""
Test S3 connectivity using boto3 directly
This helps diagnose S3 access issues
"""

import modal

# Add boto3 to the image
image = modal.Image.debian_slim().pip_install("boto3")

app = modal.App(
    name="maxwattz-s3-boto3-test",
    image=image,
    secrets=[modal.Secret.from_name("maxwattz-videos")]
)

@app.function()
def test_s3_with_boto3():
    """Test S3 access using boto3 directly"""
    import boto3
    import os
    
    print("Testing S3 access with boto3...")
    
    # Get credentials from environment (Modal loads them from secret)
    aws_access_key = os.environ.get('AWS_ACCESS_KEY_ID')
    aws_secret_key = os.environ.get('AWS_SECRET_ACCESS_KEY')
    aws_region = os.environ.get('AWS_DEFAULT_REGION', 'us-east-1')
    
    print(f"AWS Region: {aws_region}")
    print(f"AWS Access Key ID: {aws_access_key[:10]}..." if aws_access_key else "No access key found!")
    
    try:
        # Create S3 client
        s3 = boto3.client(
            's3',
            aws_access_key_id=aws_access_key,
            aws_secret_access_key=aws_secret_key,
            region_name=aws_region
        )
        
        # List buckets to verify credentials work
        print("\nListing S3 buckets:")
        buckets = s3.list_buckets()
        for bucket in buckets['Buckets'][:5]:  # Show first 5
            print(f"  - {bucket['Name']}")
        
        # Try to list objects in the specific bucket
        bucket_name = 'maxwattz-videos'
        print(f"\nTrying to access bucket: {bucket_name}")
        
        try:
            # List objects with prefix
            prefixes = [
                'maxwattz-running-videos-raw-side/',
                'maxwattz-running-videos-raw-rear/',
                'maxwattz-running-metrics-side/',
                'maxwattz-running-metrics-rear/'
            ]
            
            for prefix in prefixes:
                response = s3.list_objects_v2(
                    Bucket=bucket_name,
                    Prefix=prefix,
                    MaxKeys=5
                )
                
                count = response.get('KeyCount', 0)
                print(f"\n  📁 {prefix}")
                print(f"     Found {count} objects")
                
                if 'Contents' in response:
                    for obj in response['Contents'][:3]:
                        print(f"       - {obj['Key']}")
                        
        except s3.exceptions.NoSuchBucket:
            print(f"  ❌ Bucket '{bucket_name}' not found")
            
            # Try to find the right bucket
            print("\n  Looking for buckets with 'maxwattz' in name:")
            all_buckets = s3.list_buckets()
            for bucket in all_buckets['Buckets']:
                if 'maxwattz' in bucket['Name'].lower() or 'wattz' in bucket['Name'].lower():
                    print(f"    Found: {bucket['Name']}")
                    
        except Exception as e:
            print(f"  ❌ Error accessing bucket: {e}")
            print(f"      Error type: {type(e).__name__}")
            
        # Test Access Point if regular bucket fails
        print("\n\nTesting S3 Access Point:")
        access_point_arn = "arn:aws:s3:us-east-1:043309327841:accesspoint/biometricvideos"
        
        # Access points require different approach
        # Extract bucket name from access point
        try:
            # List through access point (requires different syntax)
            response = s3.list_objects_v2(
                Bucket=access_point_arn,
                MaxKeys=5
            )
            print(f"  ✅ Access Point working! Found {response.get('KeyCount', 0)} objects")
        except Exception as e:
            print(f"  ❌ Access Point error: {e}")
            
        return {"success": True, "message": "S3 connectivity test completed"}
        
    except Exception as e:
        print(f"\n❌ Failed to create S3 client: {e}")
        return {"success": False, "error": str(e)}

@app.local_entrypoint()
def main():
    """Run the boto3 S3 test"""
    result = test_s3_with_boto3.remote()
    print(f"\nFinal result: {result}")

if __name__ == "__main__":
    main()