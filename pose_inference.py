"""
MaxWattz Pose Inference Pipeline
Processes treadmill running videos with BlazePose + SmoothNet
"""

import modal
import os
import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Tuple, Optional, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


# 1. Define container image with all dependencies
image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        "libgl1-mesa-glx", 
        "libglib2.0-0", 
        "ffmpeg",
        "libgomp1",  # Required for MediaPipe multi-threading
        "libxcb1",   # Required for MediaPipe GUI components
        "git"        # Required for installing from git repositories
    ])
    .pip_install([
        "opencv-python~=4.8.0",
        "mediapipe==0.10.15",
        "torch==2.0.1",
        "torchvision==0.15.2",
        "numpy>=1.21",
        "boto3>=1.26.0",
        "psycopg2-binary>=2.9.0",  # For Supabase
        "supabase>=2.0.0",  # Supabase Python client
        # SmoothNet dependencies  
        "pyyaml>=5.4.1",
        "easydict>=1.9",
        "scipy>=1.7.0",
    ])
)

# 2. Configure Modal app with S3 access
MOUNT_PATH = "/mnt/s3"
app = modal.App(
    name="maxwattz-pose-inference-v2",
    image=image,
    secrets=[
        modal.Secret.from_name("maxwattz-videos"),  # AWS credentials
        modal.Secret.from_name("maxwattz-supabase"),  # Supabase credentials
    ],
    volumes={
        MOUNT_PATH: modal.CloudBucketMount(
            "maxwattz-videos",
            secret=modal.Secret.from_name("maxwattz-videos")
        )
    }
)

# 3. Helper function for consistent test UUIDs
@app.function()
def create_test_uuid(test_name: str = "coordinate-fix") -> str:
    """Generate a consistent test UUID for testing purposes"""
    test_namespace = uuid.UUID('12345678-1234-5678-1234-123456789012')
    test_uuid = str(uuid.uuid5(test_namespace, f"test-{test_name}"))
    print(f"Test UUID for '{test_name}': {test_uuid}")
    return test_uuid

# 4. GPU-accelerated pose estimation class
@app.cls(gpu="A10G", timeout=600)
class PoseEstimator:
    """Processes videos with BlazePose and SmoothNet"""
    
    @modal.enter()
    def initialize(self):
        """Initialize models when container starts"""
        logger.info("Initializing PoseEstimator...")
        
        # Initialize MediaPipe BlazePose
        import mediapipe as mp
        self.mp = mp  # Store mp reference
        self.mp_pose = mp.solutions.pose
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Model complexity mapping
        self.model_complexity_map = {
            'lite': 0,
            'full': 1,
            'heavy': 2
        }
        
        # Initialize Supabase client
        import os
        from supabase import create_client, Client
        
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_SERVICE_KEY')  # Service key for server-side
        
        if supabase_url and supabase_key:
            self.supabase: Client = create_client(supabase_url, supabase_key)
            logger.info("Supabase client initialized")
        else:
            self.supabase = None
            logger.warning("Supabase credentials not found - database updates disabled")
        
        logger.info("PoseEstimator initialized successfully")
    
    def _get_model_complexity(self, view_type: str) -> int:
        """Get model complexity based on view type"""
        if view_type == 'side':
            return self.model_complexity_map['heavy']
        else:  # rear
            return self.model_complexity_map['full']
    
    def _extract_video_metadata(self, video_path: str) -> Dict:
        """Extract video metadata using OpenCV"""
        import cv2
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {video_path}")
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Validate FPS to avoid division by zero
        if fps <= 0:
            logger.warning("Invalid FPS detected, defaulting to 30")
            fps = 30.0
            
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        # Check for rotation metadata in MOV files
        # MOV files often have rotation metadata that OpenCV doesn't apply
        if video_path.lower().endswith('.mov') and width > height:
            logger.info(f"Detected MOV file with landscape dimensions ({width}x{height}), checking if rotation needed")
            # For portrait videos incorrectly read as landscape, swap dimensions
            # This is a common issue with iPhone videos
            width, height = height, width
            logger.info(f"Swapped dimensions to {width}x{height} for proper portrait orientation")
            
        metadata = {
            'width': width,
            'height': height,
            'fps': fps,
            'frame_count': frame_count,
            'duration': frame_count / fps if fps > 0 else 0
        }
        
        cap.release()
        logger.info(f"Video metadata: {metadata}")
        return metadata
    
    def _process_frame(self, frame, pose_model, metadata_width: int, metadata_height: int) -> Optional[List[Dict]]:
        """Process a single frame and extract keypoints
        
        Args:
            frame: OpenCV frame to process
            pose_model: MediaPipe pose model
            metadata_width: Width from video metadata (corrected for rotation)
            metadata_height: Height from video metadata (corrected for rotation)
        """
        import cv2
        
        # Convert BGR to RGB for MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Process with MediaPipe
        results = pose_model.process(rgb_frame)
        
        if not results.pose_landmarks:
            return None
        
        # Extract keypoints with pixel coordinates
        keypoints = []
        landmarks = results.pose_landmarks.landmark
        
        # Use metadata dimensions for coordinate conversion (handles MOV rotation)
        width = metadata_width
        height = metadata_height
        
        # Log coordinate system being used
        frame_height, frame_width = frame.shape[:2]
        if frame_width != width or frame_height != height:
            logger.info(f"Coordinate conversion: Using metadata dims ({width}x{height}) vs frame dims ({frame_width}x{frame_height})")
        
        # Track coordinate clamping statistics
        clamped_count = 0
        out_of_bounds_details = []
        
        # BlazePose keypoint names (33 keypoints)
        keypoint_names = [
            'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
            'right_eye_inner', 'right_eye', 'right_eye_outer',
            'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
            'left_index', 'right_index', 'left_thumb', 'right_thumb',
            'left_hip', 'right_hip', 'left_knee', 'right_knee',
            'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
            'left_foot_index', 'right_foot_index'
        ]
        
        for idx, landmark in enumerate(landmarks):
            # CRITICAL FIX: Clamp normalized coordinates to valid 0-1 range
            # MediaPipe can return values outside 0-1 when landmarks are detected outside image
            
            # Check if coordinates need clamping
            was_clamped = False
            if landmark.x < 0.0 or landmark.x > 1.0 or landmark.y < 0.0 or landmark.y > 1.0:
                was_clamped = True
                clamped_count += 1
                out_of_bounds_details.append({
                    'keypoint': keypoint_names[idx] if idx < len(keypoint_names) else f'keypoint_{idx}',
                    'raw_x': landmark.x,
                    'raw_y': landmark.y
                })
            
            norm_x = max(0.0, min(1.0, landmark.x))
            norm_y = max(0.0, min(1.0, landmark.y))
            # Z coordinates in MediaPipe can be negative (depth), so preserve raw values
            norm_z = landmark.z  # Keep raw Z for depth information
            
            # Convert normalized coordinates to pixels
            keypoint = {
                'x': norm_x * width,
                'y': norm_y * height,
                'z': norm_z * width,  # Z scaled with width
                'score': landmark.visibility,  # MediaPipe calls it visibility
                'name': keypoint_names[idx] if idx < len(keypoint_names) else f'keypoint_{idx}'
            }
            
            # Log coordinate transformation for debugging including Z
            if idx < 3 or was_clamped:  # Log first 3 keypoints AND any clamped keypoints
                logger.info(f"Keypoint {idx} ({keypoint['name']}): "
                           f"raw=({landmark.x:.3f}, {landmark.y:.3f}, {landmark.z:.3f}) -> "
                           f"clamped=({norm_x:.3f}, {norm_y:.3f}, {norm_z:.3f}) -> "
                           f"pixel=({keypoint['x']:.1f}, {keypoint['y']:.1f}, {keypoint['z']:.1f})"
                           f"{' [CLAMPED]' if was_clamped else ''}")
            
            keypoints.append(keypoint)
        
        # Report clamping statistics if any coordinates were out of bounds
        if clamped_count > 0:
            logger.warning(f"Clamped {clamped_count} out-of-bounds coordinates")
            for detail in out_of_bounds_details[:5]:  # Show first 5 for brevity
                logger.warning(f"  - {detail['keypoint']}: ({detail['raw_x']:.3f}, {detail['raw_y']:.3f})")
            if clamped_count > 5:
                logger.warning(f"  ... and {clamped_count - 5} more")
        
        # Validate coordinate fix: Check for invalid (0,0) coordinates
        invalid_coords = [kp for kp in keypoints if kp['x'] == 0.0 and kp['y'] == 0.0]
        if invalid_coords:
            logger.warning(f"Found {len(invalid_coords)} keypoints at (0,0) after coordinate conversion:")
            for kp in invalid_coords[:3]:  # Show first 3
                logger.warning(f"  - {kp['name']}: (0.0, 0.0) score={kp['score']:.3f}")
        else:
            logger.info(f"Coordinate validation: All {len(keypoints)} keypoints have valid non-zero coordinates")
        
        return keypoints
    
    def _validate_coordinate_range(self, coords: List[float], coord_type: str, frame_idx: int, 
                                  expected_min: float, expected_max: float) -> bool:
        """
        Validate coordinate ranges and log issues for debugging
        
        Args:
            coords: List of coordinate values to validate
            coord_type: Type of coordinates (e.g., "world", "root-relative", "normalized")
            frame_idx: Frame index for logging
            expected_min: Expected minimum value
            expected_max: Expected maximum value
            
        Returns:
            bool: True if all coordinates are within range, False otherwise
        """
        coords_array = np.array(coords)
        min_val = np.min(coords_array)
        max_val = np.max(coords_array)
        mean_val = np.mean(coords_array)
        std_val = np.std(coords_array)
        
        within_range = (min_val >= expected_min) and (max_val <= expected_max)
        
        # Log validation results for first few frames
        if frame_idx < 3 or not within_range:
            logger.info(f"Frame {frame_idx} {coord_type} validation:")
            logger.info(f"  Range: [{min_val:.2f}, {max_val:.2f}] (expected: [{expected_min}, {expected_max}])")
            logger.info(f"  Mean: {mean_val:.2f}, Std: {std_val:.2f}")
            
            if not within_range:
                logger.warning(f"  ⚠️ OUTSIDE EXPECTED RANGE!")
                # Log specific outliers
                outliers = [(i, val) for i, val in enumerate(coords) 
                           if val < expected_min or val > expected_max]
                if outliers[:5]:  # Show first 5 outliers
                    logger.warning(f"  Outliers (first 5): {outliers[:5]}")
        
        return within_range
    
    def _validate_keypoint_mapping(self, frame_idx: int) -> Dict[str, Any]:
        """
        CRITICAL FIX: Validate BlazePose vs 3DPW keypoint ordering compatibility - Issue #8
        
        Analyzes keypoint semantic mapping to ensure SmoothNet receives data in expected format
        
        Returns:
            Dict containing mapping analysis results
        """
        # BlazePose (MediaPipe) 33 keypoint ordering
        blazepose_keypoints = [
            'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
            'right_eye_inner', 'right_eye', 'right_eye_outer', 'left_ear',
            'right_ear', 'mouth_left', 'mouth_right', 'left_shoulder',       # 11
            'right_shoulder', 'left_elbow', 'right_elbow', 'left_wrist',     # 12-15
            'right_wrist', 'left_pinky', 'left_index', 'left_thumb',         # 16-19
            'right_pinky', 'right_index', 'right_thumb', 'left_hip',         # 20-23
            'right_hip', 'left_knee', 'right_knee', 'left_ankle',            # 24-27
            'right_ankle', 'left_heel', 'right_heel', 'left_foot_index',     # 28-31
            'right_foot_index'                                               # 32
        ]
        
        # 3DPW dataset keypoint ordering (what SmoothNet expects)
        # Based on SMPL/3DPW conventions from SmoothNet paper
        smoothnet_expected_keypoints = [
            'pelvis', 'left_hip', 'right_hip', 'spine1', 'left_knee',        # 0-4
            'right_knee', 'spine2', 'left_ankle', 'right_ankle', 'spine3',   # 5-9
            'left_foot', 'right_foot', 'neck', 'left_collar', 'right_collar', # 10-14
            'head', 'left_shoulder', 'right_shoulder', 'left_elbow',         # 15-18
            'right_elbow', 'left_wrist', 'right_wrist', 'left_hand',         # 19-22
            'right_hand', 'spine4', 'left_toe', 'right_toe', 'left_thumb',   # 23-27
            'right_thumb', 'left_middle', 'right_middle', 'left_pinky',      # 28-31
            'right_pinky'                                                    # 32
        ]
        
        # Create semantic mapping analysis
        mapping_analysis = {
            'compatible_mappings': {},
            'missing_in_blazepose': [],
            'missing_in_3dpw': [],
            'semantic_issues': []
        }
        
        # Find semantic matches
        blazepose_set = set(blazepose_keypoints)
        smoothnet_set = set(smoothnet_expected_keypoints)
        
        # Direct semantic matches
        common_semantics = ['left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
                           'left_wrist', 'right_wrist', 'left_hip', 'right_hip',
                           'left_knee', 'right_knee', 'left_ankle', 'right_ankle']
        
        for semantic in common_semantics:
            if semantic in blazepose_set and semantic in smoothnet_set:
                bp_idx = blazepose_keypoints.index(semantic)
                sn_idx = smoothnet_expected_keypoints.index(semantic)
                mapping_analysis['compatible_mappings'][semantic] = {
                    'blazepose_idx': bp_idx,
                    'smoothnet_idx': sn_idx,
                    'direct_match': True
                }
        
        # Identify missing keypoints
        mapping_analysis['missing_in_blazepose'] = [kp for kp in smoothnet_expected_keypoints 
                                                   if kp not in blazepose_set]
        mapping_analysis['missing_in_3dpw'] = [kp for kp in blazepose_keypoints 
                                              if kp not in smoothnet_set]
        
        # Identify potential issues
        critical_missing = ['pelvis', 'spine1', 'spine2', 'spine3', 'neck']
        for missing in critical_missing:
            if missing in mapping_analysis['missing_in_blazepose']:
                mapping_analysis['semantic_issues'].append(f"Missing critical keypoint: {missing}")
        
        # Log mapping analysis for first few frames
        if frame_idx < 3:
            logger.info(f"=== KEYPOINT MAPPING ANALYSIS (Frame {frame_idx}) ===")
            logger.info(f"Compatible mappings: {len(mapping_analysis['compatible_mappings'])}/33")
            logger.info(f"Direct semantic matches found:")
            for semantic, mapping in mapping_analysis['compatible_mappings'].items():
                logger.info(f"  {semantic}: BlazePose[{mapping['blazepose_idx']}] → 3DPW[{mapping['smoothnet_idx']}]")
            
            if mapping_analysis['semantic_issues']:
                logger.warning(f"Semantic issues detected: {mapping_analysis['semantic_issues']}")
            
            logger.info(f"Missing in BlazePose: {mapping_analysis['missing_in_blazepose'][:5]}...")
            logger.info(f"Extra in BlazePose: {mapping_analysis['missing_in_3dpw'][:5]}...")
        
        return mapping_analysis
    
    def _validate_coordinate_system(self, world_coords: List[List[float]], frame_idx: int) -> Dict[str, Any]:
        """
        CRITICAL FIX: Validate coordinate system conventions match 3DPW expectations - Issue #8
        
        Analyzes anatomical relationships and coordinate system characteristics
        
        Args:
            world_coords: World coordinates for all keypoints in current frame
            frame_idx: Frame index for logging
            
        Returns:
            Dict containing coordinate system analysis
        """
        if len(world_coords) < 33:
            return {'error': 'Insufficient keypoints for coordinate system validation'}
        
        # Extract key anatomical landmarks (BlazePose indices)
        nose = world_coords[0]
        left_shoulder = world_coords[11]
        right_shoulder = world_coords[12]
        left_hip = world_coords[23]
        right_hip = world_coords[24]
        left_knee = world_coords[25]
        right_knee = world_coords[26]
        left_ankle = world_coords[27]
        right_ankle = world_coords[28]
        
        analysis = {
            'anatomical_measurements': {},
            'coordinate_system_check': {},
            'potential_issues': []
        }
        
        # Anatomical measurements (in mm)
        shoulder_width = abs(left_shoulder[0] - right_shoulder[0])
        hip_width = abs(left_hip[0] - right_hip[0])
        shoulder_center = [(left_shoulder[0] + right_shoulder[0])/2, 
                          (left_shoulder[1] + right_shoulder[1])/2,
                          (left_shoulder[2] + right_shoulder[2])/2]
        hip_center = [(left_hip[0] + right_hip[0])/2,
                     (left_hip[1] + right_hip[1])/2, 
                     (left_hip[2] + right_hip[2])/2]
        
        torso_height = abs(shoulder_center[1] - hip_center[1])
        left_thigh_length = ((left_hip[0] - left_knee[0])**2 + 
                            (left_hip[1] - left_knee[1])**2 + 
                            (left_hip[2] - left_knee[2])**2)**0.5
        left_shin_length = ((left_knee[0] - left_ankle[0])**2 + 
                           (left_knee[1] - left_ankle[1])**2 + 
                           (left_knee[2] - left_ankle[2])**2)**0.5
        
        analysis['anatomical_measurements'] = {
            'shoulder_width': shoulder_width,
            'hip_width': hip_width,
            'torso_height': torso_height,
            'left_thigh_length': left_thigh_length,
            'left_shin_length': left_shin_length,
            'body_height_estimate': torso_height + left_thigh_length + left_shin_length
        }
        
        # Coordinate system characteristics
        # Check if Y-axis points up or down (based on head vs hip positions)
        head_above_hips = nose[1] < hip_center[1]  # If Y increases upward, head should have smaller Y
        
        # Check left-right consistency (X-axis direction)
        left_right_consistent = left_shoulder[0] < right_shoulder[0]  # Assuming X increases rightward
        
        # Check depth relationships (Z-axis)
        avg_z_depth = sum(coord[2] for coord in world_coords) / len(world_coords)
        z_variation = max(coord[2] for coord in world_coords) - min(coord[2] for coord in world_coords)
        
        analysis['coordinate_system_check'] = {
            'y_axis_direction': 'upward' if head_above_hips else 'downward',
            'x_axis_direction': 'left_to_right' if left_right_consistent else 'right_to_left',
            'average_z_depth': avg_z_depth,
            'z_variation': z_variation,
            'coordinate_center': [sum(coord[0] for coord in world_coords) / len(world_coords),
                                 sum(coord[1] for coord in world_coords) / len(world_coords),
                                 avg_z_depth]
        }
        
        # Validate against expected human proportions
        expected_ranges = {
            'shoulder_width': (300, 600),      # mm
            'hip_width': (200, 400),           # mm  
            'torso_height': (400, 800),        # mm
            'thigh_length': (300, 600),        # mm
            'shin_length': (300, 500),         # mm
            'body_height': (1400, 2200)        # mm
        }
        
        measurements = analysis['anatomical_measurements']
        for measurement, (min_val, max_val) in expected_ranges.items():
            if measurement in measurements:
                value = measurements[measurement]
                if not (min_val <= value <= max_val):
                    analysis['potential_issues'].append(
                        f"{measurement}: {value:.1f}mm outside expected range [{min_val}-{max_val}]mm"
                    )
        
        # Check coordinate system issues
        if not head_above_hips:
            analysis['potential_issues'].append("Y-axis: Head not above hips - coordinate system may be inverted")
        
        if z_variation > 1000:  # More than 1m depth variation seems excessive for side-view
            analysis['potential_issues'].append(f"Z-axis: Excessive depth variation ({z_variation:.1f}mm)")
        
        # 3DPW compatibility checks
        # 3DPW typically uses:
        # - Y-axis pointing up
        # - X-axis pointing right
        # - Z-axis pointing forward (away from camera)
        # - Root-centered coordinates
        
        compatibility_score = 0
        total_checks = 4
        
        if head_above_hips:
            compatibility_score += 1
        if left_right_consistent:
            compatibility_score += 1
        if 300 <= shoulder_width <= 600:
            compatibility_score += 1
        if 1400 <= measurements['body_height_estimate'] <= 2200:
            compatibility_score += 1
            
        analysis['3dpw_compatibility_score'] = compatibility_score / total_checks
        
        # Log analysis for first few frames
        if frame_idx < 3:
            logger.info(f"=== COORDINATE SYSTEM ANALYSIS (Frame {frame_idx}) ===")
            logger.info(f"Anatomical measurements:")
            for key, value in measurements.items():
                logger.info(f"  {key}: {value:.1f}mm")
            
            coord_sys = analysis['coordinate_system_check']
            logger.info(f"Coordinate system:")
            logger.info(f"  Y-axis: {coord_sys['y_axis_direction']}")
            logger.info(f"  X-axis: {coord_sys['x_axis_direction']}")
            logger.info(f"  Z-depth avg: {coord_sys['average_z_depth']:.1f}mm, variation: {coord_sys['z_variation']:.1f}mm")
            
            logger.info(f"3DPW compatibility score: {analysis['3dpw_compatibility_score']:.1%}")
            
            if analysis['potential_issues']:
                logger.warning(f"Potential coordinate system issues:")
                for issue in analysis['potential_issues']:
                    logger.warning(f"  - {issue}")
        
        return analysis
    
    def _analyze_model_behavior(self, input_tensor: Any, output_tensor: Any) -> Dict[str, Any]:
        """
        CRITICAL FIX: Analyze SmoothNet input/output patterns for unexpected behavior - Issue #8
        
        Compares input vs output to detect model compatibility issues
        
        Args:
            input_tensor: SmoothNet input tensor (1, T, 99)
            output_tensor: SmoothNet output tensor (1, T, 99)
            
        Returns:
            Dict containing model behavior analysis
        """
        analysis = {
            'smoothing_effectiveness': {},
            'coordinate_drift': {},
            'temporal_patterns': {},
            'anomaly_detection': {},
            'model_health_score': 0.0
        }
        
        # Convert to numpy for analysis
        if hasattr(input_tensor, 'detach'):
            input_np = input_tensor.detach().cpu().numpy()
        else:
            input_np = np.array(input_tensor)
            
        if hasattr(output_tensor, 'detach'):
            output_np = output_tensor.detach().cpu().numpy()
        else:
            output_np = np.array(output_tensor)
        
        # Analyze smoothing effectiveness per keypoint
        num_frames = input_np.shape[1]
        num_keypoints = 33
        
        for kp_idx in range(num_keypoints):
            # Extract keypoint coordinates across time
            kp_start_idx = kp_idx * 3
            kp_end_idx = kp_start_idx + 3
            
            input_kp = input_np[0, :, kp_start_idx:kp_end_idx]  # Shape: (T, 3)
            output_kp = output_np[0, :, kp_start_idx:kp_end_idx]
            
            # Calculate temporal variance (smoothness measure)
            input_variance = np.var(input_kp, axis=0)  # Variance for each coordinate
            output_variance = np.var(output_kp, axis=0)
            
            # Smoothing ratio (higher = more smoothing)
            smoothing_ratio = input_variance / (output_variance + 1e-8)
            
            # Store analysis for key keypoints
            if kp_idx in [0, 11, 12, 23, 24]:  # nose, shoulders, hips
                keypoint_names = ['nose', 'left_shoulder', 'right_shoulder', 'left_hip', 'right_hip']
                kp_name = keypoint_names[[0, 11, 12, 23, 24].index(kp_idx)]
                
                analysis['smoothing_effectiveness'][kp_name] = {
                    'input_variance': float(np.mean(input_variance)),
                    'output_variance': float(np.mean(output_variance)),
                    'smoothing_ratio': float(np.mean(smoothing_ratio)),
                    'effective_smoothing': float(np.mean(smoothing_ratio)) > 1.2  # At least 20% variance reduction
                }
        
        # Analyze coordinate drift (systematic bias)
        input_mean = np.mean(input_np, axis=1, keepdims=True)  # Mean across time
        output_mean = np.mean(output_np, axis=1, keepdims=True)
        coordinate_drift = output_mean - input_mean
        
        # Calculate drift statistics
        drift_magnitude = np.sqrt(np.sum(coordinate_drift**2, axis=2))  # L2 norm per keypoint
        max_drift = float(np.max(drift_magnitude))
        avg_drift = float(np.mean(drift_magnitude))
        
        analysis['coordinate_drift'] = {
            'max_drift_mm': max_drift,
            'avg_drift_mm': avg_drift,
            'significant_drift': max_drift > 50.0,  # More than 5cm drift is concerning
            'drift_pattern': 'systematic' if avg_drift > 10.0 else 'minimal'
        }
        
        # Analyze temporal patterns
        # Check for unexpected temporal artifacts
        input_temporal_diff = np.diff(input_np, axis=1)  # Frame-to-frame differences
        output_temporal_diff = np.diff(output_np, axis=1)
        
        input_temporal_variance = float(np.var(input_temporal_diff))
        output_temporal_variance = float(np.var(output_temporal_diff))
        temporal_smoothing = input_temporal_variance / (output_temporal_variance + 1e-8)
        
        analysis['temporal_patterns'] = {
            'input_temporal_variance': input_temporal_variance,
            'output_temporal_variance': output_temporal_variance,
            'temporal_smoothing_ratio': float(temporal_smoothing),
            'good_temporal_smoothing': temporal_smoothing > 1.5
        }
        
        # Anomaly detection
        anomalies = []
        
        # Check for coordinate explosion
        input_range = float(np.max(input_np) - np.min(input_np))
        output_range = float(np.max(output_np) - np.min(output_np))
        if output_range > input_range * 2.0:
            anomalies.append(f"Coordinate explosion: output range {output_range:.1f} >> input range {input_range:.1f}")
        
        # Check for coordinate collapse
        if output_range < input_range * 0.5:
            anomalies.append(f"Coordinate collapse: output range {output_range:.1f} << input range {input_range:.1f}")
        
        # Check for NaN or infinite values
        if np.any(np.isnan(output_np)) or np.any(np.isinf(output_np)):
            anomalies.append("Model output contains NaN or infinite values")
        
        # Check for extreme coordinate drift
        if max_drift > 100.0:  # 10cm drift
            anomalies.append(f"Extreme coordinate drift detected: {max_drift:.1f}mm")
        
        analysis['anomaly_detection'] = {
            'anomalies_found': anomalies,
            'anomaly_count': len(anomalies),
            'model_stable': len(anomalies) == 0
        }
        
        # Calculate overall model health score
        health_factors = [
            1.0 if temporal_smoothing > 1.5 else 0.5,  # Good temporal smoothing
            1.0 if max_drift < 50.0 else 0.0,          # Low coordinate drift
            1.0 if len(anomalies) == 0 else 0.0,       # No anomalies
            1.0 if output_range > 0 else 0.0           # Non-collapsed output
        ]
        
        analysis['model_health_score'] = sum(health_factors) / len(health_factors)
        
        # Log analysis
        logger.info(f"=== MODEL BEHAVIOR ANALYSIS ===")
        logger.info(f"Smoothing effectiveness (key keypoints):")
        for kp_name, stats in analysis['smoothing_effectiveness'].items():
            logger.info(f"  {kp_name}: ratio={stats['smoothing_ratio']:.2f}, effective={stats['effective_smoothing']}")
        
        logger.info(f"Coordinate drift: max={max_drift:.1f}mm, avg={avg_drift:.1f}mm")
        logger.info(f"Temporal smoothing ratio: {temporal_smoothing:.2f}")
        logger.info(f"Model health score: {analysis['model_health_score']:.1%}")
        
        if anomalies:
            logger.warning(f"Model anomalies detected:")
            for anomaly in anomalies:
                logger.warning(f"  - {anomaly}")
        
        return analysis
    
    def _convert_to_smoothnet_format(self, frames_data: List[Dict], video_width: int, video_height: int) -> Any:
        """
        Convert our keypoint format to SmoothNet input format
        
        Args:
            frames_data: List of frame dictionaries with keypoints
            video_width: Video width for normalization
            video_height: Video height for normalization
            
        Returns:
            torch.Tensor: Shape (1, num_frames, 99) for SmoothNet input - CORRECTED FORMAT
        """
        import torch
        
        num_frames = len(frames_data)
        num_keypoints = 33
        
        # CRITICAL FIX: Validate keypoint mapping compatibility - Issue #8
        if num_frames > 0:
            keypoint_mapping = self._validate_keypoint_mapping(0)  # Validate once for the sequence
            
            # Log overall compatibility summary
            compatible_count = len(keypoint_mapping.get('compatible_mappings', {}))
            total_keypoints = 33
            compatibility_ratio = compatible_count / total_keypoints
            
            logger.info(f"=== KEYPOINT COMPATIBILITY SUMMARY ===")
            logger.info(f"Compatible keypoint mappings: {compatible_count}/{total_keypoints} ({compatibility_ratio:.1%})")
            
            if keypoint_mapping.get('semantic_issues'):
                logger.warning(f"Keypoint semantic issues detected: {len(keypoint_mapping['semantic_issues'])}")
                for issue in keypoint_mapping['semantic_issues']:
                    logger.warning(f"  - {issue}")
        
        # Initialize tensor: (batch=1, temporal=num_frames, channels=99) - CORRECTED FORMAT for SmoothNet
        pose_tensor = torch.zeros(1, num_frames, num_keypoints * 3, dtype=torch.float32)
        
        for frame_idx, frame_data in enumerate(frames_data):
            keypoints = frame_data.get('keypoints', [])
            
            if len(keypoints) != num_keypoints:
                logger.warning(f"Frame {frame_idx}: Expected {num_keypoints} keypoints, got {len(keypoints)}")
                continue
            
            # Convert all keypoints to world coordinates first
            world_coords = []
            pixel_to_world_scale = 2000.0 / video_width  # 2000mm / video_width_pixels
            
            # Log first frame coordinates for debugging
            if frame_idx < 3:  # First 3 frames
                logger.info(f"=== FRAME {frame_idx} COORDINATE TRANSFORMATION ===")
                logger.info(f"Video dimensions: {video_width}x{video_height}, scale: {pixel_to_world_scale:.6f}")
            
            for keypoint in keypoints:
                world_x = (keypoint['x'] - video_width / 2) * pixel_to_world_scale   # Center and scale
                world_y = (keypoint['y'] - video_height / 2) * pixel_to_world_scale  # Center and scale  
                world_z = keypoint.get('z', 0.0) * pixel_to_world_scale              # Scale Z (MediaPipe depth)
                world_coords.append([world_x, world_y, world_z])
                
                # Log nose keypoint (index 0) transformation for each frame
                if len(world_coords) == 1 and frame_idx < 3:  # nose is first keypoint
                    logger.info(f"Nose (idx=0): pixel=({keypoint['x']:.1f}, {keypoint['y']:.1f}) -> world=({world_x:.1f}, {world_y:.1f})")
            
            # CRITICAL FIX: Validate world coordinates - Issue #7
            if world_coords:
                all_world_values = [coord for xyz in world_coords for coord in xyz]
                self._validate_coordinate_range(
                    all_world_values, "world coordinates", frame_idx,
                    expected_min=-2000.0, expected_max=2000.0
                )
                
                # CRITICAL FIX: Validate coordinate system compatibility - Issue #8
                coord_system_analysis = self._validate_coordinate_system(world_coords, frame_idx)
                
                # Store analysis for later use
                if not hasattr(self, 'coordinate_system_scores'):
                    self.coordinate_system_scores = []
                self.coordinate_system_scores.append(coord_system_analysis.get('3dpw_compatibility_score', 0.0))
            
            # CRITICAL FIX: Robust root position calculation with hip quality validation
            # This fixes Issue #5: Hip Keypoint Dependency
            root_x, root_y, root_z, root_method = self._calculate_robust_root_position(
                keypoints, world_coords, frame_idx, video_width, video_height, pixel_to_world_scale
            )
            
            # Store root position for next frame's fallback mechanism
            self.previous_root_position = (root_x, root_y, root_z)
            
            # Track root calculation methods for analysis
            if not hasattr(self, 'root_method_counts'):
                self.root_method_counts = {}
            self.root_method_counts[root_method] = self.root_method_counts.get(root_method, 0) + 1
            
            if frame_idx < 3:
                logger.info(f"Frame {frame_idx}: Root position: ({root_x:.1f}, {root_y:.1f}, {root_z:.1f}), method: {root_method}")
            
            # Log method distribution summary for first few frames
            if frame_idx == 9:  # After 10 frames
                logger.info(f"Root calculation method distribution (frames 0-9): {dict(self.root_method_counts)}")
                
            for kp_idx, (world_x, world_y, world_z) in enumerate(world_coords):
                # Convert to root-relative coordinates (common in 3D pose estimation)
                rel_x = world_x - root_x
                rel_y = world_y - root_y
                rel_z = world_z - root_z
                
                # Log nose transformation to root-relative
                if kp_idx == 0 and frame_idx < 3:  # nose keypoint
                    logger.info(f"Nose root-relative: world=({world_x:.1f}, {world_y:.1f}) -> rel=({rel_x:.1f}, {rel_y:.1f})")
                
                # Store in tensor: NEW FORMAT (batch, frame, joint_features)
                # Each frame contains [x0, y0, z0, x1, y1, z1, ..., x32, y32, z32] in root-relative world coordinates (mm)
                pose_tensor[0, frame_idx, kp_idx * 3] = rel_x      # x coordinate (mm, relative to pelvis)
                pose_tensor[0, frame_idx, kp_idx * 3 + 1] = rel_y  # y coordinate (mm, relative to pelvis)
                pose_tensor[0, frame_idx, kp_idx * 3 + 2] = rel_z  # z coordinate (mm, relative to pelvis)
        
        # CRITICAL FIX: Validate root-relative coordinates before normalization - Issue #7
        for frame_idx in range(num_frames):
            frame_coords = []
            for kp_idx in range(33):  # 33 keypoints
                frame_coords.extend([
                    float(pose_tensor[0, frame_idx, kp_idx * 3]),
                    float(pose_tensor[0, frame_idx, kp_idx * 3 + 1]),
                    float(pose_tensor[0, frame_idx, kp_idx * 3 + 2])
                ])
            
            self._validate_coordinate_range(
                frame_coords, "root-relative coordinates", frame_idx,
                expected_min=-1000.0, expected_max=1000.0
            )
        
        # CRITICAL FIX: Normalize coordinates for SmoothNet
        # SmoothNet was trained on 3DPW dataset with normalized coordinates in [-1, +1] range
        # Our coordinates are in mm with typical range [-500, +500] mm from root
        normalization_scale = 1000.0  # Scale to convert mm to normalized range
        
        # Log coordinate ranges before normalization
        coords_flat = pose_tensor.flatten()
        coord_min = coords_flat.min().item()
        coord_max = coords_flat.max().item()
        coord_range = coord_max - coord_min
        logger.info(f"Before normalization: range [{coord_min:.1f}, {coord_max:.1f}] mm, span: {coord_range:.1f} mm")
        
        # Normalize to [-1, +1] range expected by SmoothNet
        pose_tensor = pose_tensor / normalization_scale
        
        # Log coordinate ranges after normalization  
        coords_flat_norm = pose_tensor.flatten()
        coord_min_norm = coords_flat_norm.min().item()
        coord_max_norm = coords_flat_norm.max().item()
        coord_range_norm = coord_max_norm - coord_min_norm
        logger.info(f"After normalization: range [{coord_min_norm:.3f}, {coord_max_norm:.3f}], span: {coord_range_norm:.3f}")
        
        # CRITICAL FIX: Validate normalized coordinates for SmoothNet - Issue #7
        for frame_idx in range(num_frames):
            frame_coords = []
            for kp_idx in range(33):
                frame_coords.extend([
                    float(pose_tensor[0, frame_idx, kp_idx * 3]),
                    float(pose_tensor[0, frame_idx, kp_idx * 3 + 1]),
                    float(pose_tensor[0, frame_idx, kp_idx * 3 + 2])
                ])
            
            self._validate_coordinate_range(
                frame_coords, "normalized coordinates", frame_idx,
                expected_min=-2.0, expected_max=2.0  # Allow some margin beyond [-1, 1]
            )
        
        # Store normalization scale for later denormalization
        self.normalization_scale = normalization_scale
        
        # Note: Root positions are now calculated and stored during the main conversion loop above
        # They will be used in _convert_from_smoothnet_format() for smoothed root calculation
        
        # CHECKPOINT: Validate tensor format before returning
        pose_tensor = self._validate_tensor_format(
            pose_tensor,
            (1, num_frames, 99),
            "Final SmoothNet Format Conversion",
            auto_fix=True
        )
        
        return pose_tensor
    
    def _calculate_robust_root_position(self, keypoints: List[Dict], world_coords: List[List[float]], 
                                      frame_idx: int, video_width: int, video_height: int, 
                                      pixel_to_world_scale: float) -> Tuple[float, float, float, str]:
        """
        Calculate root position with robust hip quality validation and fallback mechanisms
        
        Args:
            keypoints: Raw keypoint data with confidence scores
            world_coords: Converted world coordinates
            frame_idx: Current frame index
            video_width: Video width for validation
            video_height: Video height for validation
            pixel_to_world_scale: Scale factor for coordinate conversion
            
        Returns:
            Tuple[float, float, float, str]: (root_x, root_y, root_z, method_used)
        """
        
        # Method 1: Hip-based root (preferred method)
        if len(keypoints) >= 25 and len(world_coords) >= 25:
            left_hip_kp = keypoints[23]   # left_hip keypoint
            right_hip_kp = keypoints[24]  # right_hip keypoint
            
            # Check hip confidence scores
            left_hip_confidence = left_hip_kp.get('score', left_hip_kp.get('confidence', 0.0))
            right_hip_confidence = right_hip_kp.get('score', right_hip_kp.get('confidence', 0.0))
            
            # Minimum confidence threshold
            min_confidence = 0.5
            
            # Check if both hips have sufficient confidence
            if left_hip_confidence >= min_confidence and right_hip_confidence >= min_confidence:
                left_hip = world_coords[23]
                right_hip = world_coords[24]
                
                # Validate hip positions are reasonable
                hip_distance = ((left_hip[0] - right_hip[0])**2 + (left_hip[1] - right_hip[1])**2)**0.5
                
                # Expected hip distance range: 100-800mm (reasonable human proportions)
                if 100.0 <= hip_distance <= 800.0:
                    root_x = (left_hip[0] + right_hip[0]) / 2
                    root_y = (left_hip[1] + right_hip[1]) / 2  
                    root_z = (left_hip[2] + right_hip[2]) / 2
                    
                    if frame_idx < 3:
                        logger.info(f"Frame {frame_idx}: Hip-based root, confidence=({left_hip_confidence:.2f}, {right_hip_confidence:.2f}), distance={hip_distance:.1f}mm")
                    
                    return root_x, root_y, root_z, "hip_primary"
        
        # Method 2: Shoulder-based fallback
        if len(keypoints) >= 13 and len(world_coords) >= 13:
            left_shoulder_kp = keypoints[11]   # left_shoulder
            right_shoulder_kp = keypoints[12]  # right_shoulder
            
            left_shoulder_confidence = left_shoulder_kp.get('score', left_shoulder_kp.get('confidence', 0.0))
            right_shoulder_confidence = right_shoulder_kp.get('score', right_shoulder_kp.get('confidence', 0.0))
            
            if left_shoulder_confidence >= min_confidence and right_shoulder_confidence >= min_confidence:
                left_shoulder = world_coords[11]
                right_shoulder = world_coords[12]
                
                # Validate shoulder positions
                shoulder_distance = ((left_shoulder[0] - right_shoulder[0])**2 + (left_shoulder[1] - right_shoulder[1])**2)**0.5
                
                # Expected shoulder distance range: 200-600mm
                if 200.0 <= shoulder_distance <= 600.0:
                    # Estimate root position below shoulders (typical torso length ~400mm)
                    torso_offset = 400.0  # mm
                    root_x = (left_shoulder[0] + right_shoulder[0]) / 2
                    root_y = (left_shoulder[1] + right_shoulder[1]) / 2 + torso_offset  # Below shoulders
                    root_z = (left_shoulder[2] + right_shoulder[2]) / 2
                    
                    if frame_idx < 3:
                        logger.info(f"Frame {frame_idx}: Shoulder-based fallback root, confidence=({left_shoulder_confidence:.2f}, {right_shoulder_confidence:.2f})")
                    
                    return root_x, root_y, root_z, "shoulder_fallback"
        
        # Method 3: Previous frame fallback
        if hasattr(self, 'previous_root_position') and self.previous_root_position is not None:
            root_x, root_y, root_z = self.previous_root_position
            
            if frame_idx < 3:
                logger.info(f"Frame {frame_idx}: Previous frame fallback root")
            
            return root_x, root_y, root_z, "previous_frame_fallback"
        
        # Method 4: Center-based emergency fallback
        # Place root at video center, reasonable depth
        root_x = 0.0  # Center X in world coordinates
        root_y = 0.0  # Center Y in world coordinates  
        root_z = 0.0  # Neutral Z depth
        
        if frame_idx < 3:
            logger.warning(f"Frame {frame_idx}: Emergency center-based root fallback - hip detection failed")
        
        return root_x, root_y, root_z, "emergency_center_fallback"
    
    def _convert_from_smoothnet_format(self, smoothed_tensor: Any, video_width: int, video_height: int, 
                                     original_frames: List[Dict]) -> List[Dict]:
        """
        Convert SmoothNet output back to our keypoint format
        
        Args:
            smoothed_tensor: Shape (1, num_frames, 99) from SmoothNet - CORRECTED FORMAT
            video_width: Video width for denormalization  
            video_height: Video height for denormalization
            original_frames: Original frame data for timestamps and metadata
            
        Returns:
            List[Dict]: Smoothed frames in our format
        """
        import torch
        
        # CHECKPOINT 3: Validate tensor format at start of conversion back
        num_frames = len(original_frames)
        smoothed_tensor = self._validate_tensor_format(
            smoothed_tensor,
            (1, num_frames, 99),
            "Pre-Conversion from SmoothNet Format",
            auto_fix=True
        )
        
        # Validate keypoint layout with schema
        schema = self._define_keypoint_schema() 
        self._validate_keypoint_layout(smoothed_tensor, schema, "Post-SmoothNet")
        
        # CRITICAL FIX: Denormalize coordinates from SmoothNet
        # SmoothNet outputs normalized coordinates in [-1, +1] range
        # We need to convert back to mm coordinates before reconstruction
        normalization_scale = getattr(self, 'normalization_scale', 1000.0)
        
        # Log coordinate ranges before denormalization
        coords_flat = smoothed_tensor.flatten()
        coord_min = coords_flat.min().item()
        coord_max = coords_flat.max().item()
        coord_range = coord_max - coord_min
        logger.info(f"SmoothNet output range: [{coord_min:.3f}, {coord_max:.3f}], span: {coord_range:.3f}")
        
        # Denormalize back to mm coordinates
        smoothed_tensor = smoothed_tensor * normalization_scale
        
        # Log coordinate ranges after denormalization
        coords_flat_denorm = smoothed_tensor.flatten()
        coord_min_denorm = coords_flat_denorm.min().item()
        coord_max_denorm = coords_flat_denorm.max().item()
        coord_range_denorm = coord_max_denorm - coord_min_denorm
        logger.info(f"After denormalization: range [{coord_min_denorm:.1f}, {coord_max_denorm:.1f}] mm, span: {coord_range_denorm:.1f} mm")
        
        # CRITICAL FIX: Validate denormalized SmoothNet output - Issue #7
        for frame_idx in range(smoothed_tensor.shape[1]):  # Frames are in dimension 1
            frame_coords = []
            for kp_idx in range(33):
                frame_coords.extend([
                    float(smoothed_tensor[0, frame_idx, kp_idx * 3]),
                    float(smoothed_tensor[0, frame_idx, kp_idx * 3 + 1]),
                    float(smoothed_tensor[0, frame_idx, kp_idx * 3 + 2])
                ])
            
            self._validate_coordinate_range(
                frame_coords, "SmoothNet output (denormalized)", frame_idx,
                expected_min=-1000.0, expected_max=1000.0
            )
        
        num_keypoints = 33
        num_frames = smoothed_tensor.shape[1]  # CORRECTED: frames are now in dimension 1
        
        # BlazePose keypoint names (33 keypoints)
        keypoint_names = [
            'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
            'right_eye_inner', 'right_eye', 'right_eye_outer',
            'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
            'left_index', 'right_index', 'left_thumb', 'right_thumb',
            'left_hip', 'right_hip', 'left_knee', 'right_knee',
            'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
            'left_foot_index', 'right_foot_index'
        ]
        
        smoothed_frames = []
        
        for frame_idx in range(num_frames):
            # Preserve original frame metadata
            frame_data = {
                'frameNumber': original_frames[frame_idx]['frameNumber'],
                'timestamp': original_frames[frame_idx]['timestamp'],
                'keypoints': []
            }
            
            # CRITICAL FIX: Calculate smoothed root position from smoothed hip keypoints
            # This ensures root position benefits from temporal smoothing
            pixel_to_world_scale = 2000.0 / video_width
            
            # Extract smoothed hip keypoints (indices 23, 24) from SmoothNet output
            left_hip_idx = 23
            right_hip_idx = 24
            
            # Get smoothed root-relative hip coordinates
            left_hip_rel_x = float(smoothed_tensor[0, frame_idx, left_hip_idx * 3])
            left_hip_rel_y = float(smoothed_tensor[0, frame_idx, left_hip_idx * 3 + 1])
            left_hip_rel_z = float(smoothed_tensor[0, frame_idx, left_hip_idx * 3 + 2])
            
            right_hip_rel_x = float(smoothed_tensor[0, frame_idx, right_hip_idx * 3])
            right_hip_rel_y = float(smoothed_tensor[0, frame_idx, right_hip_idx * 3 + 1])
            right_hip_rel_z = float(smoothed_tensor[0, frame_idx, right_hip_idx * 3 + 2])
            
            # Calculate smoothed root position from smoothed hip coordinates
            # Since hip coordinates are root-relative, we need to solve for root position
            # If left_hip_world = left_hip_rel + root and right_hip_world = right_hip_rel + root
            # Then root = (left_hip_world + right_hip_world) / 2
            # But since left_hip_world = left_hip_rel + root, we get:
            # root = ((left_hip_rel + root) + (right_hip_rel + root)) / 2
            # This simplifies to: root = (left_hip_rel + right_hip_rel) / 2 + root
            # So: 0 = (left_hip_rel + right_hip_rel) / 2
            # This means root = 0 for root-relative coordinates, but we need absolute coordinates.
            
            # Better approach: Get original root position but apply smoothing to it
            orig_keypoints = original_frames[frame_idx]['keypoints']
            
            if len(orig_keypoints) >= 25:  # Ensure we have hip keypoints
                # Get original root as fallback
                left_hip_orig = orig_keypoints[23]   # left_hip
                right_hip_orig = orig_keypoints[24]  # right_hip
                
                left_hip_world_x_orig = (left_hip_orig['x'] - video_width / 2) * pixel_to_world_scale
                left_hip_world_y_orig = (left_hip_orig['y'] - video_height / 2) * pixel_to_world_scale
                left_hip_world_z_orig = left_hip_orig.get('z', 0.0) * pixel_to_world_scale
                
                right_hip_world_x_orig = (right_hip_orig['x'] - video_width / 2) * pixel_to_world_scale
                right_hip_world_y_orig = (right_hip_orig['y'] - video_height / 2) * pixel_to_world_scale
                right_hip_world_z_orig = right_hip_orig.get('z', 0.0) * pixel_to_world_scale
                
                # Calculate original root position 
                root_x_orig = (left_hip_world_x_orig + right_hip_world_x_orig) / 2
                root_y_orig = (left_hip_world_y_orig + right_hip_world_y_orig) / 2
                root_z_orig = (left_hip_world_z_orig + right_hip_world_z_orig) / 2
                
                # Calculate smoothed absolute hip positions
                left_hip_world_x_smooth = left_hip_rel_x + root_x_orig
                left_hip_world_y_smooth = left_hip_rel_y + root_y_orig
                left_hip_world_z_smooth = left_hip_rel_z + root_z_orig
                
                right_hip_world_x_smooth = right_hip_rel_x + root_x_orig
                right_hip_world_y_smooth = right_hip_rel_y + root_y_orig
                right_hip_world_z_smooth = right_hip_rel_z + root_z_orig
                
                # Calculate smoothed root position from smoothed absolute hip positions
                root_x = (left_hip_world_x_smooth + right_hip_world_x_smooth) / 2
                root_y = (left_hip_world_y_smooth + right_hip_world_y_smooth) / 2
                root_z = (left_hip_world_z_smooth + right_hip_world_z_smooth) / 2
                
                # Log root position comparison for first 3 frames
                if frame_idx < 3:
                    logger.info(f"=== FRAME {frame_idx} SMOOTHNET OUTPUT CONVERSION ===")
                    logger.info(f"Original root: ({root_x_orig:.1f}, {root_y_orig:.1f}, {root_z_orig:.1f})")
                    logger.info(f"Smoothed root: ({root_x:.1f}, {root_y:.1f}, {root_z:.1f})")
                    root_diff = ((root_x - root_x_orig)**2 + (root_y - root_y_orig)**2)**0.5
                    logger.info(f"Root position difference: {root_diff:.1f} mm")
            else:
                # Fallback: no root adjustment
                root_x = root_y = root_z = 0.0
            
            # Extract smoothed keypoints
            keypoints = []
            for kp_idx in range(num_keypoints):
                # Get root-relative coordinates from tensor - CORRECTED FORMAT (batch, frame, features)
                rel_x = float(smoothed_tensor[0, frame_idx, kp_idx * 3])
                rel_y = float(smoothed_tensor[0, frame_idx, kp_idx * 3 + 1])
                rel_z = float(smoothed_tensor[0, frame_idx, kp_idx * 3 + 2])
                
                # Convert back to absolute world coordinates
                world_x = rel_x + root_x
                world_y = rel_y + root_y
                world_z = rel_z + root_z
                
                # Convert back to pixel coordinates from world coordinates (mm)
                pixel_x = (world_x / pixel_to_world_scale) + video_width / 2   # Uncenter and unscale
                pixel_y = (world_y / pixel_to_world_scale) + video_height / 2  # Uncenter and unscale
                pixel_z = world_z / pixel_to_world_scale                       # Unscale Z
                
                # Log nose keypoint transformation for first 3 frames
                if kp_idx == 0 and frame_idx < 3:  # nose keypoint
                    logger.info(f"Nose SmoothNet: rel=({rel_x:.1f}, {rel_y:.1f}) -> world=({world_x:.1f}, {world_y:.1f}) -> pixel=({pixel_x:.1f}, {pixel_y:.1f})")
                
                # Clamp coordinates to valid pixel range after conversion
                pixel_x = max(0.0, min(pixel_x, video_width))
                pixel_y = max(0.0, min(pixel_y, video_height))
                # Note: Z coordinate can be negative (MediaPipe depth), so don't clamp
                
                # Get original score from original data
                original_kp = original_frames[frame_idx]['keypoints'][kp_idx] if kp_idx < len(original_frames[frame_idx]['keypoints']) else {}
                
                keypoint = {
                    'x': pixel_x,
                    'y': pixel_y,
                    'z': pixel_z,
                    'score': original_kp.get('score', 0.8),  # Keep original score or default to 0.8
                    'name': keypoint_names[kp_idx]
                }
                keypoints.append(keypoint)
            
            # CRITICAL FIX: Validate final reconstructed pixel coordinates - Issue #7
            pixel_coords = [(kp['x'], kp['y']) for kp in keypoints]
            all_pixel_values = [coord for xy in pixel_coords for coord in xy]
            
            self._validate_coordinate_range(
                all_pixel_values, "final pixel coordinates", frame_idx,
                expected_min=0.0, expected_max=max(video_width, video_height)
            )
            
            frame_data['keypoints'] = keypoints
            smoothed_frames.append(frame_data)
        
        # CHECKPOINT 4: Final validation of converted frames
        logger.info(f"=== FINAL FORMAT VALIDATION ===")
        logger.info(f"Converted {len(smoothed_frames)} frames back to keypoint format")
        
        # Quick validation of first frame structure
        if smoothed_frames:
            first_frame = smoothed_frames[0]
            if 'keypoints' in first_frame:
                num_keypoints = len(first_frame['keypoints'])
                if num_keypoints == 33:
                    logger.info(f"✅ Frame structure valid: {num_keypoints} keypoints per frame")
                else:
                    logger.warning(f"⚠️ Unexpected keypoint count: {num_keypoints} (expected 33)")
            else:
                logger.error(f"❌ Missing keypoints in frame structure")
        
        return smoothed_frames
    
    def _validate_coordinates(self, frames_data: List[Dict], video_width: int, video_height: int, 
                            stage: str = "pre-smoothing") -> Dict[str, Any]:
        """
        Validate coordinate ranges and detect anomalies
        
        Args:
            frames_data: List of frame dictionaries with keypoints
            video_width: Video width for validation
            video_height: Video height for validation
            stage: Description of when validation is performed
            
        Returns:
            Dict with validation results and statistics
        """
        import numpy as np
        
        logger.info(f"\n=== Coordinate Validation ({stage}) ===")
        
        validation_results = {
            'stage': stage,
            'total_frames': len(frames_data),
            'total_keypoints': 0,
            'out_of_bounds': 0,
            'zero_coordinates': 0,
            'low_confidence': 0,
            'coordinate_ranges': {
                'x': {'min': float('inf'), 'max': float('-inf')},
                'y': {'min': float('inf'), 'max': float('-inf')},
                'z': {'min': float('inf'), 'max': float('-inf')}
            },
            'center_of_mass': [],
            'anomalies': []
        }
        
        # Key keypoint indices for validation
        nose_idx = 0
        left_hip_idx = 23
        right_hip_idx = 24
        
        for frame_idx, frame_data in enumerate(frames_data):
            keypoints = frame_data.get('keypoints', [])
            validation_results['total_keypoints'] += len(keypoints)
            
            # Calculate center of mass for this frame
            com_x, com_y, com_z = 0, 0, 0
            valid_kp_count = 0
            
            for kp_idx, kp in enumerate(keypoints):
                x, y, z = kp['x'], kp['y'], kp['z']
                score = kp.get('score', 0)
                
                # Update coordinate ranges
                validation_results['coordinate_ranges']['x']['min'] = min(validation_results['coordinate_ranges']['x']['min'], x)
                validation_results['coordinate_ranges']['x']['max'] = max(validation_results['coordinate_ranges']['x']['max'], x)
                validation_results['coordinate_ranges']['y']['min'] = min(validation_results['coordinate_ranges']['y']['min'], y)
                validation_results['coordinate_ranges']['y']['max'] = max(validation_results['coordinate_ranges']['y']['max'], y)
                validation_results['coordinate_ranges']['z']['min'] = min(validation_results['coordinate_ranges']['z']['min'], z)
                validation_results['coordinate_ranges']['z']['max'] = max(validation_results['coordinate_ranges']['z']['max'], z)
                
                # Check for out of bounds coordinates
                if x < 0 or x > video_width or y < 0 or y > video_height:
                    validation_results['out_of_bounds'] += 1
                    if validation_results['out_of_bounds'] <= 5:  # Log first 5
                        validation_results['anomalies'].append({
                            'type': 'out_of_bounds',
                            'frame': frame_idx,
                            'keypoint': kp['name'],
                            'coordinates': (x, y, z)
                        })
                
                # Check for zero coordinates (common error)
                if x == 0 and y == 0:
                    validation_results['zero_coordinates'] += 1
                    if validation_results['zero_coordinates'] <= 5:  # Log first 5
                        validation_results['anomalies'].append({
                            'type': 'zero_coordinates',
                            'frame': frame_idx,
                            'keypoint': kp['name'],
                            'score': score
                        })
                
                # Check for low confidence scores
                if score < 0.5:
                    validation_results['low_confidence'] += 1
                
                # Accumulate for center of mass
                if score > 0.5:  # Only use confident keypoints
                    com_x += x
                    com_y += y
                    com_z += z
                    valid_kp_count += 1
            
            # Calculate frame center of mass
            if valid_kp_count > 0:
                com_x /= valid_kp_count
                com_y /= valid_kp_count
                com_z /= valid_kp_count
                validation_results['center_of_mass'].append((com_x, com_y, com_z))
            
            # Special validation for key keypoints (first frame only for efficiency)
            if frame_idx == 0:
                # Check nose position (should be in upper portion of frame)
                if nose_idx < len(keypoints):
                    nose = keypoints[nose_idx]
                    if nose['y'] > video_height * 0.7:  # Nose in bottom 30% is unusual
                        logger.warning(f"Nose position seems low: y={nose['y']:.1f} (frame height: {video_height})")
                
                # Check hip positions (should be roughly centered)
                if left_hip_idx < len(keypoints) and right_hip_idx < len(keypoints):
                    left_hip = keypoints[left_hip_idx]
                    right_hip = keypoints[right_hip_idx]
                    hip_center_x = (left_hip['x'] + right_hip['x']) / 2
                    hip_center_y = (left_hip['y'] + right_hip['y']) / 2
                    
                    # Expected hip center around middle of frame
                    expected_x = video_width / 2
                    expected_y = video_height / 2
                    
                    x_deviation = abs(hip_center_x - expected_x) / video_width
                    y_deviation = abs(hip_center_y - expected_y) / video_height
                    
                    if x_deviation > 0.3 or y_deviation > 0.3:
                        logger.warning(f"Hip center far from expected position: ({hip_center_x:.1f}, {hip_center_y:.1f}) "
                                     f"vs expected ({expected_x:.1f}, {expected_y:.1f})")
        
        # Calculate statistics
        if validation_results['center_of_mass']:
            com_array = np.array(validation_results['center_of_mass'])
            com_mean = com_array.mean(axis=0)
            com_std = com_array.std(axis=0)
            
            logger.info(f"Center of mass (mean): X={com_mean[0]:.1f}, Y={com_mean[1]:.1f}, Z={com_mean[2]:.1f}")
            logger.info(f"Center of mass (std): X={com_std[0]:.1f}, Y={com_std[1]:.1f}, Z={com_std[2]:.1f}")
            
            # Check if center of mass is reasonable (should be near center of frame)
            expected_com_x = video_width / 2
            expected_com_y = video_height / 2
            com_deviation_x = abs(com_mean[0] - expected_com_x) / video_width
            com_deviation_y = abs(com_mean[1] - expected_com_y) / video_height
            
            if com_deviation_x > 0.2 or com_deviation_y > 0.2:
                logger.warning(f"⚠️  Center of mass significantly off-center: "
                             f"({com_mean[0]:.1f}, {com_mean[1]:.1f}) vs expected ({expected_com_x:.1f}, {expected_com_y:.1f})")
        
        # Log validation summary
        logger.info(f"Coordinate ranges - X: [{validation_results['coordinate_ranges']['x']['min']:.1f}, "
                   f"{validation_results['coordinate_ranges']['x']['max']:.1f}]")
        logger.info(f"Coordinate ranges - Y: [{validation_results['coordinate_ranges']['y']['min']:.1f}, "
                   f"{validation_results['coordinate_ranges']['y']['max']:.1f}]")
        logger.info(f"Coordinate ranges - Z: [{validation_results['coordinate_ranges']['z']['min']:.1f}, "
                   f"{validation_results['coordinate_ranges']['z']['max']:.1f}]")
        
        if validation_results['out_of_bounds'] > 0:
            logger.warning(f"⚠️  Found {validation_results['out_of_bounds']} out-of-bounds coordinates")
        if validation_results['zero_coordinates'] > 0:
            logger.warning(f"⚠️  Found {validation_results['zero_coordinates']} zero coordinates")
        if validation_results['low_confidence'] > 0:
            logger.info(f"Found {validation_results['low_confidence']} low-confidence keypoints (score < 0.5)")
        
        # Log first few anomalies
        for anomaly in validation_results['anomalies'][:3]:
            logger.warning(f"Anomaly: {anomaly}")
        
        logger.info(f"=== Validation Complete ({stage}) ===\n")
        
        return validation_results
    
    def _load_smoothnet_config(self, config_name='pw3d_spin_3D'):
        """Load SmoothNet configuration from S3"""
        import yaml
        import boto3
        from easydict import EasyDict
        
        try:
            # Download config from S3
            import os
            s3_client = boto3.client('s3')
            config_path = f'/tmp/smoothnet_{config_name}.yaml'
            s3_key = f'model-weights/{config_name}.yaml'
            
            # Check if config already exists locally
            if os.path.exists(config_path):
                logger.info(f"Using cached SmoothNet config from {config_path}")
            else:
                logger.info(f"Downloading SmoothNet config from s3://maxwattz-videos/{s3_key}")
                s3_client.download_file('maxwattz-videos', s3_key, config_path)
            
            # Load YAML config
            with open(config_path, 'r') as f:
                config = EasyDict(yaml.safe_load(f))
            
            logger.info(f"✅ Loaded SmoothNet config with architecture:")
            logger.info(f"  - Hidden size: {config.MODEL.HIDDEN_SIZE}")
            logger.info(f"  - Residual hidden size: {config.MODEL.RES_HIDDEN_SIZE}")
            logger.info(f"  - Number of blocks: {config.MODEL.NUM_BLOCK}")
            logger.info(f"  - Dropout: {config.MODEL.DROPOUT}")
            logger.info(f"  - Slide window size: {config.MODEL.SLIDE_WINDOW_SIZE}")
            
            return config
            
        except Exception as e:
            logger.warning(f"Failed to load config: {e}")
            logger.warning("Using default SmoothNet architecture")
            # Return default config
            return EasyDict({
                'MODEL': {
                    'HIDDEN_SIZE': 512,
                    'RES_HIDDEN_SIZE': 256,
                    'NUM_BLOCK': 3,
                    'DROPOUT': 0.5,
                    'SLIDE_WINDOW_SIZE': 32
                }
            })
    
    def _load_smoothnet_weights(self, model, model_name='3dpw_spin_3d'):
        """Load pre-trained SmoothNet weights from S3"""
        import torch
        import boto3
        
        try:
            # Download weights from S3 - using correct .pth.tar format
            import os
            s3_client = boto3.client('s3')
            weight_path = f'/tmp/smoothnet_{model_name}_checkpoint_32.pth.tar'
            s3_key = f'model-weights/smoothnet_{model_name}_checkpoint_32.pth.tar'
            
            # Check if weights already exist locally
            if os.path.exists(weight_path):
                logger.info(f"Using cached SmoothNet weights from {weight_path}")
            else:
                logger.info(f"Downloading SmoothNet weights from s3://maxwattz-videos/{s3_key}")
                s3_client.download_file('maxwattz-videos', s3_key, weight_path)
            
            # Load weights (use same device as model)
            device = next(model.parameters()).device
            checkpoint = torch.load(weight_path, map_location=device)
            
            # Handle different checkpoint formats
            if isinstance(checkpoint, dict):
                logger.info(f"Checkpoint keys: {list(checkpoint.keys())}")
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']
                elif 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                else:
                    state_dict = checkpoint
            else:
                # Direct state dict
                state_dict = checkpoint
            
            # Log model architecture info
            logger.info(f"Model has {sum(p.numel() for p in model.parameters())} parameters")
            logger.info(f"State dict has {len(state_dict)} keys")
            
            # Load state dict with detailed error handling
            incompatible_keys = model.load_state_dict(state_dict, strict=False)
            
            if incompatible_keys.missing_keys:
                logger.warning(f"Missing keys in state dict: {incompatible_keys.missing_keys[:5]}...")
            if incompatible_keys.unexpected_keys:
                logger.warning(f"Unexpected keys in state dict: {incompatible_keys.unexpected_keys[:5]}...")
            
            logger.info("✅ Successfully loaded pre-trained SmoothNet weights")
            
            return model
            
        except Exception as e:
            logger.warning(f"Failed to load pre-trained weights: {e}")
            logger.warning("Using untrained SmoothNet model (random initialization)")
            return model
    
    def _create_smoothnet_model(self, window_size=16, output_size=16, hidden_size=512, 
                              res_hidden_size=256, num_blocks=3, dropout=0.5):
        """Create SmoothNet model inside Modal where torch is available"""
        import torch
        import torch.nn as nn
        
        class SmoothNet(nn.Module):
            def __init__(self):
                super(SmoothNet, self).__init__()
                
                self.window_size = window_size
                self.output_size = output_size
                
                # Simple temporal smoothing network for 3D coordinates
                self.input_layer = nn.Linear(99, hidden_size)  # 33 keypoints * 3 coordinates
                
                # Residual blocks for temporal modeling
                self.residual_blocks = nn.ModuleList([
                    nn.Sequential(
                        nn.Linear(hidden_size, res_hidden_size),
                        nn.ReLU(),
                        nn.Dropout(dropout),
                        nn.Linear(res_hidden_size, hidden_size),
                        nn.ReLU()
                    ) for _ in range(num_blocks)
                ])
                
                # Temporal convolution for smoothing
                self.temporal_conv = nn.Conv1d(hidden_size, hidden_size, 
                                             kernel_size=3, padding=1)
                
                # Output layer for 3D coordinates
                self.output_layer = nn.Linear(hidden_size, 99)  # 33 keypoints * 3 coordinates
                
                # Activation
                self.relu = nn.ReLU()
                self.dropout = nn.Dropout(dropout)
            
            def forward(self, x):
                # CORRECTED: Input is now (batch, seq_len, channels)
                batch_size, seq_len, channels = x.shape
                
                # No transpose needed - already in correct format
                
                # Process each timestep
                x = self.input_layer(x)
                
                # Apply residual blocks
                for block in self.residual_blocks:
                    residual = x
                    x = block(x)
                    x = x + residual
                
                # Transpose for temporal convolution
                x = x.transpose(1, 2)
                
                # Apply temporal smoothing
                x = self.temporal_conv(x)
                x = self.relu(x)
                x = self.dropout(x)
                
                # Transpose back
                x = x.transpose(1, 2)
                
                # Output layer
                x = self.output_layer(x)
                
                # Take only the desired output length
                if self.output_size < seq_len:
                    start_idx = (seq_len - self.output_size) // 2
                    x = x[:, start_idx:start_idx + self.output_size, :]
                
                # Return in same format as input: (batch, seq_len, channels)
                # No transpose needed - maintaining (batch, seq, channels) format
                
                return x
        
        return SmoothNet()
    
    def _apply_smoothnet(self, frames_data: List[Dict], video_width: int, video_height: int) -> List[Dict]:
        """
        Apply SmoothNet temporal smoothing to pose sequence
        
        Args:
            frames_data: List of frame dictionaries with raw keypoints
            video_width: Video width for coordinate conversion
            video_height: Video height for coordinate conversion
            
        Returns:
            List[Dict]: Smoothed frames in our format
        """
        import torch
        
        try:
            # Validate input coordinates before smoothing
            pre_validation = self._validate_coordinates(frames_data, video_width, video_height, "pre-smoothing")
            
            # Log original coordinates before SmoothNet for comparison
            if len(frames_data) > 0:
                frame0 = frames_data[0]
                if 'keypoints' in frame0 and len(frame0['keypoints']) > 0:
                    nose_orig = frame0['keypoints'][0]  # nose keypoint
                    logger.info(f"BEFORE SmoothNet - Frame 0 nose: ({nose_orig['x']:.1f}, {nose_orig['y']:.1f})")
            
            # Convert to SmoothNet format
            pose_tensor = self._convert_to_smoothnet_format(frames_data, video_width, video_height)
            
            # CHECKPOINT 1: Validate tensor after conversion to SmoothNet format
            pose_tensor = self._validate_tensor_format(
                pose_tensor,
                (1, -1, 99),  # -1 for variable frame count
                "Post-Conversion to SmoothNet Format",
                auto_fix=True
            )
            
            # Validate keypoint layout with schema
            schema = self._define_keypoint_schema()
            self._validate_keypoint_layout(pose_tensor, schema, "Pre-SmoothNet")
            
            # DEBUG: Detailed tensor analysis for first 3 frames
            self._debug_tensor_format(pose_tensor, "Input to SmoothNet", verbose=True)
            
            num_frames = pose_tensor.shape[1]  # CORRECTED: frames are now in dimension 1
            
            logger.info(f"Applying SmoothNet to {num_frames} frames")
            logger.info(f"Input tensor shape: {pose_tensor.shape} (expected: (1, {num_frames}, 99))")
            
            # Log coordinate range to verify world coordinate conversion
            first_frame_coords = pose_tensor[0, 0, :9]  # First 3 keypoints (x,y,z each)
            logger.info(f"Sample world coordinates (first 3 keypoints): {first_frame_coords.tolist()}")
            logger.info(f"Root-relative coordinate range - X: [{float(pose_tensor[:,:,::3].min()):.1f}, {float(pose_tensor[:,:,::3].max()):.1f}] mm")
            logger.info(f"Root-relative coordinate range - Y: [{float(pose_tensor[:,:,1::3].min()):.1f}, {float(pose_tensor[:,:,1::3].max()):.1f}] mm")
            logger.info(f"Root-relative coordinate range - Z: [{float(pose_tensor[:,:,2::3].min()):.1f}, {float(pose_tensor[:,:,2::3].max()):.1f}] mm")
            
            # Load SmoothNet configuration
            config = self._load_smoothnet_config('pw3d_spin_3D')
            
            # Initialize SmoothNet model (using 32-frame window - SmoothNet standard)
            window_size = 32  # Default to 32-frame window as per documentation
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            logger.info(f"SmoothNet using device: {device} (CUDA available: {torch.cuda.is_available()})")
            logger.info(f"Window size: {window_size} frames (stride: {window_size // 2})")
            
            # Check if video is long enough for proper 32-frame processing
            if num_frames < window_size:
                logger.warning(f"Video has only {num_frames} frames, less than window size {window_size}")
                logger.info("Will use frame padding for short video")
                # For very short videos, consider reducing window size for better performance
                if num_frames < 16:
                    logger.info(f"Video too short for optimal smoothing, will process with padding")
            else:
                num_windows = (num_frames - 1) // (window_size // 2) + 1
                logger.info(f"Will process {num_windows} overlapping windows of {window_size} frames each")
                logger.info(f"Expected smoothing quality: HIGH (32-frame standard window)")
            
            # Create SmoothNet model using official config parameters
            model = self._create_smoothnet_model(
                window_size=window_size,
                output_size=window_size,
                hidden_size=config.MODEL.HIDDEN_SIZE,
                res_hidden_size=config.MODEL.RES_HIDDEN_SIZE,
                num_blocks=config.MODEL.NUM_BLOCK,
                dropout=config.MODEL.DROPOUT
            )
            
            # Explicitly move model to GPU
            model = model.to(device)
            logger.info(f"Model moved to {device}")
            
            # Load pre-trained weights from S3
            model = self._load_smoothnet_weights(model, model_name='3dpw_spin_3d')
            
            model.eval()
            
            # Process sequence with sliding windows
            smoothed_tensor = self._process_sliding_windows(pose_tensor, model, window_size, device)
            logger.info(f"Output tensor shape: {smoothed_tensor.shape} (expected: (1, {num_frames}, 99))")
            
            # CHECKPOINT 2: Validate tensor after SmoothNet processing
            smoothed_tensor = self._validate_tensor_format(
                smoothed_tensor,
                (1, num_frames, 99),
                "Post-SmoothNet Processing",
                auto_fix=True
            )
            
            # DEBUG: Compare input vs output tensors
            self._log_tensor_evolution({
                'Input': pose_tensor,
                'Output': smoothed_tensor
            }, "SmoothNet Processing")
            
            # CRITICAL FIX: Analyze model behavior for compatibility issues - Issue #8
            model_behavior_analysis = self._analyze_model_behavior(pose_tensor, smoothed_tensor)
            
            # Store behavior analysis for overall assessment
            if not hasattr(self, 'model_behavior_scores'):
                self.model_behavior_scores = []
            self.model_behavior_scores.append(model_behavior_analysis['model_health_score'])
            
            # Convert back to our format
            smoothed_frames = self._convert_from_smoothnet_format(smoothed_tensor, video_width, video_height, frames_data)
            
            # Log comparison after SmoothNet processing
            if len(smoothed_frames) > 0:
                frame0_smoothed = smoothed_frames[0]
                if 'keypoints' in frame0_smoothed and len(frame0_smoothed['keypoints']) > 0:
                    nose_smoothed = frame0_smoothed['keypoints'][0]  # nose keypoint
                    logger.info(f"AFTER SmoothNet - Frame 0 nose: ({nose_smoothed['x']:.1f}, {nose_smoothed['y']:.1f})")
                    
                    # Calculate difference from original
                    if len(frames_data) > 0 and 'keypoints' in frames_data[0] and len(frames_data[0]['keypoints']) > 0:
                        nose_orig = frames_data[0]['keypoints'][0]
                        diff_x = nose_smoothed['x'] - nose_orig['x']
                        diff_y = nose_smoothed['y'] - nose_orig['y']
                        logger.info(f"SmoothNet difference: ({diff_x:.1f}, {diff_y:.1f}) pixels")
            
            # Validate output coordinates after smoothing
            post_validation = self._validate_coordinates(smoothed_frames, video_width, video_height, "post-smoothing")
            
            # Compare pre/post validation results
            logger.info("\n=== Smoothing Impact Summary ===")
            logger.info(f"Out-of-bounds reduction: {pre_validation['out_of_bounds']} → {post_validation['out_of_bounds']}")
            logger.info(f"Zero coordinates reduction: {pre_validation['zero_coordinates']} → {post_validation['zero_coordinates']}")
            logger.info(f"Low confidence reduction: {pre_validation['low_confidence']} → {post_validation['low_confidence']}")
            
            # Check if coordinates moved to expected center position
            if post_validation['center_of_mass']:
                import numpy as np
                post_com = np.array(post_validation['center_of_mass']).mean(axis=0)
                expected_x = video_width / 2
                expected_y = video_height / 2
                
                distance_from_center = np.sqrt((post_com[0] - expected_x)**2 + (post_com[1] - expected_y)**2)
                logger.info(f"Final center of mass distance from frame center: {distance_from_center:.1f} pixels")
                
                if distance_from_center < 100:  # Within 100 pixels of center
                    logger.info("✅ Coordinates successfully centered after smoothing")
                else:
                    logger.warning(f"⚠️  Coordinates still off-center after smoothing: ({post_com[0]:.1f}, {post_com[1]:.1f})")
            
            # CRITICAL FIX: Final architecture compatibility assessment - Issue #8
            if hasattr(self, 'coordinate_system_scores') and hasattr(self, 'model_behavior_scores'):
                avg_coord_compatibility = sum(self.coordinate_system_scores) / len(self.coordinate_system_scores)
                avg_model_health = sum(self.model_behavior_scores) / len(self.model_behavior_scores)
                
                overall_compatibility = (avg_coord_compatibility + avg_model_health) / 2
                
                logger.info(f"=== FINAL ARCHITECTURE COMPATIBILITY ASSESSMENT ===")
                logger.info(f"Average coordinate system compatibility: {avg_coord_compatibility:.1%}")
                logger.info(f"Average model behavior health: {avg_model_health:.1%}")
                logger.info(f"Overall architecture compatibility score: {overall_compatibility:.1%}")
                
                if overall_compatibility < 0.7:
                    logger.warning(f"🔶 LOW COMPATIBILITY ({overall_compatibility:.1%}) - BlazePose↔3DPW architecture mismatch detected")
                    logger.warning("Consider reviewing keypoint mappings and coordinate system assumptions")
                elif overall_compatibility < 0.85:
                    logger.info(f"⚠️  MODERATE COMPATIBILITY ({overall_compatibility:.1%}) - some architecture differences detected")
                else:
                    logger.info(f"✅ HIGH COMPATIBILITY ({overall_compatibility:.1%}) - good BlazePose↔3DPW alignment")
            
            logger.info("SmoothNet processing completed successfully")
            return smoothed_frames
            
        except Exception as e:
            logger.error(f"SmoothNet processing failed: {e}")
            logger.info("Falling back to raw keypoints without smoothing")
            return frames_data
    
    def _create_symmetric_boundary_extension(self, pose_tensor: Any, window_size: int, device, position: str) -> Any:
        """
        Create symmetric boundary extension for consistent edge frame coverage
        
        Args:
            pose_tensor: Input tensor (1, num_frames, 99)
            window_size: Window size for processing  
            device: Torch device
            position: 'start' for beginning, 'end' for ending
            
        Returns:
            torch.Tensor: Extended window with symmetric boundary padding
        """
        import torch
        
        num_frames = pose_tensor.shape[1]
        half_window = window_size // 2
        
        # Create extended window tensor
        extended_window = torch.zeros(1, window_size, 99, dtype=torch.float32).to(device)
        
        if position == 'start':
            # START: Create reflected window [-half_window to half_window-1]
            # Real frames [0 to half_window-1] go in second half
            extended_window[:, half_window:, :] = pose_tensor[:, :half_window, :]
            
            # Reflected frames in first half for symmetry
            for i in range(half_window):
                # Symmetric reflection: pos -1 → frame 1, pos -2 → frame 2, etc.
                reflect_idx = half_window - i - 1
                source_frame = min(reflect_idx, num_frames - 1)  # Clamp to available frames
                extended_window[:, i, :] = pose_tensor[:, source_frame, :]
                
        elif position == 'end':
            # END: Create reflected window [last_half_window to last+half_window-1]
            start_frame = max(0, num_frames - half_window)
            actual_frames = num_frames - start_frame
            
            # Real frames in first half
            extended_window[:, :actual_frames, :] = pose_tensor[:, start_frame:, :]
            
            # Reflected frames in second half for symmetry
            for i in range(half_window):
                # Symmetric reflection around last frame
                reflect_offset = i + 1
                source_idx = max(0, num_frames - 1 - reflect_offset)
                target_idx = actual_frames + i
                if target_idx < window_size:
                    extended_window[:, target_idx, :] = pose_tensor[:, source_idx, :]
        
        return extended_window
    
    def _calculate_boundary_balanced_weights(self, frame_positions: list, window_size: int, device, is_boundary_window: bool = False) -> Any:
        """
        Calculate weights with boundary frame balancing to ensure consistent treatment
        
        Args:
            frame_positions: List of frame positions within window
            window_size: Size of processing window
            device: Torch device
            is_boundary_window: True if this is a boundary extension window
            
        Returns:
            torch.Tensor: Balanced weights for frames in window
        """
        import torch
        
        # Base triangular weights (0.3 to 1.0 range)
        center = window_size // 2
        weights = torch.zeros(len(frame_positions)).to(device)
        
        for i, pos in enumerate(frame_positions):
            distance_ratio = abs(pos - center) / center
            base_weight = 0.3 + 0.7 * (1.0 - distance_ratio)
            
            if is_boundary_window:
                # BOUNDARY ENHANCEMENT: Boost weights for boundary frames to compensate 
                # for reduced coverage compared to center frames
                if pos == 0 or pos == window_size - 1:
                    # Boost edge positions by 20% for better balance
                    boundary_boost = 1.2
                    weights[i] = min(1.0, base_weight * boundary_boost)
                else:
                    weights[i] = base_weight
            else:
                # Regular triangular weights for normal windows
                weights[i] = base_weight
        
        return weights
    
    def _validate_temporal_consistency(self, smoothed_tensor: Any, weight_tensor: Any, num_frames: int) -> dict:
        """
        Validate temporal consistency and smoothing quality across frame transitions
        
        Args:
            smoothed_tensor: Smoothed pose tensor (1, num_frames, 99)
            weight_tensor: Weight accumulation tensor (num_frames,)
            num_frames: Total number of frames
            
        Returns:
            dict: Temporal consistency analysis results
        """
        import torch
        
        analysis = {
            'weight_distribution': {},
            'temporal_smoothness': {},
            'boundary_quality': {},
            'consistency_score': 0.0
        }
        
        # 1. Weight distribution analysis
        if num_frames > 0:
            # Compare edge vs center weight accumulation
            edge_weights = []
            center_weights = []
            
            # Collect edge frame weights (first/last 25%)
            edge_count = max(1, num_frames // 4)
            edge_weights.extend(weight_tensor[:edge_count].tolist())
            edge_weights.extend(weight_tensor[-edge_count:].tolist())
            
            # Collect center frame weights (middle 50%)
            center_start = num_frames // 4
            center_end = num_frames - center_start
            center_weights.extend(weight_tensor[center_start:center_end].tolist())
            
            avg_edge_weight = sum(edge_weights) / len(edge_weights) if edge_weights else 0.0
            avg_center_weight = sum(center_weights) / len(center_weights) if center_weights else 0.0
            
            weight_balance_ratio = avg_edge_weight / avg_center_weight if avg_center_weight > 0 else 0.0
            
            analysis['weight_distribution'] = {
                'avg_edge_weight': avg_edge_weight,
                'avg_center_weight': avg_center_weight,
                'balance_ratio': weight_balance_ratio,
                'quality': 'GOOD' if 0.7 <= weight_balance_ratio <= 1.3 else 'POOR'
            }
        
        # 2. Temporal smoothness analysis
        if num_frames > 1:
            # Calculate frame-to-frame coordinate differences
            frame_diffs = []
            for i in range(num_frames - 1):
                # Calculate RMS difference between consecutive frames
                diff = smoothed_tensor[:, i+1, :] - smoothed_tensor[:, i, :]
                rms_diff = torch.sqrt(torch.mean(diff**2)).item()
                frame_diffs.append(rms_diff)
            
            avg_temporal_diff = sum(frame_diffs) / len(frame_diffs)
            max_temporal_diff = max(frame_diffs)
            
            # Check for temporal discontinuities (large jumps)
            discontinuity_threshold = avg_temporal_diff * 3.0  # 3x average = discontinuity
            discontinuities = sum(1 for diff in frame_diffs if diff > discontinuity_threshold)
            
            analysis['temporal_smoothness'] = {
                'avg_frame_diff': avg_temporal_diff,
                'max_frame_diff': max_temporal_diff,
                'discontinuities': discontinuities,
                'smoothness_quality': 'GOOD' if discontinuities == 0 else 'POOR'
            }
        
        # 3. Boundary quality assessment
        if num_frames >= 3:
            # Compare first/last frames to their neighbors
            boundary_issues = 0
            
            # First frame quality
            first_to_second_diff = torch.sqrt(torch.mean((smoothed_tensor[:, 1, :] - smoothed_tensor[:, 0, :])**2)).item()
            second_to_third_diff = torch.sqrt(torch.mean((smoothed_tensor[:, 2, :] - smoothed_tensor[:, 1, :])**2)).item()
            
            if first_to_second_diff > second_to_third_diff * 2.0:
                boundary_issues += 1
            
            # Last frame quality  
            if num_frames >= 3:
                last_to_prev_diff = torch.sqrt(torch.mean((smoothed_tensor[:, -1, :] - smoothed_tensor[:, -2, :])**2)).item()
                prev_to_prev2_diff = torch.sqrt(torch.mean((smoothed_tensor[:, -2, :] - smoothed_tensor[:, -3, :])**2)).item()
                
                if last_to_prev_diff > prev_to_prev2_diff * 2.0:
                    boundary_issues += 1
            
            analysis['boundary_quality'] = {
                'boundary_issues': boundary_issues,
                'first_frame_diff': first_to_second_diff,
                'last_frame_diff': last_to_prev_diff if num_frames >= 3 else 0.0,
                'quality': 'GOOD' if boundary_issues == 0 else 'POOR'
            }
        
        # 4. Overall consistency score (0.0 to 1.0)
        scores = []
        if 'quality' in analysis['weight_distribution']:
            scores.append(1.0 if analysis['weight_distribution']['quality'] == 'GOOD' else 0.3)
        if 'smoothness_quality' in analysis['temporal_smoothness']:
            scores.append(1.0 if analysis['temporal_smoothness']['smoothness_quality'] == 'GOOD' else 0.3)
        if 'quality' in analysis['boundary_quality']:
            scores.append(1.0 if analysis['boundary_quality']['quality'] == 'GOOD' else 0.3)
            
        analysis['consistency_score'] = sum(scores) / len(scores) if scores else 0.0
        
        return analysis
    
    def _calculate_edge_frame_quality_metrics(self, smoothed_tensor: Any, weight_tensor: Any, num_frames: int) -> dict:
        """
        Calculate specific quality metrics for edge frames vs center frames
        
        Args:
            smoothed_tensor: Smoothed pose tensor (1, num_frames, 99)
            weight_tensor: Weight accumulation tensor (num_frames,)  
            num_frames: Total number of frames
            
        Returns:
            dict: Edge frame quality analysis
        """
        import torch
        
        metrics = {
            'edge_frame_scores': {},
            'center_frame_scores': {}, 
            'quality_comparison': {},
            'recommendations': []
        }
        
        if num_frames < 5:
            metrics['recommendations'].append("Sequence too short for meaningful edge frame analysis")
            return metrics
        
        # Define edge and center frame indices
        edge_count = max(2, num_frames // 8)  # At least 2 frames, up to 12.5% of sequence
        edge_indices = list(range(edge_count)) + list(range(num_frames - edge_count, num_frames))
        center_start = num_frames // 3
        center_end = num_frames - center_start
        center_indices = list(range(center_start, center_end))
        
        # 1. Weight quality assessment
        edge_weights = [weight_tensor[i].item() for i in edge_indices]
        center_weights = [weight_tensor[i].item() for i in center_indices if i < len(weight_tensor)]
        
        avg_edge_weight = sum(edge_weights) / len(edge_weights) if edge_weights else 0.0
        avg_center_weight = sum(center_weights) / len(center_weights) if center_weights else 0.0
        
        weight_parity = avg_edge_weight / avg_center_weight if avg_center_weight > 0 else 0.0
        
        # 2. Smoothing effectiveness (coordinate stability)
        edge_stability_scores = []
        center_stability_scores = []
        
        # Calculate coordinate variance for edge frames
        for i in edge_indices:
            if i > 0 and i < num_frames - 1:
                # Calculate how much this frame differs from its neighbors
                prev_diff = torch.mean((smoothed_tensor[:, i, :] - smoothed_tensor[:, i-1, :])**2).item()
                next_diff = torch.mean((smoothed_tensor[:, i+1, :] - smoothed_tensor[:, i, :])**2).item()
                stability = 1.0 / (1.0 + (prev_diff + next_diff) / 2.0)  # Higher = more stable
                edge_stability_scores.append(stability)
        
        # Calculate coordinate variance for center frames
        for i in center_indices:
            if i > 0 and i < num_frames - 1:
                prev_diff = torch.mean((smoothed_tensor[:, i, :] - smoothed_tensor[:, i-1, :])**2).item()
                next_diff = torch.mean((smoothed_tensor[:, i+1, :] - smoothed_tensor[:, i, :])**2).item()
                stability = 1.0 / (1.0 + (prev_diff + next_diff) / 2.0)
                center_stability_scores.append(stability)
        
        avg_edge_stability = sum(edge_stability_scores) / len(edge_stability_scores) if edge_stability_scores else 0.0
        avg_center_stability = sum(center_stability_scores) / len(center_stability_scores) if center_stability_scores else 0.0
        
        stability_parity = avg_edge_stability / avg_center_stability if avg_center_stability > 0 else 0.0
        
        # 3. Coordinate range preservation
        edge_ranges = []
        center_ranges = []
        
        for i in edge_indices:
            frame_coords = smoothed_tensor[:, i, :].flatten()
            coord_range = (torch.max(frame_coords) - torch.min(frame_coords)).item()
            edge_ranges.append(coord_range)
            
        for i in center_indices:
            frame_coords = smoothed_tensor[:, i, :].flatten()
            coord_range = (torch.max(frame_coords) - torch.min(frame_coords)).item()
            center_ranges.append(coord_range)
        
        avg_edge_range = sum(edge_ranges) / len(edge_ranges) if edge_ranges else 0.0
        avg_center_range = sum(center_ranges) / len(center_ranges) if center_ranges else 0.0
        
        range_parity = avg_edge_range / avg_center_range if avg_center_range > 0 else 0.0
        
        # Store metrics
        metrics['edge_frame_scores'] = {
            'avg_weight': avg_edge_weight,
            'avg_stability': avg_edge_stability,
            'avg_coordinate_range': avg_edge_range,
            'frame_count': len(edge_indices)
        }
        
        metrics['center_frame_scores'] = {
            'avg_weight': avg_center_weight,
            'avg_stability': avg_center_stability,
            'avg_coordinate_range': avg_center_range,
            'frame_count': len(center_indices)
        }
        
        metrics['quality_comparison'] = {
            'weight_parity': weight_parity,
            'stability_parity': stability_parity,
            'range_parity': range_parity,
            'overall_edge_quality': (weight_parity + stability_parity + range_parity) / 3.0
        }
        
        # 4. Generate recommendations
        if weight_parity < 0.8:
            metrics['recommendations'].append(f"Edge frames underweighted ({weight_parity:.2f}) - consider stronger boundary extension")
        if stability_parity < 0.9:
            metrics['recommendations'].append(f"Edge frames less stable ({stability_parity:.2f}) - boundary smoothing needs improvement")
        if range_parity < 0.95 or range_parity > 1.05:
            metrics['recommendations'].append(f"Edge coordinate ranges differ ({range_parity:.2f}) - check boundary padding consistency")
        if not metrics['recommendations']:
            metrics['recommendations'].append("Edge frame quality is good - no issues detected")
        
        return metrics
    
    def _validate_tensor_format(self, tensor: Any, expected_shape: tuple, stage_name: str, auto_fix: bool = True) -> Any:
        """
        Comprehensive tensor format validation with auto-correction
        
        Args:
            tensor: Tensor to validate
            expected_shape: Expected dimensions (use -1 for variable dimensions)
            stage_name: Name of pipeline stage for logging
            auto_fix: Whether to attempt automatic format correction
            
        Returns:
            torch.Tensor: Validated/corrected tensor
            
        Raises:
            ValueError: If tensor format is invalid and cannot be fixed
        """
        import torch
        
        logger.info(f"\n=== TENSOR FORMAT VALIDATION: {stage_name} ===")
        
        # 1. Basic shape validation
        actual_shape = tensor.shape
        logger.info(f"Expected shape: {expected_shape}")
        logger.info(f"Actual shape: {actual_shape}")
        
        # Check number of dimensions
        if len(actual_shape) != len(expected_shape):
            error_msg = f"Dimension mismatch at {stage_name}: expected {len(expected_shape)}D, got {len(actual_shape)}D"
            logger.error(error_msg)
            
            # Try to fix common dimension issues
            if auto_fix and len(actual_shape) == 2 and len(expected_shape) == 3:
                # Missing batch dimension - add it
                tensor = tensor.unsqueeze(0)
                logger.warning(f"Auto-fixed: Added batch dimension. New shape: {tensor.shape}")
            else:
                raise ValueError(error_msg)
        
        # 2. Validate each dimension
        shape_valid = True
        for i, (expected, actual) in enumerate(zip(expected_shape, tensor.shape)):
            if expected != -1 and expected != actual:
                shape_valid = False
                logger.warning(f"Dimension {i} mismatch: expected {expected}, got {actual}")
        
        # 3. Check for common transpose errors
        if not shape_valid and auto_fix:
            # Check if it's a (1, 99, T) vs (1, T, 99) issue
            if (len(expected_shape) == 3 and 
                expected_shape[0] == actual_shape[0] and  # Batch dim matches
                expected_shape[1] == actual_shape[2] and  # T and 99 swapped
                expected_shape[2] == actual_shape[1]):
                
                tensor = tensor.transpose(1, 2)
                logger.warning(f"Auto-fixed: Transposed dimensions 1 and 2. New shape: {tensor.shape}")
                actual_shape = tensor.shape
                shape_valid = True
        
        if not shape_valid:
            try:
                # Try to handle the error gracefully
                error = ValueError(f"Shape validation failed at {stage_name}")
                tensor = self._handle_tensor_format_error(error, tensor, stage_name)
                logger.info("✅ Successfully recovered from shape validation error")
            except Exception as recovery_error:
                raise ValueError(f"Shape validation failed at {stage_name}: {recovery_error}")
        
        # 4. Value range validation
        if not torch.all(torch.isfinite(tensor)):
            nan_count = torch.sum(torch.isnan(tensor)).item()
            inf_count = torch.sum(torch.isinf(tensor)).item()
            logger.error(f"Non-finite values detected: {nan_count} NaN, {inf_count} Inf")
            
            if auto_fix:
                # Replace NaN with 0, Inf with large finite values
                tensor = torch.nan_to_num(tensor, nan=0.0, posinf=1e6, neginf=-1e6)
                logger.warning("Auto-fixed: Replaced non-finite values")
            else:
                raise ValueError(f"Non-finite values at {stage_name}")
        
        # 5. Coordinate range validation (for pose data)
        if len(actual_shape) == 3 and actual_shape[2] == 99:  # (batch, frames, keypoints*3)
            # Check if coordinates are in reasonable ranges
            coord_min = tensor.min().item()
            coord_max = tensor.max().item()
            logger.info(f"Coordinate range: [{coord_min:.3f}, {coord_max:.3f}]")
            
            # Different stages have different expected ranges
            if "world" in stage_name.lower():
                expected_range = (-2000, 2000)  # mm
            elif "normalized" in stage_name.lower():
                expected_range = (-2, 2)  # normalized
            elif "pixel" in stage_name.lower():
                expected_range = (0, 4000)  # pixels (up to 4K)
            else:
                expected_range = (-10000, 10000)  # generous default
            
            if coord_min < expected_range[0] or coord_max > expected_range[1]:
                logger.warning(f"Coordinates outside expected range {expected_range}")
                
                if auto_fix and coord_max > 10000:
                    # Likely in wrong units - try scaling
                    scale_factor = 1000.0 if coord_max > 100000 else 1.0
                    tensor = tensor / scale_factor
                    logger.warning(f"Auto-fixed: Scaled coordinates by {scale_factor}")
        
        # 6. Keypoint structure validation
        if actual_shape[-1] == 99:  # Should be 33 keypoints * 3 coords
            # Reshape to separate keypoints and coordinates
            *batch_dims, features = tensor.shape
            reshaped = tensor.reshape(*batch_dims, 33, 3)
            
            # Check Z-coordinate validity
            z_coords = reshaped[..., 2]
            z_min = z_coords.min().item()
            z_max = z_coords.max().item()
            z_zeros = (z_coords == 0).sum().item()
            z_total = z_coords.numel()
            
            logger.info(f"Z-coordinate stats: range=[{z_min:.3f}, {z_max:.3f}], zeros={z_zeros}/{z_total}")
            
            if z_zeros > z_total * 0.9:  # More than 90% zeros
                logger.warning("Most Z-coordinates are zero - possible 2D pose data")
        
        logger.info(f"✅ Tensor format validation passed for {stage_name}")
        return tensor
    
    def _define_keypoint_schema(self) -> dict:
        """
        Define explicit BlazePose keypoint layout schema for validation
        
        Returns:
            dict: Comprehensive keypoint schema with indices, names, and constraints
        """
        schema = {
            'num_keypoints': 33,
            'coordinate_order': ['x', 'y', 'z'],
            'keypoint_names': [
                # Face landmarks (0-10)
                'nose',              # 0
                'left_eye_inner',    # 1
                'left_eye',          # 2
                'left_eye_outer',    # 3
                'right_eye_inner',   # 4
                'right_eye',         # 5
                'right_eye_outer',   # 6
                'left_ear',          # 7
                'right_ear',         # 8
                'mouth_left',        # 9
                'mouth_right',       # 10
                
                # Upper body (11-22)
                'left_shoulder',     # 11
                'right_shoulder',    # 12
                'left_elbow',        # 13
                'right_elbow',       # 14
                'left_wrist',        # 15
                'right_wrist',       # 16
                'left_pinky',        # 17
                'right_pinky',       # 18
                'left_index',        # 19
                'right_index',       # 20
                'left_thumb',        # 21
                'right_thumb',       # 22
                
                # Lower body (23-32)
                'left_hip',          # 23
                'right_hip',         # 24
                'left_knee',         # 25
                'right_knee',        # 26
                'left_ankle',        # 27
                'right_ankle',       # 28
                'left_heel',         # 29
                'right_heel',        # 30
                'left_foot_index',   # 31
                'right_foot_index',  # 32
            ],
            
            # Anatomical constraint groups
            'paired_keypoints': [
                (1, 4),   # eye_inner
                (2, 5),   # eye
                (3, 6),   # eye_outer
                (7, 8),   # ear
                (9, 10),  # mouth
                (11, 12), # shoulder
                (13, 14), # elbow
                (15, 16), # wrist
                (17, 18), # pinky
                (19, 20), # index
                (21, 22), # thumb
                (23, 24), # hip
                (25, 26), # knee
                (27, 28), # ankle
                (29, 30), # heel
                (31, 32), # foot_index
            ],
            
            # Expected connectivity for anatomical validation
            'skeletal_connections': [
                # Head
                (0, 1), (1, 2), (2, 3), (3, 7),    # Left eye chain
                (0, 4), (4, 5), (5, 6), (6, 8),    # Right eye chain
                (9, 10),                             # Mouth
                
                # Arms
                (11, 13), (13, 15),                 # Left arm
                (12, 14), (14, 16),                 # Right arm
                (11, 12),                           # Shoulders
                (11, 23), (12, 24),                 # Shoulder to hip
                
                # Hands
                (15, 17), (15, 19), (15, 21),      # Left hand
                (16, 18), (16, 20), (16, 22),      # Right hand
                
                # Legs
                (23, 24),                           # Hips
                (23, 25), (25, 27),                 # Left leg
                (24, 26), (26, 28),                 # Right leg
                
                # Feet
                (27, 29), (29, 31),                 # Left foot
                (28, 30), (30, 32),                 # Right foot
            ],
            
            # BlazePose to 3DPW mapping (for SmoothNet compatibility)
            'blazepose_to_3dpw': {
                # Core body landmarks that exist in both
                0: 'nose',
                11: 'left_shoulder',
                12: 'right_shoulder',
                13: 'left_elbow', 
                14: 'right_elbow',
                15: 'left_wrist',
                16: 'right_wrist',
                23: 'left_hip',
                24: 'right_hip',
                25: 'left_knee',
                26: 'right_knee',
                27: 'left_ankle',
                28: 'right_ankle',
                # Note: BlazePose has more detailed face/hand/foot keypoints than 3DPW
            },
            
            # Validation rules
            'validation_rules': {
                'min_coordinate_value': -10000.0,
                'max_coordinate_value': 10000.0,
                'max_limb_length_mm': 1000.0,      # 1 meter max limb length
                'min_keypoint_distance_mm': 10.0,   # Minimum 10mm between different keypoints
                'expected_human_height_mm': (1000.0, 2500.0),  # 1-2.5 meters
            }
        }
        
        return schema
    
    def _validate_keypoint_layout(self, tensor: Any, schema: dict, stage_name: str) -> bool:
        """
        Validate keypoint layout matches expected schema
        
        Args:
            tensor: Pose tensor shaped (batch, frames, 99) or (batch, frames, 33, 3)
            schema: Keypoint schema from _define_keypoint_schema()
            stage_name: Pipeline stage name for logging
            
        Returns:
            bool: True if valid, logs warnings for issues
        """
        import torch
        
        logger.info(f"Validating keypoint layout at {stage_name}")
        
        # Reshape to separate keypoints and coordinates
        if tensor.shape[-1] == 99:
            *batch_dims, _ = tensor.shape
            tensor_reshaped = tensor.reshape(*batch_dims, 33, 3)
        else:
            tensor_reshaped = tensor
        
        batch_size, num_frames, num_keypoints, num_coords = tensor_reshaped.shape
        
        # 1. Validate basic structure
        if num_keypoints != schema['num_keypoints']:
            logger.error(f"Wrong number of keypoints: expected {schema['num_keypoints']}, got {num_keypoints}")
            return False
        
        if num_coords != len(schema['coordinate_order']):
            logger.error(f"Wrong number of coordinates: expected {len(schema['coordinate_order'])}, got {num_coords}")
            return False
        
        # 2. Check paired keypoint symmetry (left/right should be similar)
        symmetry_issues = 0
        for left_idx, right_idx in schema['paired_keypoints']:
            left_kp = tensor_reshaped[:, :, left_idx, :]
            right_kp = tensor_reshaped[:, :, right_idx, :]
            
            # Calculate average distance between paired keypoints
            pair_dist = torch.mean(torch.norm(left_kp - right_kp, dim=-1)).item()
            
            # If paired keypoints are too far apart, might indicate issues
            if pair_dist > 500.0:  # 500mm = 50cm threshold
                symmetry_issues += 1
                if symmetry_issues <= 3:  # Log first few issues
                    logger.warning(f"Large distance between {schema['keypoint_names'][left_idx]} and "
                                 f"{schema['keypoint_names'][right_idx]}: {pair_dist:.1f}mm")
        
        if symmetry_issues > 0:
            logger.warning(f"Found {symmetry_issues} paired keypoint symmetry issues")
        
        # 3. Check skeletal connection distances
        connection_issues = 0
        rules = schema['validation_rules']
        
        for kp1_idx, kp2_idx in schema['skeletal_connections']:
            kp1 = tensor_reshaped[:, :, kp1_idx, :]
            kp2 = tensor_reshaped[:, :, kp2_idx, :]
            
            # Calculate connection lengths
            connection_lengths = torch.norm(kp1 - kp2, dim=-1)
            max_length = torch.max(connection_lengths).item()
            
            if max_length > rules['max_limb_length_mm']:
                connection_issues += 1
                if connection_issues <= 3:
                    logger.warning(f"Unrealistic connection length {schema['keypoint_names'][kp1_idx]}->"
                                 f"{schema['keypoint_names'][kp2_idx]}: {max_length:.1f}mm")
        
        if connection_issues > 0:
            logger.warning(f"Found {connection_issues} unrealistic skeletal connections")
        
        # 4. Validate overall body dimensions
        # Estimate height from head to feet
        nose = tensor_reshaped[:, :, 0, :]  # nose
        left_ankle = tensor_reshaped[:, :, 27, :]
        right_ankle = tensor_reshaped[:, :, 28, :]
        
        # Average of both ankles
        avg_ankle = (left_ankle + right_ankle) / 2.0
        
        # Calculate height as nose to ankle distance
        heights = torch.norm(nose - avg_ankle, dim=-1)
        avg_height = torch.mean(heights).item()
        
        min_height, max_height = rules['expected_human_height_mm']
        if avg_height < min_height or avg_height > max_height:
            logger.warning(f"Unusual body height detected: {avg_height:.1f}mm (expected {min_height}-{max_height}mm)")
        
        # 5. Check for keypoint collisions (multiple keypoints at same location)
        collision_threshold = rules['min_keypoint_distance_mm']
        collisions = 0
        
        # Check first frame as sample
        if num_frames > 0:
            first_frame = tensor_reshaped[0, 0, :, :]  # Shape: (33, 3)
            for i in range(num_keypoints):
                for j in range(i + 1, num_keypoints):
                    dist = torch.norm(first_frame[i] - first_frame[j]).item()
                    if dist < collision_threshold:
                        collisions += 1
                        if collisions <= 3:
                            logger.warning(f"Keypoints too close: {schema['keypoint_names'][i]} and "
                                         f"{schema['keypoint_names'][j]} ({dist:.1f}mm apart)")
        
        if collisions > 0:
            logger.warning(f"Found {collisions} keypoint collisions in first frame")
        
        # Overall validation result
        is_valid = (symmetry_issues < 5 and connection_issues < 5 and collisions < 10)
        
        if is_valid:
            logger.info(f"✅ Keypoint layout validation passed")
        else:
            logger.error(f"❌ Keypoint layout validation failed")
        
        return is_valid
    
    def _handle_tensor_format_error(self, error: Exception, tensor: Any, stage_name: str) -> Any:
        """
        Graceful error handling for tensor format issues
        
        Args:
            error: The format error that occurred
            tensor: The problematic tensor
            stage_name: Pipeline stage where error occurred
            
        Returns:
            torch.Tensor: Corrected tensor or raises if unfixable
        """
        import torch
        
        logger.error(f"Tensor format error at {stage_name}: {str(error)}")
        
        # Try common fixes based on error type
        if "dimension mismatch" in str(error).lower():
            logger.info("Attempting dimension fix...")
            
            # Check if it's a simple transpose issue
            if len(tensor.shape) == 3:
                # Try all possible permutations for 3D tensor
                permutations = [(0, 1, 2), (0, 2, 1), (1, 0, 2), (1, 2, 0), (2, 0, 1), (2, 1, 0)]
                
                for perm in permutations:
                    try:
                        candidate = tensor.permute(perm)
                        # Check if this makes sense for pose data
                        if (candidate.shape[0] == 1 and  # batch = 1
                            candidate.shape[2] == 99):   # features = 99
                            logger.warning(f"Fixed dimension order from {tensor.shape} to {candidate.shape}")
                            return candidate
                    except:
                        continue
            
            # Try adding/removing batch dimension
            if len(tensor.shape) == 2:
                candidate = tensor.unsqueeze(0)
                logger.warning(f"Added batch dimension: {tensor.shape} -> {candidate.shape}")
                return candidate
                
        elif "non-finite" in str(error).lower():
            logger.info("Attempting non-finite value fix...")
            # Replace NaN/Inf with reasonable values
            tensor = torch.nan_to_num(tensor, nan=0.0, posinf=1000.0, neginf=-1000.0)
            logger.warning("Replaced non-finite values with finite approximations")
            return tensor
            
        elif "range" in str(error).lower():
            logger.info("Attempting coordinate range fix...")
            # Clip values to reasonable ranges
            tensor = torch.clamp(tensor, min=-10000.0, max=10000.0)
            logger.warning("Clamped coordinates to [-10000, 10000] range")
            return tensor
        
        # If we can't fix it, provide detailed error information
        error_info = {
            'stage': stage_name,
            'tensor_shape': tensor.shape,
            'tensor_dtype': tensor.dtype,
            'tensor_device': tensor.device,
            'error_type': type(error).__name__,
            'error_message': str(error)
        }
        
        logger.error(f"Unable to fix tensor format error. Details: {error_info}")
        
        # Create a minimal valid tensor as last resort
        if "pose" in stage_name.lower():
            logger.warning("Creating minimal valid pose tensor as fallback")
            # Create a tensor with reasonable default pose (T-pose)
            fallback_tensor = torch.zeros(1, 1, 99, dtype=torch.float32)
            return fallback_tensor
        
        # Re-raise if we can't handle it
        raise error
    
    def _debug_tensor_format(self, tensor: Any, name: str, verbose: bool = True) -> dict:
        """
        Comprehensive tensor format debugging tools
        
        Args:
            tensor: Tensor to debug
            name: Name/identifier for the tensor
            verbose: Whether to log detailed information
            
        Returns:
            dict: Debug information about the tensor
        """
        import torch
        
        debug_info = {
            'name': name,
            'timestamp': datetime.now().isoformat(),
            'shape': tensor.shape,
            'dtype': str(tensor.dtype),
            'device': str(tensor.device),
            'requires_grad': tensor.requires_grad,
            'memory_usage_mb': tensor.numel() * tensor.element_size() / (1024 * 1024),
            'statistics': {}
        }
        
        # Basic statistics
        debug_info['statistics'] = {
            'min': tensor.min().item(),
            'max': tensor.max().item(),
            'mean': tensor.mean().item(),
            'std': tensor.std().item(),
            'num_elements': tensor.numel(),
            'finite_elements': torch.sum(torch.isfinite(tensor)).item(),
            'nan_count': torch.sum(torch.isnan(tensor)).item(),
            'inf_count': torch.sum(torch.isinf(tensor)).item()
        }
        
        # Shape analysis for pose tensors
        if len(tensor.shape) == 3 and tensor.shape[-1] == 99:
            batch, frames, features = tensor.shape
            debug_info['pose_analysis'] = {
                'batch_size': batch,
                'num_frames': frames,
                'keypoints_per_frame': features // 3,
                'coordinates_per_keypoint': 3,
                'expected_format': f"({batch}, {frames}, 99) = (batch, frames, 33*3)"
            }
            
            # Coordinate-specific statistics
            x_coords = tensor[:, :, ::3]  # Every 3rd starting from 0
            y_coords = tensor[:, :, 1::3]  # Every 3rd starting from 1
            z_coords = tensor[:, :, 2::3]  # Every 3rd starting from 2
            
            debug_info['coordinate_analysis'] = {
                'x_range': [x_coords.min().item(), x_coords.max().item()],
                'y_range': [y_coords.min().item(), y_coords.max().item()],
                'z_range': [z_coords.min().item(), z_coords.max().item()],
                'x_mean': x_coords.mean().item(),
                'y_mean': y_coords.mean().item(),
                'z_mean': z_coords.mean().item(),
                'z_zeros_percent': (z_coords == 0).float().mean().item() * 100
            }
        
        # Temporal analysis for multi-frame tensors
        if len(tensor.shape) >= 2 and tensor.shape[-2] > 1:
            frames_dim = -2
            frame_diffs = []
            for i in range(tensor.shape[frames_dim] - 1):
                diff = torch.mean((tensor.select(frames_dim, i+1) - tensor.select(frames_dim, i))**2).item()
                frame_diffs.append(diff)
            
            debug_info['temporal_analysis'] = {
                'avg_frame_diff': sum(frame_diffs) / len(frame_diffs) if frame_diffs else 0.0,
                'max_frame_diff': max(frame_diffs) if frame_diffs else 0.0,
                'min_frame_diff': min(frame_diffs) if frame_diffs else 0.0,
                'temporal_stability': 1.0 / (1.0 + sum(frame_diffs) / len(frame_diffs)) if frame_diffs else 1.0
            }
        
        if verbose:
            logger.info(f"\n=== TENSOR DEBUG: {name} ===")
            logger.info(f"Shape: {debug_info['shape']}")
            logger.info(f"Memory: {debug_info['memory_usage_mb']:.2f} MB")
            logger.info(f"Stats: min={debug_info['statistics']['min']:.3f}, "
                       f"max={debug_info['statistics']['max']:.3f}, "
                       f"mean={debug_info['statistics']['mean']:.3f}")
            
            if 'pose_analysis' in debug_info:
                pa = debug_info['pose_analysis']
                logger.info(f"Pose: {pa['num_frames']} frames, {pa['keypoints_per_frame']} keypoints")
                
            if 'coordinate_analysis' in debug_info:
                ca = debug_info['coordinate_analysis']
                logger.info(f"Coords: X[{ca['x_range'][0]:.1f}, {ca['x_range'][1]:.1f}], "
                           f"Y[{ca['y_range'][0]:.1f}, {ca['y_range'][1]:.1f}], "
                           f"Z[{ca['z_range'][0]:.1f}, {ca['z_range'][1]:.1f}]")
                logger.info(f"Z-zeros: {ca['z_zeros_percent']:.1f}%")
                
            if 'temporal_analysis' in debug_info:
                ta = debug_info['temporal_analysis']
                logger.info(f"Temporal: avg_diff={ta['avg_frame_diff']:.3f}, "
                           f"stability={ta['temporal_stability']:.3f}")
            
            # Health check warnings
            stats = debug_info['statistics']
            if stats['nan_count'] > 0:
                logger.warning(f"⚠️ Contains {stats['nan_count']} NaN values")
            if stats['inf_count'] > 0:
                logger.warning(f"⚠️ Contains {stats['inf_count']} infinite values")
            if abs(stats['mean']) > 10000:
                logger.warning(f"⚠️ Large mean value ({stats['mean']:.1f}) - possible scaling issue")
                
        return debug_info
    
    def _log_tensor_evolution(self, tensors_dict: dict, stage_name: str):
        """
        Log the evolution of tensor shapes through pipeline stages
        
        Args:
            tensors_dict: Dictionary of {name: tensor} pairs
            stage_name: Current pipeline stage name
        """
        logger.info(f"\n=== TENSOR EVOLUTION: {stage_name} ===")
        
        for name, tensor in tensors_dict.items():
            if tensor is not None:
                debug_info = self._debug_tensor_format(tensor, name, verbose=False)
                shape_str = "×".join(map(str, debug_info['shape']))
                memory_mb = debug_info['memory_usage_mb']
                
                logger.info(f"{name:20} {shape_str:15} {memory_mb:6.2f}MB "
                           f"[{debug_info['statistics']['min']:6.1f}, {debug_info['statistics']['max']:6.1f}]")
            else:
                logger.info(f"{name:20} {'None':15}")
    
    def _process_sliding_windows(self, pose_tensor: Any, model, window_size: int, device) -> Any:
        """
        Process pose sequence using sliding windows with overlap and blending
        
        Args:
            pose_tensor: Input tensor (1, num_frames, 99) - CORRECTED FORMAT
            model: SmoothNet model
            window_size: Window size for processing
            device: Torch device
            
        Returns:
            torch.Tensor: Smoothed pose tensor (1, num_frames, 99) - CORRECTED FORMAT
        """
        import torch
        
        num_frames = pose_tensor.shape[1]  # CORRECTED: frames are now in dimension 1
        stride = window_size // 2  # 50% overlap
        
        logger.info(f"Processing {num_frames} frames with {window_size}-frame sliding window (stride: {stride})")
        
        # Initialize output tensor and weight accumulation for blending
        smoothed_tensor = torch.zeros_like(pose_tensor).to(device)
        weight_tensor = torch.zeros(num_frames).to(device)
        
        window_count = 0
        
        # IMPROVED: Symmetric boundary extension for consistent edge frame coverage
        # This ensures Frame 0 and last frames get proper smoothing treatment
        
        # Process initial boundary extension for better Frame 0 coverage
        if num_frames >= window_size:
            # Create symmetric boundary extension using dedicated function
            reflected_window = self._create_symmetric_boundary_extension(
                pose_tensor, window_size, device, 'start'
            )
            half_window = window_size // 2
            
            # Apply SmoothNet to reflected window
            with torch.no_grad():
                window_output = model(reflected_window)
            
            # Apply boundary-balanced weights for initial boundary extension
            frame_positions = list(range(window_size))
            window_weights = self._calculate_boundary_balanced_weights(
                frame_positions, window_size, device, is_boundary_window=True
            )
            
            # Accumulate results for frames [0-15]
            for i in range(half_window):
                frame_idx = i
                window_pos = half_window + i
                smoothed_tensor[:, frame_idx, :] += window_output[:, window_pos, :] * window_weights[window_pos]
                weight_tensor[frame_idx] += window_weights[window_pos]
            
            window_count += 1
            logger.info(f"Processed reflected window [-{half_window} to {half_window-1}] for edge frame coverage")
        
        # Process regular sliding windows
        for start_idx in range(0, num_frames, stride):
            end_idx = min(start_idx + window_size, num_frames)
            actual_window_size = end_idx - start_idx
            window_count += 1
            
            # Handle edge case: pad short windows
            if actual_window_size < window_size:
                # Pad by repeating last frame - CORRECTED FORMAT (batch, frames, features)
                window_input = torch.zeros(1, window_size, 99, dtype=torch.float32).to(device)
                window_input[:, :actual_window_size, :] = pose_tensor[:, start_idx:end_idx, :]
                
                # Repeat last frame to fill window
                if actual_window_size > 0:
                    last_frame = pose_tensor[:, end_idx-1:end_idx, :]
                    for i in range(actual_window_size, window_size):
                        window_input[:, i:i+1, :] = last_frame
            else:
                window_input = pose_tensor[:, start_idx:end_idx, :].to(device)
            
            # Apply SmoothNet to window
            with torch.no_grad():
                window_output = model(window_input)
                # Keep on GPU for accumulation
            
            # Create boundary-balanced blending weights for consistent treatment
            if actual_window_size == window_size:
                # Use boundary-balanced weights for full windows
                frame_positions = list(range(window_size))
                window_weights = self._calculate_boundary_balanced_weights(
                    frame_positions, window_size, device, is_boundary_window=False
                )
                
                # Log weight distribution for first regular window
                if start_idx == 0:
                    center = window_size // 2
                    logger.info("First regular window weight distribution (boundary-balanced):")
                    logger.info(f"  Frame 0 weight: {window_weights[0]:.3f}")
                    logger.info(f"  Frame {center} (center) weight: {window_weights[center]:.3f}")
                    logger.info(f"  Frame {window_size-1} weight: {window_weights[window_size-1]:.3f}")
            else:
                # Simple uniform weights for partial windows
                window_weights = torch.ones(actual_window_size).to(device)
            
            # Accumulate weighted results - CORRECTED FORMAT
            for i in range(actual_window_size):
                frame_idx = start_idx + i
                weight = window_weights[i] if i < len(window_weights) else 1.0
                
                # CORRECTED: Access frames in dimension 1
                smoothed_tensor[:, frame_idx, :] += window_output[:, i, :] * weight
                weight_tensor[frame_idx] += weight
        
        # Process final boundary extension for better last frame coverage
        if num_frames >= window_size:
            # Create symmetric boundary extension for end frames
            reflected_window = self._create_symmetric_boundary_extension(
                pose_tensor, window_size, device, 'end'
            )
            half_window = window_size // 2
            start_frame = max(0, num_frames - half_window)
            
            # Apply SmoothNet to reflected window
            with torch.no_grad():
                window_output = model(reflected_window)
            
            # Apply boundary-balanced weights for final boundary extension
            frame_positions = list(range(window_size))
            window_weights = self._calculate_boundary_balanced_weights(
                frame_positions, window_size, device, is_boundary_window=True
            )
            
            # Accumulate results for last frames
            for i in range(half_window):
                frame_idx = start_frame + i
                window_pos = i
                smoothed_tensor[:, frame_idx, :] += window_output[:, window_pos, :] * window_weights[window_pos]
                weight_tensor[frame_idx] += window_weights[window_pos]
            
            window_count += 1
            logger.info(f"Processed reflected window [{start_frame} to {num_frames + half_window - 1}] for end frame coverage")
        
        # CRITICAL FIX: Robust weight normalization with edge case handling
        # This fixes Issue #6: Weight Normalization Edge Cases
        min_weight_threshold = 0.1  # Minimum weight to avoid numerical instability
        edge_case_count = 0

        for frame_idx in range(num_frames):
            weight = weight_tensor[frame_idx]

            if weight >= min_weight_threshold:
                # NORMAL CASE: Safe division with sufficient weight
                smoothed_tensor[:, frame_idx, :] /= weight

            elif weight > 0.0:
                # LOW WEIGHT: Clamp to prevent numerical instability
                edge_case_count += 1
                clamped_weight = max(weight, min_weight_threshold * 0.5)  # Clamp to 0.05 minimum
                smoothed_tensor[:, frame_idx, :] /= clamped_weight

                # Log first few low-weight cases for debugging
                if edge_case_count <= 3:
                    logger.warning(f"Frame {frame_idx}: Low weight {weight:.4f} clamped to {clamped_weight:.4f}")

            else:
                # ZERO WEIGHT: Use neighbor interpolation fallback
                edge_case_count += 1
                
                if frame_idx > 0 and frame_idx < num_frames - 1:
                    # Interpolate between neighboring frames
                    prev_frame = smoothed_tensor[:, frame_idx-1, :].clone()
                    next_frame = smoothed_tensor[:, frame_idx+1, :].clone()
                    smoothed_tensor[:, frame_idx, :] = (prev_frame + next_frame) / 2.0
                    logger.warning(f"Frame {frame_idx}: Zero weight, using neighbor interpolation")

                elif frame_idx > 0:
                    # Use previous frame (for last frame case)
                    smoothed_tensor[:, frame_idx, :] = smoothed_tensor[:, frame_idx-1, :].clone()
                    logger.warning(f"Frame {frame_idx}: Zero weight, using previous frame")

                elif frame_idx < num_frames - 1:
                    # Use next frame (for first frame case)
                    smoothed_tensor[:, frame_idx, :] = smoothed_tensor[:, frame_idx+1, :].clone()
                    logger.warning(f"Frame {frame_idx}: Zero weight, using next frame")

                else:
                    # Single frame edge case: keep accumulated values as-is
                    logger.warning(f"Frame {frame_idx}: Zero weight, single frame - keeping accumulated values")

        # Log edge case statistics for quality monitoring
        if edge_case_count > 0:
            edge_case_ratio = edge_case_count / num_frames
            logger.warning(f"Weight normalization edge cases: {edge_case_count}/{num_frames} frames ({edge_case_ratio:.1%})")

            # Alert on high edge case ratio indicating systemic issues
            if edge_case_ratio > 0.1:  # More than 10% problematic frames
                logger.error(f"HIGH EDGE CASE RATIO ({edge_case_ratio:.1%}) - indicates potential sliding window issues")
        
        # Log frame coverage to verify fix
        logger.info(f"Frame 0 accumulated weight: {weight_tensor[0]:.3f} (improved with reflection)")
        logger.info(f"Frame {num_frames//2} accumulated weight: {weight_tensor[num_frames//2]:.3f}")
        logger.info(f"Frame {num_frames-1} accumulated weight: {weight_tensor[num_frames-1]:.3f} (improved with reflection)")
        
        # CRITICAL FIX: Temporal consistency validation - Issue #9 Step 4
        temporal_analysis = self._validate_temporal_consistency(smoothed_tensor, weight_tensor, num_frames)
        
        # Log temporal consistency results
        logger.info("=== TEMPORAL CONSISTENCY ANALYSIS ===")
        if 'weight_distribution' in temporal_analysis:
            wd = temporal_analysis['weight_distribution']
            logger.info(f"Weight Distribution: {wd['quality']} (edge/center ratio: {wd['balance_ratio']:.3f})")
            
        if 'temporal_smoothness' in temporal_analysis:
            ts = temporal_analysis['temporal_smoothness']
            logger.info(f"Temporal Smoothness: {ts['smoothness_quality']} ({ts['discontinuities']} discontinuities)")
            
        if 'boundary_quality' in temporal_analysis:
            bq = temporal_analysis['boundary_quality']
            logger.info(f"Boundary Quality: {bq['quality']} ({bq['boundary_issues']} boundary issues)")
            
        consistency_score = temporal_analysis['consistency_score']
        logger.info(f"Overall Consistency Score: {consistency_score:.3f}/1.0")
        
        # Alert on poor temporal consistency
        if consistency_score < 0.7:
            logger.warning(f"LOW TEMPORAL CONSISTENCY ({consistency_score:.3f}) - sliding window edge effects detected")
        else:
            logger.info(f"✅ GOOD TEMPORAL CONSISTENCY ({consistency_score:.3f}) - boundary frames properly handled")
        
        # CRITICAL FIX: Edge frame quality metrics - Issue #9 Step 5
        edge_metrics = self._calculate_edge_frame_quality_metrics(smoothed_tensor, weight_tensor, num_frames)
        
        # Log edge frame quality analysis
        logger.info("=== EDGE FRAME QUALITY ANALYSIS ===")
        if 'edge_frame_scores' in edge_metrics and 'center_frame_scores' in edge_metrics:
            edge = edge_metrics['edge_frame_scores'] 
            center = edge_metrics['center_frame_scores']
            comparison = edge_metrics['quality_comparison']
            
            logger.info(f"Edge Frames ({edge['frame_count']}): weight={edge['avg_weight']:.3f}, stability={edge['avg_stability']:.3f}")
            logger.info(f"Center Frames ({center['frame_count']}): weight={center['avg_weight']:.3f}, stability={center['avg_stability']:.3f}")
            logger.info(f"Quality Parity: weight={comparison['weight_parity']:.3f}, stability={comparison['stability_parity']:.3f}")
            logger.info(f"Overall Edge Quality Score: {comparison['overall_edge_quality']:.3f}/1.0")
            
            # Log recommendations
            for rec in edge_metrics['recommendations']:
                if "good" in rec.lower():
                    logger.info(f"✅ {rec}")
                else:
                    logger.warning(f"⚠️ {rec}")
        
        # Log traditional weight ratio for comparison  
        if num_frames > 0:
            edge_weight = (weight_tensor[0] + weight_tensor[num_frames-1]) / 2
            center_weight = weight_tensor[num_frames//2]
            logger.info(f"Traditional edge/center weight ratio: {edge_weight/center_weight:.2f}")
        
        logger.info(f"Sliding window processing complete: {window_count} windows processed")
        logger.info(f"Window coverage: frames 0-{num_frames-1} processed with {window_size}-frame windows")
        
        # Move back to CPU for further processing
        return smoothed_tensor.cpu()
    
    # DEPRECATED: Using _load_smoothnet_weights from S3 instead
    # def _download_smoothnet_checkpoint(self, checkpoint_path: str) -> bool:
    #     """
    #     Download pre-trained SmoothNet checkpoint if not already present
    #     
    #     Args:
    #         checkpoint_path: Path to save the checkpoint
    #         
    #     Returns:
    #         bool: True if checkpoint is available, False otherwise
    #     """
        import os
        import urllib.request
        
        # For now, we'll use the pw3d_spin_3D checkpoint with window size 16
        # This is trained on 3DPW dataset with SPIN backbone
        checkpoint_url = "https://drive.google.com/uc?export=download&id=1AsOm10AReDKt4HSVAQ0MsZ1Fp-_18IV3"
        
        # Check if checkpoint already exists
        if os.path.exists(checkpoint_path):
            logger.info(f"SmoothNet checkpoint already exists at {checkpoint_path}")
            return True
        
        try:
            logger.info(f"Downloading SmoothNet checkpoint to {checkpoint_path}...")
            # Note: Direct Google Drive download requires special handling
            # For production, we should host this on S3 or another reliable source
            
            # For now, return False to use untrained model
            # TODO: Implement proper checkpoint download from reliable source
            logger.warning("Pre-trained checkpoint download not yet implemented")
            return False
            
        except Exception as e:
            logger.error(f"Failed to download SmoothNet checkpoint: {e}")
            return False
    
    def _create_supabase_queue_entry(self, analysis_id: str, video_id: str, view_type: str, 
                                    s3_key: str, user_email: str = "<EMAIL>") -> Optional[str]:
        """Create entry in bio_modal_processing_queue"""
        if not self.supabase:
            logger.warning("Supabase client not initialized - skipping queue creation")
            return None
            
        try:
            # Create queue entry
            queue_data = {
                'analysis_id': analysis_id,
                'video_id': video_id if video_id else self._create_test_video_record(view_type, user_email),
                'view_type': view_type,
                'status': 'queued',
                'modal_job_id': os.environ.get('MODAL_TASK_ID', f'local-{datetime.now().isoformat()}'),
                'modal_function_name': 'process_running_video',
                'modal_request_payload': {
                    's3_key': s3_key,
                    'model_type': 'heavy' if view_type == 'side' else 'full',
                    'view_type': view_type,
                    'user_email': user_email,
                    'analysis_id': analysis_id
                },
                'queued_at': datetime.utcnow().isoformat()
            }
            
            result = self.supabase.table('bio_modal_processing_queue').insert(queue_data).execute()
            queue_id = result.data[0]['id']
            logger.info(f"Created queue entry: {queue_id} for analysis: {analysis_id}")
            return queue_id
            
        except Exception as e:
            logger.error(f"Failed to create Supabase queue entry: {e}")
            return None
    
    def _update_supabase_queue(self, queue_id: str, status: str, error_message: str = None, 
                              modal_job_id: str = None, response_payload: Dict = None) -> bool:
        """Update Modal processing queue status in Supabase"""
        if not self.supabase:
            logger.warning("Supabase client not initialized - skipping queue update")
            return False
            
        try:
            update_data = {
                'status': status,
                'updated_at': datetime.now().isoformat()
            }
            
            if status == 'processing':
                update_data['started_at'] = datetime.now().isoformat()
                if modal_job_id:
                    update_data['modal_job_id'] = modal_job_id
                    
            elif status == 'completed':
                update_data['completed_at'] = datetime.now().isoformat()
                if response_payload:
                    update_data['modal_response_payload'] = json.dumps(response_payload)
                    
            elif status == 'failed':
                update_data['completed_at'] = datetime.now().isoformat()
                if error_message:
                    update_data['error_message'] = error_message
                    update_data['error_details'] = {'timestamp': datetime.now().isoformat()}
            
            # Update the queue record
            result = self.supabase.table('bio_modal_processing_queue') \
                .update(update_data) \
                .eq('id', queue_id) \
                .execute()
            
            logger.info(f"Updated queue {queue_id} to status: {status}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update Supabase queue: {e}")
            return False
    
    def _update_supabase_analysis(self, analysis_id: str, view_type: str, 
                                 output_data: Dict, s3_url: str, 
                                 processing_time: float, frames_processed: int,
                                 user_email: str = None) -> bool:
        """Update analysis results in Supabase"""
        if not self.supabase:
            logger.warning("Supabase client not initialized - skipping analysis update")
            return False
            
        try:
            # Prepare update data based on view type
            update_data = {
                'updated_at': datetime.now().isoformat()
            }
            
            # Store the full analysis JSON
            if view_type == 'side':
                update_data['side_analysis_json'] = output_data
                update_data['side_metrics_s3_url'] = s3_url
                update_data['side_frames_processed'] = frames_processed
                # Calculate average confidence
                total_confidence = sum(
                    kp['score'] for frame in output_data['frames'] 
                    for kp in frame['keypoints']
                )
                avg_confidence = total_confidence / (frames_processed * 33) if frames_processed > 0 else 0
                update_data['side_detection_confidence'] = avg_confidence
            else:  # rear
                update_data['rear_analysis_json'] = output_data
                update_data['rear_metrics_s3_url'] = s3_url
                update_data['rear_frames_processed'] = frames_processed
                # Calculate average confidence
                total_confidence = sum(
                    kp['score'] for frame in output_data['frames'] 
                    for kp in frame['keypoints']
                )
                avg_confidence = total_confidence / (frames_processed * 33) if frames_processed > 0 else 0
                update_data['rear_detection_confidence'] = avg_confidence
            
            # Update processing metadata
            update_data['modal_job_id'] = os.environ.get('MODAL_TASK_ID', 'local')
            update_data['processing_duration_seconds'] = int(processing_time)
            update_data['model_version'] = f"blazepose_{'heavy' if view_type == 'side' else 'full'}_smoothnet"
            update_data['smoothnet_version'] = 'v1.0'
            
            # Try to get existing record first
            try:
                analysis = self.supabase.table('bio_run_analysis') \
                    .select('side_analysis_json', 'rear_analysis_json', 'processing_started_at') \
                    .eq('id', analysis_id) \
                    .single() \
                    .execute()
                
                existing_data = analysis.data
                logger.info(f"Found existing analysis record for {analysis_id}")
            except Exception as e:
                logger.info(f"No existing analysis record found for {analysis_id}, will create new one")
                existing_data = None
            
            # Determine status based on completion
            if existing_data:
                side_complete = bool(existing_data.get('side_analysis_json')) or view_type == 'side'
                rear_complete = bool(existing_data.get('rear_analysis_json')) or view_type == 'rear'
                
                # Set processing started time if this is the first view
                if not existing_data.get('processing_started_at'):
                    update_data['processing_started_at'] = datetime.now().isoformat()
            else:
                # New record - this is the first view
                side_complete = view_type == 'side'
                rear_complete = view_type == 'rear'
                
                # Use single timestamp to avoid constraint violations
                current_time = datetime.now().isoformat()
                update_data['created_at'] = current_time
                update_data['processing_started_at'] = current_time
                update_data['id'] = analysis_id  # Include ID for upsert
                
                # Add required fields for new records
                update_data['user_email'] = user_email if user_email else '<EMAIL>'
                
                # Add required biometric fields for testing (until proper user profile integration)
                update_data['height_inches'] = 70.0  # 5'10"
                update_data['height_display'] = "5'10\""
                update_data['gender'] = 'male'
                update_data['weight_lbs'] = 165.0
                update_data['weight_display'] = "165 lbs"
                
                # Handle video reference constraint - create test video record first
                test_video_id = self._create_test_video_record(view_type, user_email)
                if test_video_id:
                    if view_type == 'side':
                        update_data['side_video_id'] = test_video_id
                    else:
                        update_data['rear_video_id'] = test_video_id
                    logger.info(f"Using test video ID: {test_video_id} for {view_type} view")
                else:
                    # This will likely fail the constraint, but let's try
                    logger.error("Failed to create test video record - analysis creation may fail due to constraint")
            
            # Update status based on completion
            if side_complete and rear_complete:
                update_data['status'] = 'completed'
                update_data['processing_completed_at'] = datetime.now().isoformat()
            elif view_type == 'side':
                update_data['status'] = 'processing_rear'
            else:
                update_data['status'] = 'processing_side'
            
            # Upsert the analysis record (update if exists, create if not)
            result = self.supabase.table('bio_run_analysis') \
                .upsert(update_data) \
                .execute()
            
            logger.info(f"Updated analysis {analysis_id} with {view_type} results")
            return True
            
        except Exception as e:
            logger.error(f"Failed to update Supabase analysis: {e}")
            return False
    
    def _create_test_video_record(self, view_type: str, user_email: str) -> str:
        """Create a dummy video record for testing purposes"""
        if not self.supabase:
            return None
            
        try:
            video_data = {
                'id': str(uuid.uuid4()),
                'filename': f'test_{view_type}_video.mov',
                'original_filename': f'test_{view_type}_video.mov',
                'file_size_bytes': 50000000,  # 50MB
                'mime_type': 'video/quicktime',
                's3_bucket': 'maxwattz-videos',
                's3_key': f'maxwattz-running-videos-raw-{view_type}/test_video.mov',
                's3_url': f'https://maxwattz-videos.s3.amazonaws.com/maxwattz-running-videos-raw-{view_type}/test_video.mov',
                's3_upload_completed': True,
                'duration_seconds': 10.0,
                'width': 1080,
                'height': 1920,
                'fps': 30.0,
                'view_type': view_type,
                'user_email': user_email if user_email else '<EMAIL>',
                'session_id': 'test-session-001'
            }
            
            result = self.supabase.table('bio_run_videos').upsert(video_data).execute()
            logger.info(f"Created test video record: {video_data['id']}")
            return video_data['id']
            
        except Exception as e:
            logger.warning(f"Failed to create test video record: {e}")
            return None
    
    def _validate_or_create_analysis_id(self, analysis_id: str) -> str:
        """Validate UUID or create a test UUID for testing purposes"""
        try:
            # Try to parse as UUID
            uuid.UUID(analysis_id)
            return analysis_id
        except ValueError:
            # If not a valid UUID, create a test UUID based on the input
            if analysis_id.startswith('test-'):
                # Create deterministic test UUID from string
                test_namespace = uuid.UUID('12345678-1234-5678-1234-123456789012')
                test_uuid = str(uuid.uuid5(test_namespace, analysis_id))
                logger.info(f"Created test UUID: {analysis_id} -> {test_uuid}")
                return test_uuid
            else:
                # Generate random UUID for other cases
                new_uuid = str(uuid.uuid4())
                logger.info(f"Generated new UUID: {analysis_id} -> {new_uuid}")
                return new_uuid

    @modal.method()
    def process(
        self, 
        video_s3_key: str, 
        analysis_id: str,
        view_type: str = 'side',
        queue_id: str = None,
        video_id: str = None,
        user_email: str = "<EMAIL>",  # Default test email
        height_inches: float = 70.0,  # Default 5'10" for testing
        disable_smoothnet: bool = False
    ) -> Dict:
        """
        Main processing method
        
        Args:
            video_s3_key: S3 key for the video (e.g., "maxwattz-running-videos-raw-side/video.mp4")
            analysis_id: UUID for this analysis session (or test string starting with 'test-')
            view_type: 'side' or 'rear' for model selection
            queue_id: Optional queue ID for Supabase status updates (will create if not provided)
            video_id: Optional video record ID (will create test record if not provided)
            user_email: User email for record association (default: <EMAIL>)
            height_inches: Runner height in inches for biomechanics calculations
            disable_smoothnet: Skip SmoothNet processing for testing
        
        Returns:
            Dict with processing results and S3 output path
        """
        # Validate or create proper UUID
        validated_analysis_id = self._validate_or_create_analysis_id(analysis_id)
        logger.info(f"Processing analysis: {analysis_id} (UUID: {validated_analysis_id})")
        import cv2
        import numpy as np
        
        start_time = datetime.now()
        logger.info(f"Starting processing for {video_s3_key} (view: {view_type})")
        logger.info(f"Using user email: {user_email}")
        
        # Create queue entry if not provided
        if not queue_id and self.supabase:
            logger.info("No queue_id provided, creating new queue entry")
            queue_id = self._create_supabase_queue_entry(
                analysis_id=validated_analysis_id,
                video_id=video_id,
                view_type=view_type,
                s3_key=video_s3_key,
                user_email=user_email
            )
            if queue_id:
                logger.info(f"Created queue entry: {queue_id}")
            else:
                logger.warning("Failed to create queue entry, continuing without database updates")
        
        # Update queue status to processing
        if queue_id:
            modal_job_id = os.environ.get('MODAL_TASK_ID', f'local-{datetime.now().isoformat()}')
            self._update_supabase_queue(queue_id, 'processing', modal_job_id=modal_job_id)
        
        try:
            # Construct full path
            video_path = os.path.join(MOUNT_PATH, video_s3_key)
            
            # Extract metadata
            metadata = self._extract_video_metadata(video_path)
            
            # Initialize pose model with appropriate complexity
            model_complexity = self._get_model_complexity(view_type)
            logger.info(f"Initializing BlazePose with complexity: {model_complexity} ({'Heavy' if model_complexity == 2 else 'Full' if model_complexity == 1 else 'Lite'})")
            
            pose_model = self.mp_pose.Pose(
                model_complexity=model_complexity,
                enable_segmentation=False,
                smooth_landmarks=False,  # We'll use SmoothNet instead
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            
            # Verify model loaded correctly
            try:
                # Test with a dummy frame to ensure model works
                import numpy as np
                test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                test_result = pose_model.process(test_frame)
                logger.info("BlazePose model verified and ready")
            except Exception as model_error:
                logger.error(f"BlazePose model verification failed: {model_error}")
                pose_model.close()
                raise RuntimeError(f"Failed to initialize BlazePose {['Lite', 'Full', 'Heavy'][model_complexity]} model: {model_error}")
            
            # Determine frame sampling based on FPS
            frame_skip = 2 if metadata['fps'] > 50 else 1  # Sample every 2nd frame for 60fps
            
            # Process video frames
            cap = cv2.VideoCapture(video_path)
            frames_data = []
            frame_count = 0
            processed_count = 0
            
            while cap.isOpened():
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Apply frame sampling
                if frame_count % frame_skip == 0:
                    keypoints = self._process_frame(frame, pose_model, metadata['width'], metadata['height'])
                    
                    frame_data = {
                        'frameNumber': processed_count,
                        'timestamp': processed_count / (metadata['fps'] / frame_skip),  # Correct timestamp for sampled frames
                        'keypoints': keypoints if keypoints else []
                    }
                    frames_data.append(frame_data)
                    processed_count += 1
                
                frame_count += 1
                
                # Log progress every 30 frames
                if frame_count % 30 == 0:
                    logger.info(f"Processed {frame_count}/{metadata['frame_count']} frames")
            
            cap.release()
            pose_model.close()
            
            logger.info(f"Processing complete: {processed_count} frames processed from {frame_count} total frames")
            
            # Apply SmoothNet temporal smoothing (unless disabled)
            if disable_smoothnet:
                logger.info("SmoothNet disabled - using raw BlazePose coordinates")
            else:
                logger.info("Starting SmoothNet temporal smoothing...")
                frames_data = self._apply_smoothnet(frames_data, metadata['width'], metadata['height'])
            
            # Prepare output JSON
            output_data = {
                'video': os.path.basename(video_s3_key),
                'videoWidth': metadata['width'],
                'videoHeight': metadata['height'],
                'fps': metadata['fps'],
                'modelType': f"{'heavy' if view_type == 'side' else 'full'}_smoothnet",
                'analysisId': validated_analysis_id,
                'viewType': view_type,
                'processingTime': (datetime.now() - start_time).total_seconds(),
                'frames': frames_data
            }
            
            # Determine output path using UUID to avoid overwriting files
            output_filename = f"{validated_analysis_id}_pose.json"
            output_s3_key = f"maxwattz-running-metrics-{view_type}/{output_filename}"
            output_path = os.path.join(MOUNT_PATH, output_s3_key)
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Write JSON to S3
            with open(output_path, 'w') as f:
                json.dump(output_data, f)
            
            logger.info(f"Processing complete. Output saved to: {output_s3_key}")
            
            # Update Supabase with results
            processing_time = output_data['processingTime']
            s3_url = f"https://maxwattz-videos.s3.amazonaws.com/{output_s3_key}"
            
            # Update analysis record
            self._update_supabase_analysis(
                analysis_id=validated_analysis_id,
                view_type=view_type,
                output_data=output_data,
                s3_url=s3_url,
                processing_time=processing_time,
                frames_processed=processed_count,
                user_email=user_email
            )
            
            # Update queue status to completed
            if queue_id:
                response_payload = {
                    'output_s3_key': output_s3_key,
                    'frames_processed': processed_count,
                    'processing_time': processing_time
                }
                self._update_supabase_queue(queue_id, 'completed', response_payload=response_payload)
            
            return {
                'success': True,
                'output_s3_key': output_s3_key,
                'frames_processed': processed_count,
                'processing_time': processing_time,
                's3_url': s3_url
            }
            
        except Exception as e:
            logger.error(f"Processing failed: {str(e)}")
            
            # Update queue status to failed
            if queue_id:
                self._update_supabase_queue(queue_id, 'failed', error_message=str(e))
            
            return {
                'success': False,
                'error': str(e),
                'processing_time': (datetime.now() - start_time).total_seconds()
            }

# 4. Local entrypoint for testing
@app.local_entrypoint()
def main(
    video: str,
    analysis_id: str = None,
    view_type: str = "side",
    queue_id: str = None,
    video_id: str = None,
    user_email: str = "<EMAIL>",
    height_inches: float = 70.0,
    disable_smoothnet: bool = False
):
    """
    Entry point for CLI testing
    
    Args:
        video: S3 key or path within bucket
        analysis_id: Analysis session ID (if None, will create test UUID)
        view_type: 'side' or 'rear'
        queue_id: Optional queue ID for status tracking (will create if not provided)
        video_id: Optional video record ID (will create test record if not provided)
        user_email: User email for record association (default: <EMAIL>)
        height_inches: Runner height in inches (default: 70.0 = 5'10")
        disable_smoothnet: Skip SmoothNet temporal smoothing (for debugging)
    """
    logger.info(f"Starting pose inference for: {video}")
    if disable_smoothnet:
        logger.info("SmoothNet DISABLED - using raw BlazePose coordinates")
    
    if not analysis_id:
        logger.warning("No analysis_id provided - Supabase updates will be skipped")
        analysis_id = f"test-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
    
    # Ensure proper S3 key format
    if not video.startswith("maxwattz-running-videos-"):
        video = f"maxwattz-running-videos-raw-{view_type}/{video}"
    
    # Process video
    result = PoseEstimator().process.remote(
        video_s3_key=video, 
        analysis_id=analysis_id, 
        view_type=view_type, 
        queue_id=queue_id,
        video_id=video_id,
        user_email=user_email,
        height_inches=height_inches,
        disable_smoothnet=disable_smoothnet
    )
    
    if result['success']:
        print(f"✅ Success! Results saved to: {result['output_s3_key']}")
        print(f"   Frames processed: {result['frames_processed']}")
        print(f"   Processing time: {result['processing_time']:.2f}s")
        print(f"   S3 URL: {result.get('s3_url', 'N/A')}")
        if analysis_id and not analysis_id.startswith('test-'):
            print(f"   Analysis ID: {analysis_id}")
    else:
        print(f"❌ Failed: {result['error']}")

if __name__ == "__main__":
    # For testing without Modal CLI
    import sys
    if len(sys.argv) > 1:
        video_path = sys.argv[1]
        main(video_path)