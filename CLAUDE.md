# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## You Must Read CLAUDEASSIST.md

## Repository Overview

This is a customized fork of the TensorFlow.js Models repository, containing pre-trained machine learning models ported to JavaScript. The repository has been heavily modified to focus on pose detection, particularly the BlazePose Full model implementation.

## Common Development Commands

### Building and Testing

**From the root directory:**

```bash
# Run all tests across the repository
npm run presubmit

# Link packages locally for development
npm run link-local
```

**For individual models (e.g., pose-detection):**

```bash
# Navigate to model directory
cd pose-detection

# Build the model
yarn build

# Run tests
yarn test           # Browser tests (Chrome)
yarn test-node      # Node.js tests
yarn test-ci        # CI tests on BrowserStack

# Lint the code
yarn lint

# Development workflow
yarn build && yarn test
```

### Running a Single Test

To run specific tests, use the test flag:

```bash
# In pose-detection directory
yarn test --grep "test name pattern"

# For Node tests
yarn test-node -- --grep "test name pattern"
```

## High-Level Architecture

### Repository Structure

The repository follows a monorepo pattern with each model in its own directory. Currently, the main focus is on the `pose-detection` model with custom BlazePose implementations.

### Pose Detection Architecture

The pose-detection module implements a factory pattern with a common `PoseDetector` interface:

```text
PoseDetector (interface)
├── BlazePoseTfjsDetector    # Custom TFJS implementation
├── BlazePoseMediaPipeDetector
├── MoveNetDetector
└── PoseNetDetector
```

**Key Processing Pipeline:**

1. **Detection Phase**: Find person bounding box using detector model
2. **Landmark Phase**: Extract pose keypoints within detected region
3. **Post-processing**: Apply smoothing, coordinate conversion, and scoring

### Custom BlazePose Full Model Features

This repository contains significant customizations for BlazePose Full (39 landmarks):

- **Extended Keypoints**: Support for 39 landmarks (vs standard 33)
- **Performance Monitoring**: Custom tracking system for optimization
- **Memory Management**: Explicit tensor disposal and memory optimization
- **Bug Fixes**: Fixed coordinate transformation and tensor processing issues

### Critical Modified Files

The following shared calculators have been heavily customized and should be handled with care:

- `shared/calculators/tensors_to_detections.ts` - Performance monitoring added
- `shared/calculators/detector_result.ts` - Fixed coordinate order bug
- `shared/calculators/tensors_to_landmarks.ts` - Fixed 5D tensor score extraction
- `shared/calculators/normalized_keypoints_to_keypoints.ts` - Fixed hardcoded coordinates

### Model Configuration

Each model variant has specific tensor specifications:

- **Lite**: 33 keypoints, basic performance
- **Full**: 39 keypoints, enhanced accuracy (custom implementation)
- **Heavy**: 33 keypoints, highest accuracy

### Testing Considerations

- Tests use Jasmine framework with Karma runner
- BrowserStack configuration available for cross-browser testing
- Test data stored in `test_data/` directories
- MediaPipe resources served from `demos/` for integration tests

### Development Workflow

1. Make changes in the appropriate model directory
2. Run `yarn lint` to check code style
3. Run `yarn build` to compile TypeScript
4. Run `yarn test` for browser tests
5. Run `yarn test-node` for Node.js tests
6. Run `npm run presubmit` from root to validate all packages

### Important Notes

- Each model has independent package.json with standardized scripts
- TensorFlow.js dependencies must use caret (^) versioning
- Shared utilities in `/shared` are used across multiple models
- Performance is critical - always dispose tensors properly
- The repository has diverged significantly from upstream tfjs-models
