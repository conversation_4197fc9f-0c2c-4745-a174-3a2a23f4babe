[
  {
    "table_name": "pose_data",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "is_primary_key": true,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "session_id",
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": "pose_sessions",
    "target_column": "id",
    "foreign_key_name": "pose_data_session_id_fkey"
  },
  {
    "table_name": "pose_data",
    "column_name": "frame_number",
    "data_type": "integer",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "timestamp_seconds",
    "data_type": "real",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "detection_confidence",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "pose_detected",
    "data_type": "boolean",
    "is_nullable": "YES",
    "column_default": "false",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "trunk_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "neck_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "trunk_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "trunk_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "neck_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "neck_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "stride_length",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "foot_strike_type",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "posture_score",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "raw_keypoints",
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "elbow_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "elbow_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "wrist_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "wrist_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "heel_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "heel_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "foot_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "foot_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_left_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_left_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_right_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_right_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "elbow_left_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "elbow_left_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "elbow_right_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "elbow_right_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "wrist_left_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "wrist_left_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "wrist_right_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "wrist_right_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_left_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_left_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_right_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_right_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_left_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_left_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_right_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_right_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_left_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_left_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_right_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_right_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "detection_quality",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "bilateral_symmetry",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_world_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_world_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "hip_world_z",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_world_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_world_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "knee_world_z",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_world_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_world_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "ankle_world_z",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_world_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_world_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "shoulder_world_z",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "estimated_distance_meters",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "depth_scale_factor",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "pixels_per_meter",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "world_center_x",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "world_center_y",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_data",
    "column_name": "world_center_z",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "is_primary_key": true,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "session_id",
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": "pose_sessions",
    "target_column": "id",
    "foreign_key_name": "pose_metrics_session_id_fkey"
  },
  {
    "table_name": "pose_metrics",
    "column_name": "metric_name",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "metric_category",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "value",
    "data_type": "real",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "unit",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "score",
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "calculation_method",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "confidence_level",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "notes",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "start_time_seconds",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_metrics",
    "column_name": "end_time_seconds",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "is_primary_key": true,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "session_id",
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": "pose_sessions",
    "target_column": "id",
    "foreign_key_name": "pose_recommendations_session_id_fkey"
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "category",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "priority",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "'medium'::text",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "title",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "description",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "equipment_type",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "specific_recommendations",
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "supporting_metrics",
    "data_type": "jsonb",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_recommendations",
    "column_name": "confidence_score",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "is_primary_key": true,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "video_id",
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": "pose_videos",
    "target_column": "id",
    "foreign_key_name": "pose_sessions_video_id_fkey"
  },
  {
    "table_name": "pose_sessions",
    "column_name": "model_type",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": "'BlazePose'::text",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "model_version",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "analysis_fps",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": "10",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "total_frames_analyzed",
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "successful_detections",
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "detection_rate",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_hip_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_knee_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_ankle_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_trunk_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_neck_angle",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_stride_length",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "avg_posture_score",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "processing_time_seconds",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "analysis_notes",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_sessions",
    "column_name": "user_height_meters",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": "1.78",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "id",
    "data_type": "uuid",
    "is_nullable": "NO",
    "column_default": "gen_random_uuid()",
    "is_primary_key": true,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "created_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "updated_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "NO",
    "column_default": "timezone('utc'::text, now())",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "filename",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "file_path",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "file_size",
    "data_type": "bigint",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "duration",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "width",
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "height",
    "data_type": "integer",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "fps",
    "data_type": "real",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "activity_type",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "view_type",
    "data_type": "text",
    "is_nullable": "NO",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "processing_status",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": "'uploaded'::text",
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "processing_started_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "processing_completed_at",
    "data_type": "timestamp with time zone",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "processing_error",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "user_id",
    "data_type": "uuid",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
  {
    "table_name": "pose_videos",
    "column_name": "session_id",
    "data_type": "text",
    "is_nullable": "YES",
    "column_default": null,
    "is_primary_key": false,
    "target_table": null,
    "target_column": null,
    "foreign_key_name": null
  },
]
