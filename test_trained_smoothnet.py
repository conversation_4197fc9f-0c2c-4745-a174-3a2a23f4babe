#!/usr/bin/env python3
"""
Test Modal pose inference with trained SmoothNet model
"""
import modal
import sys

# Import the Modal app
sys.path.append('.')
from pose_inference import app, PoseEstimator

@app.local_entrypoint()
def test_trained_model():
    """Test with trained SmoothNet weights"""
    print("\n🚀 Testing Modal Pose Inference with TRAINED SmoothNet (3DPW-SPIN-3D)")
    print("=" * 70)
    
    # Test parameters - correct parameter names for the process() method
    test_params = {
        'video_s3_key': 'maxwattz-running-videos-raw-side/Michael_test_side.mp4',
        'analysis_id': 'trained-smoothnet-test',
        'view_type': 'side',
        'disable_smoothnet': False,  # Enable SmoothNet with trained weights
    }
    
    print(f"\nTest Parameters:")
    for key, value in test_params.items():
        print(f"  {key}: {value}")
    
    print(f"\n⏳ Starting pose estimation with trained SmoothNet...")
    
    try:
        # Call the Modal function - correct method name is 'process'
        estimator = PoseEstimator()
        result = estimator.process.remote(**test_params)
        
        print(f"\n✅ Processing completed!")
        print(f"Output saved to: {result.get('output_s3_key', 'Unknown')}")
        
        # Download result for analysis
        import boto3
        import json
        
        s3 = boto3.client('s3')
        output_file = 'trained_smoothnet_results.json'
        
        if result.get('output_s3_key'):
            print(f"\n📥 Downloading results to {output_file}...")
            s3.download_file(
                'maxwattz-videos',
                result['output_s3_key'],
                output_file
            )
            
            # Quick analysis
            with open(output_file, 'r') as f:
                data = json.load(f)
            
            print(f"\n📊 Quick Analysis:")
            print(f"Total frames: {len(data.get('frames', []))}")
            
            # Check first few frames for coordinate ranges
            frames = data.get('frames', [])
            if frames:
                print(f"\nSample coordinates from first valid frame:")
                for frame in frames[:5]:
                    keypoints = frame.get('keypoints', [])
                    valid_kps = [kp for kp in keypoints if kp['x'] != 0 or kp['y'] != 0]
                    
                    if valid_kps:
                        avg_x = sum(kp['x'] for kp in valid_kps) / len(valid_kps)
                        avg_y = sum(kp['y'] for kp in valid_kps) / len(valid_kps)
                        print(f"  Frame {frame['frameNumber']}: Center at ({avg_x:.1f}, {avg_y:.1f})")
                        
                        # Check if coordinates are in expected range
                        if avg_x > 400 and avg_x < 700 and avg_y > 800 and avg_y < 1100:
                            print(f"    ✅ Coordinates look correct!")
                        else:
                            print(f"    ⚠️  Coordinates may still be off")
                        break
        
        return result
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_trained_model()