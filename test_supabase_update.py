"""
Test Supabase updates from Modal
"""

import modal
import os
import json
from datetime import datetime

# Define image with Supabase dependency
image = modal.Image.debian_slim(python_version="3.11").pip_install([
    "supabase>=2.0.0",
    "psycopg2-binary>=2.9.0"
])

# Configure Modal app with Supabase secret
app = modal.App(
    name="maxwattz-supabase-test",
    image=image,
    secrets=[
        modal.Secret.from_name("maxwattz-supabase"),
    ]
)

@app.function()
def test_supabase_connection():
    """Test Supabase connection and perform test updates"""
    import os
    from supabase import create_client, Client
    
    print("Testing Supabase connection...")
    
    # Get credentials
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_SERVICE_KEY')
    
    print(f"SUPABASE_URL: {supabase_url[:30]}..." if supabase_url else "SUPABASE_URL: Not found")
    print(f"SUPABASE_SERVICE_KEY: {'***' if supabase_key else 'Not found'}")
    
    if not supabase_url or not supabase_key:
        return {"success": False, "error": "Missing Supabase credentials"}
    
    try:
        # Initialize client
        supabase: Client = create_client(supabase_url, supabase_key)
        print("✅ Supabase client created")
        
        # Test 1: Check if we can query the bio_run_analysis table
        print("\nTest 1: Querying bio_run_analysis table...")
        result = supabase.table('bio_run_analysis').select('id', count='exact').limit(5).execute()
        print(f"Found {result.count if hasattr(result, 'count') else len(result.data)} records")
        
        # Test 2: Try to find the test analysis
        print("\nTest 2: Looking for test-analysis-001...")
        analysis = supabase.table('bio_run_analysis') \
            .select('*') \
            .eq('id', 'test-analysis-001') \
            .execute()
        
        if analysis.data:
            print(f"❌ Found existing test analysis: {analysis.data[0]['id']}")
            print("   This might be why updates aren't working - it's trying to update a non-existent record")
        else:
            print("✅ No test analysis found - this is expected for the default test ID")
        
        # Test 3: Get the most recent analysis
        print("\nTest 3: Getting most recent analysis...")
        recent = supabase.table('bio_run_analysis') \
            .select('id', 'user_email', 'status', 'created_at') \
            .order('created_at', desc=True) \
            .limit(1) \
            .execute()
        
        if recent.data:
            recent_analysis = recent.data[0]
            print(f"Most recent analysis:")
            print(f"  ID: {recent_analysis['id']}")
            print(f"  Email: {recent_analysis['user_email']}")
            print(f"  Status: {recent_analysis['status']}")
            print(f"  Created: {recent_analysis['created_at']}")
            
            # Test 4: Try updating this analysis
            print(f"\nTest 4: Attempting to update analysis {recent_analysis['id']}...")
            test_update = {
                'modal_job_id': f'test-modal-{datetime.now().isoformat()}',
                'processing_started_at': datetime.now().isoformat(),
                'status': 'processing_side',
                'updated_at': datetime.now().isoformat()
            }
            
            update_result = supabase.table('bio_run_analysis') \
                .update(test_update) \
                .eq('id', recent_analysis['id']) \
                .execute()
            
            if update_result.data:
                print("✅ Update successful!")
                print(f"   Updated fields: {list(test_update.keys())}")
            else:
                print("❌ Update failed - no data returned")
                
        else:
            print("No analyses found in database")
        
        return {"success": True, "tests_completed": 4}
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

@app.local_entrypoint()
def main():
    result = test_supabase_connection.remote()
    print(f"\nFinal result: {result}")

if __name__ == "__main__":
    main()