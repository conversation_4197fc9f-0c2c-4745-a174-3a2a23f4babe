# Coordinate Space Fix Summary

## Problem Identified
SmoothNet was receiving normalized pixel coordinates (0-1 range) instead of the world coordinates it was trained on, causing severe coordinate displacement.

## Solution Implemented

### 1. World Coordinate Conversion
- **Changed from**: Normalized pixel coordinates (0-1 range)
- **Changed to**: World coordinates in millimeters
- **Scaling**: Assumes video represents ~2m wide space (2000mm / video_width)

### 2. Root-Relative Coordinates  
- **Root joint**: Pelvis center (average of left_hip and right_hip keypoints)
- **Method**: All keypoints converted to relative positions from pelvis
- **Benefit**: Common format in 3D pose estimation, reduces coordinate variance

### 3. Coordinate Pipeline

#### Input Processing (pixel → world → root-relative):
```python
# 1. Convert pixel to world coordinates
pixel_to_world_scale = 2000.0 / video_width
world_x = (pixel_x - video_width / 2) * pixel_to_world_scale
world_y = (pixel_y - video_height / 2) * pixel_to_world_scale
world_z = pixel_z * pixel_to_world_scale

# 2. Calculate pelvis root position
root_x = (left_hip_world_x + right_hip_world_x) / 2
root_y = (left_hip_world_y + right_hip_world_y) / 2
root_z = (left_hip_world_z + right_hip_world_z) / 2

# 3. Convert to root-relative
rel_x = world_x - root_x
rel_y = world_y - root_y
rel_z = world_z - root_z
```

#### Output Processing (root-relative → world → pixel):
```python
# 1. Add back root position
world_x = rel_x + root_x
world_y = rel_y + root_y 
world_z = rel_z + root_z

# 2. Convert back to pixel coordinates
pixel_x = (world_x / pixel_to_world_scale) + video_width / 2
pixel_y = (world_y / pixel_to_world_scale) + video_height / 2
pixel_z = world_z / pixel_to_world_scale
```

## Expected Benefits

1. **Coordinate Accuracy**: SmoothNet now receives data in the same format it was trained on
2. **Reduced Displacement**: Root-relative coordinates should eliminate major positioning errors
3. **Proper Scaling**: World coordinates match the magnitude SmoothNet expects
4. **Temporal Consistency**: Root normalization reduces motion artifacts

## Key Changes Made

### Files Modified:
- `pose_inference.py` - `_convert_to_smoothnet_format()` function
- `pose_inference.py` - `_convert_from_smoothnet_format()` function

### Logging Added:
- Sample world coordinates for first frame
- Root-relative coordinate ranges (X, Y, Z in mm)
- Input/output tensor shape verification

## Testing Required

1. **Coordinate Validation**: Verify output keypoints are near expected center-frame position (~540, 960)
2. **Range Checking**: Ensure root-relative coordinates are reasonable (-500 to +500mm typical)
3. **Temporal Smoothness**: Confirm SmoothNet reduces jitter compared to raw keypoints
4. **Hip Position**: Verify pelvis root calculation maintains proper body proportions

## Next Steps

1. Test with updated coordinate space conversion
2. Implement 32-frame sliding window (currently using 16-frame)
3. Load official SmoothNet config files for proper architecture
4. Validate against expected center-frame positioning

---

**Status**: Coordinate space conversion implemented ✅  
**Expected Impact**: Should resolve major coordinate displacement issues  
**Risk**: Root calculation requires valid hip keypoints (indices 23, 24)