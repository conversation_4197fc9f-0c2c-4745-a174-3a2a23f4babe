import modal

app = modal.App(image=modal.Image.debian_slim().pip_install("boto3"))


@app.function(secrets=[modal.Secret.from_name("maxwattz-videos")])
def read_s3_file(bucket, key):
    import boto3

    s3 = boto3.client("s3")
    response = s3.get_object(Bucket=bucket, Key=key)
    contents = response["Body"].read()
    return contents.decode("utf-8")


@app.local_entrypoint()
def main():
    read_s3_file.remote("modal-public-assets", "hello.txt")
