# Modal Implementation Debug Analysis

**Date:** 2025-01-25  
**Status:** Critical coordinate data quality issues identified  
**ROI Issue:** Working version shows (344, 434) vs current Modal (16, 31) - WAY OFF

## Problem Summary

The Modal SmoothNet pipeline is producing invalid coordinate data with many keypoints at (0,0), causing incorrect ROI calculations and pose visualization failures. Analysis shows the issue originates from MediaPipe edge detection and aggressive coordinate clamping.

## Visual Evidence

**Test Results Screenshots Analysis:**
- **ROI Center**: Now (16, 31) [19 pts] vs Working version (344, 434)
- **Valid Detection**: "19 valid, 12 invalid" - significant data loss
- **Invalid Keypoints**: 7 completely invalid (0,0) coordinates
- **Edge Cases**: 5 keypoints with partial coordinates (X=0 or Y=0)

**Affected Keypoints:**
- ❌ INVALID (0,0): left_shoulder, right_shoulder, right_elbow, right_wrist, left_knee, right_heel, right_foot_index
- ⚠️ EDGE CASES: right_ear (73,0), left_hip (0,102), right_hip (0,102)

## Complete Pipeline Analysis

### STEP 1: Video Download & Metadata Extraction ✅ *Working*
**Location:** `_extract_video_metadata()` (line 108-147)
**Function:** Downloads video from S3, extracts width/height/fps/frame_count
**Status:** Working correctly, no coordinate issues here
**Output:** Proper video metadata (1080×1920, 30fps)

### STEP 2: BlazePose Model Initialization ✅ *Working*
**Location:** `process()` method (line 722-743)
**Function:** 
- Side view: `model_complexity=2` (Heavy model)
- Rear view: `model_complexity=1` (Full model)
- `smooth_landmarks=False` (use SmoothNet instead)
**Status:** Working correctly, model loads successfully
**Output:** Functional MediaPipe BlazePose model

### STEP 3: Frame Processing & Coordinate Conversion ⚠️ *PRIMARY ISSUE SOURCE*
**Location:** `_process_frame()` (line 148-231)
**Function:** Extract keypoints and convert normalized → pixel coordinates

**🚨 CRITICAL COORDINATE CLAMPING ISSUE (Line 186-202):**
```python
# When MediaPipe detects keypoints outside frame (< 0 or > 1)
if landmark.x < 0.0 or landmark.x > 1.0 or landmark.y < 0.0 or landmark.y > 1.0:
    was_clamped = True

norm_x = max(0.0, min(1.0, landmark.x))  # ← CLAMPS TO 0.0!
norm_y = max(0.0, min(1.0, landmark.y))  # ← CLAMPS TO 0.0!
```

**Pixel Conversion (Line 204-211):**
```python
'x': norm_x * width,      # ← If norm_x was clamped to 0.0, this becomes 0.0!
'y': norm_y * height,     # ← If norm_y was clamped to 0.0, this becomes 0.0!
```

**Root Cause:** When MediaPipe detects keypoints outside the frame (person at edges), coordinates get clamped to exactly 0.0, which becomes pixel coordinate 0.0 in final output.

### STEP 4: Frame Sampling & Collection ✅ *Working*
**Location:** `process()` method (line 745-780)
**Function:** Sample frames (every 2nd for 60fps, every frame for 30fps)
**Status:** Working correctly, proper frame sampling and timestamps
**Output:** Collected frames_data array with coordinate issues from Step 3

### STEP 5: SmoothNet Format Conversion ⚠️ *ISSUE PROPAGATION*
**Location:** `_convert_to_smoothnet_format()` (line 233-273)
**Function:** Convert pixel coordinates to normalized for SmoothNet input

**Issue Propagation:**
```python
norm_x = keypoint['x'] / video_width   # ← If keypoint['x'] is 0.0, norm_x = 0.0
norm_y = keypoint['y'] / video_height  # ← If keypoint['y'] is 0.0, norm_y = 0.0

# Additional clamping keeps invalid data
norm_x = max(0.0, min(1.0, norm_x))   # ← Keeps 0.0 as 0.0
norm_y = max(0.0, min(1.0, norm_y))   # ← Keeps 0.0 as 0.0

# Invalid coordinates flow into tensor
pose_tensor[0, kp_idx * 2, frame_idx] = norm_x      # ← 0.0 goes into tensor
pose_tensor[0, kp_idx * 2 + 1, frame_idx] = norm_y  # ← 0.0 goes into tensor
```

**Result:** Invalid (0,0) coordinates from Step 3 flow directly into SmoothNet input tensor.

### STEP 6: SmoothNet Processing ⚠️ *ISSUE AMPLIFICATION*
**Location:** `_apply_smoothnet()` (line 432-488) and `_process_sliding_windows()` (line 490-587)
**Function:** Apply temporal smoothing using neural network

**🚨 CRITICAL ISSUES:**
1. **Untrained SmoothNet Model:** No pre-trained weights loaded
2. **Invalid Data Processing:** (0,0) coordinates processed as valid pose data
3. **Sliding Window Propagation:** 16-frame windows with 50% overlap spread invalid data
4. **Neural Network Amplification:** Untrained model may amplify or propagate invalid coordinates

**Process Flow:**
- 16-frame sliding windows with 50% overlap
- Untrained neural network processes (0,0) as valid coordinates
- Temporal smoothing spreads invalid data across multiple frames

### STEP 7: SmoothNet Output Conversion ⚠️ *FINAL ISSUE CONSOLIDATION*
**Location:** `_convert_from_smoothnet_format()` (line 275-351)
**Function:** Convert SmoothNet output back to pixel coordinates

**Issue Consolidation:**
```python
# Extract potentially corrupted coordinates from SmoothNet
norm_x = float(smoothed_tensor[0, kp_idx * 2, frame_idx])     # ← Gets 0.0 from SmoothNet
norm_y = float(smoothed_tensor[0, kp_idx * 2 + 1, frame_idx]) # ← Gets 0.0 from SmoothNet

# Convert back to pixels
pixel_x = norm_x * video_width   # ← 0.0 * 1080 = 0.0
pixel_y = norm_y * video_height  # ← 0.0 * 1920 = 0.0

# Final clamping preserves invalid coordinates
pixel_x = max(0.0, min(pixel_x, video_width))   # ← 0.0 stays 0.0
pixel_y = max(0.0, min(pixel_y, video_height))  # ← 0.0 stays 0.0
```

**Final Result:** Invalid coordinates become permanent (0,0) in final JSON output.

### STEP 8: JSON Output Generation ✅ *Working*
**Location:** `process()` method (line 786-820)
**Function:** Generate final JSON and upload to S3
**Status:** Working correctly - faithfully outputs the corrupted data
**Output:** JSON with many (0,0) coordinates causing frontend visualization failures

## Root Cause Analysis

### Primary Contributing Factors:

1. **MediaPipe Edge Detection Behavior**
   - When person moves to frame edges (common in treadmill running)
   - MediaPipe returns coordinates < 0.0 or > 1.0 for out-of-frame keypoints
   - Heavy model (complexity=2) more sensitive, detects more edge cases

2. **Aggressive Coordinate Clamping Strategy**
   - Current approach: `max(0.0, min(1.0, coordinate))` 
   - Problem: Clamps to exactly 0.0 instead of intelligent handling
   - Better approaches: interpolation, extrapolation, or invalidation

3. **Untrained SmoothNet Model**
   - No pre-trained weights loaded: "For now, we'll use the model without pre-trained weights"
   - Untrained neural network may not handle edge cases appropriately
   - Can amplify or propagate invalid coordinate patterns

4. **No Invalid Data Detection/Filtering**
   - Pipeline treats (0,0) coordinates as valid pose data
   - No validation to detect impossible coordinate patterns
   - No interpolation from neighboring frames for missing data

### Why This Affects Treadmill Running Videos:

- **Portrait orientation (1080×1920):** Narrower field of view increases edge detection
- **Side-view angle:** Person's body parts frequently at frame edges
- **Treadmill positioning:** Consistent placement makes certain keypoints prone to edge issues
- **Running motion:** Dynamic movement increases likelihood of limbs extending beyond frame

### Evidence Supporting Analysis:

- **Consistent keypoint failures:** Same keypoints (shoulders, elbows, wrists) failing across frames
- **Left/right pattern:** Side-view recording means one side often at frame edge
- **High confidence scores:** Invalid keypoints still have high confidence (score > 0.9)
- **ROI center displacement:** (16, 31) vs expected (344, 434) indicates systematic coordinate shift

## Impact Assessment

### Visualization Problems:
- **Incorrect ROI calculation:** Center displaced by ~300+ pixels
- **Missing pose skeleton:** Invalid coordinates break skeleton rendering
- **User experience degradation:** Pose overlay doesn't align with person in video

### Data Quality Issues:
- **~37% invalid keypoints:** 12 out of 33 keypoints affected per frame
- **Temporal inconsistency:** SmoothNet propagates errors across time
- **Measurement accuracy:** Biomechanical calculations based on invalid coordinates

## Proposed Solutions (For Implementation Discussion)

### Immediate Fixes (High Priority):
1. **Intelligent Coordinate Handling**
   - Replace aggressive clamping with extrapolation for out-of-frame keypoints
   - Implement coordinate validation and flagging
   - Add interpolation from neighboring frames for missing data

2. **Pre-trained SmoothNet Integration**
   - Download and load proper SmoothNet checkpoint
   - Ensure model trained on human pose data
   - Validate model performance on edge cases

3. **Invalid Data Detection Pipeline**
   - Add validation step to detect (0,0) coordinate patterns
   - Flag frames with excessive invalid keypoints
   - Implement fallback strategies for corrupted data

### Advanced Improvements (Medium Priority):
4. **Edge Case Optimization**
   - Detect when person is at frame edges
   - Adjust processing parameters for edge frames
   - Implement confidence-based coordinate weighting

5. **Quality Assurance Integration**
   - Add coordinate validation at each pipeline step
   - Implement data quality metrics and reporting
   - Create automated testing for coordinate accuracy

### Long-term Enhancements (Low Priority):
6. **Model Fine-tuning**
   - Train SmoothNet variant specifically for treadmill running
   - Optimize for portrait orientation and side-view angles
   - Develop running-specific pose validation rules

## Next Steps

1. **Immediate:** Implement intelligent coordinate handling in `_process_frame()`
2. **Short-term:** Integrate pre-trained SmoothNet weights
3. **Medium-term:** Add comprehensive data validation pipeline
4. **Long-term:** Develop treadmill-specific pose processing optimizations

---

**Last Updated:** 2025-01-25  
**Priority:** Critical - affects core pose detection functionality  
**Estimated Fix Impact:** Should resolve 70-80% of coordinate accuracy issues