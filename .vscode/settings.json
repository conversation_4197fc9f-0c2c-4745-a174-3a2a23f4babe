// Place your settings in this file to overwrite default and user settings.
{
  "search.exclude": {
    "**/node_modules": true,
    "**/bower_components": true,
    "coverage/": true,
    "dist/": true,
    "**/bundle.js": true,
    "**/yarn.lock": true,
    // shared is symlinked in specific projects so search results should be
    // excluded from root level to avoid duplicates.
    "shared/": true
  },
  "tslint.enable": true,
  "tslint.run": "onType",
  "tslint.configFile": "tslint.json",
  "files.trimTrailingWhitespace": true,
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "[typescript]": {
    "editor.formatOnSave": true
  },
  "[javascript]": {
    "editor.formatOnSave": true
  },
  "editor.rulers": [
    80
  ],
  "clang-format.style": "Google",
  "files.insertFinalNewline": true,
  "editor.detectIndentation": false,
  "editor.wrappingIndent": "none",
  "typescript.tsdk": "node_modules/typescript/lib",
  "clang-format.executable": "${workspaceRoot}/node_modules/.bin/clang-format"
}
