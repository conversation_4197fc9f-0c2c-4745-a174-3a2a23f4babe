#!/usr/bin/env python3
"""
Test SmoothNet configuration loading and model initialization
"""

import yaml
from pathlib import Path

def test_local_config():
    """Test loading the local config file"""
    config_path = Path("smoothnet_weights_official/pw3d_spin_3D.yaml")
    
    if not config_path.exists():
        print(f"❌ Config file not found at {config_path}")
        return
    
    print(f"✅ Found config file at {config_path}")
    
    # Load and parse YAML
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    print("\n=== Configuration Structure ===")
    print(f"Top-level keys: {list(config.keys())}")
    
    print("\n=== Model Configuration ===")
    model_config = config.get('MODEL', {})
    print(f"HIDDEN_SIZE: {model_config.get('HIDDEN_SIZE')}")
    print(f"RES_HIDDEN_SIZE: {model_config.get('RES_HIDDEN_SIZE')}")
    print(f"NUM_BLOCK: {model_config.get('NUM_BLOCK')}")
    print(f"DROPOUT: {model_config.get('DROPOUT')}")
    print(f"SLIDE_WINDOW_SIZE: {model_config.get('SLIDE_WINDOW_SIZE')}")
    
    print("\n=== Architecture Comparison ===")
    print("Official config vs Current implementation:")
    print(f"Hidden size: {model_config.get('HIDDEN_SIZE')} vs 512 ✅")
    print(f"Residual hidden size: {model_config.get('RES_HIDDEN_SIZE')} vs 256 ❌ (was 256, should be 16)")
    print(f"Number of blocks: {model_config.get('NUM_BLOCK')} vs 3 ❌ (was 3, should be 1)")
    print(f"Dropout: {model_config.get('DROPOUT')} vs 0.5 ✅")
    
    print("\n⚠️  Key differences found:")
    print("1. RES_HIDDEN_SIZE should be 16 (not 256)")
    print("2. NUM_BLOCK should be 1 (not 3)")
    print("3. These differences could cause weight loading issues")

if __name__ == "__main__":
    print("=== SmoothNet Config Test ===")
    test_local_config()