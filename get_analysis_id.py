"""
Get the correct analysis ID for testing
"""

import modal

# Define image with Supabase dependency
image = modal.Image.debian_slim(python_version="3.11").pip_install([
    "supabase>=2.0.0",
    "psycopg2-binary>=2.9.0"
])

app = modal.App(
    name="maxwattz-get-analysis",
    image=image,
    secrets=[modal.Secret.from_name("maxwattz-supabase")]
)

@app.function()
def get_analysis_for_video(video_filename: str):
    """Find analysis ID associated with a video"""
    import os
    from supabase import create_client, Client
    
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_SERVICE_KEY')
    
    if not supabase_url or not supabase_key:
        return {"success": False, "error": "Missing credentials"}
    
    supabase: Client = create_client(supabase_url, supabase_key)
    
    # First, try to find the video
    print(f"Searching for video: {video_filename}")
    
    video_result = supabase.table('bio_run_videos') \
        .select('id', 'filename', 'view_type', 'created_at') \
        .eq('filename', video_filename) \
        .execute()
    
    if video_result.data:
        print(f"Found {len(video_result.data)} video(s)")
        for video in video_result.data:
            print(f"  Video ID: {video['id']}")
            print(f"  View: {video['view_type']}")
            print(f"  Created: {video['created_at']}")
            
            # Find associated analysis
            analysis_result = supabase.table('bio_run_analysis') \
                .select('id', 'status', 'user_email') \
                .or_(f"side_video_id.eq.{video['id']},rear_video_id.eq.{video['id']}") \
                .execute()
            
            if analysis_result.data:
                analysis = analysis_result.data[0]
                print(f"\n  Associated Analysis:")
                print(f"    Analysis ID: {analysis['id']}")
                print(f"    Status: {analysis['status']}")
                print(f"    User: {analysis['user_email']}")
                
                return {
                    "success": True,
                    "analysis_id": analysis['id'],
                    "video_id": video['id'],
                    "view_type": video['view_type']
                }
    
    # If no video found, list recent analyses
    print("\nNo video found with that filename. Recent analyses:")
    recent = supabase.table('bio_run_analysis') \
        .select('id', 'user_email', 'status', 'created_at') \
        .order('created_at', desc=True) \
        .limit(5) \
        .execute()
    
    for analysis in recent.data:
        print(f"  {analysis['id']} - {analysis['user_email']} - {analysis['status']}")
    
    return {"success": False, "error": "Video not found"}

@app.local_entrypoint()
def main(video: str = "Michael_test_side.mp4"):
    result = get_analysis_for_video.remote(video)
    print(f"\nResult: {result}")
    
    if result.get('success'):
        print(f"\n📋 Use this command to process with correct analysis ID:")
        print(f"modal run pose_inference.py --video {video} --analysis-id {result['analysis_id']} --view-type {result['view_type']}")

if __name__ == "__main__":
    import sys
    video = sys.argv[1] if len(sys.argv) > 1 else "Michael_test_side.mp4"
    main(video)