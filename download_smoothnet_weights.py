#!/usr/bin/env python3
"""
Download SmoothNet pre-trained weights from Google Drive
"""
import os
import sys
import requests
from urllib.parse import urlparse, parse_qs

def download_file_from_google_drive(file_id, destination):
    """Download a file from Google Drive"""
    URL = "https://docs.google.com/uc?export=download"

    session = requests.Session()
    response = session.get(URL, params={'id': file_id}, stream=True)
    token = get_confirm_token(response)

    if token:
        params = {'id': file_id, 'confirm': token}
        response = session.get(URL, params=params, stream=True)

    save_response_content(response, destination)

def get_confirm_token(response):
    """Extract confirmation token from Google Drive warning page"""
    for key, value in response.cookies.items():
        if key.startswith('download_warning'):
            return value
    return None

def save_response_content(response, destination):
    """Save response content to file"""
    CHUNK_SIZE = 32768
    
    os.makedirs(os.path.dirname(destination), exist_ok=True)
    
    with open(destination, "wb") as f:
        for chunk in response.iter_content(CHUNK_SIZE):
            if chunk:
                f.write(chunk)

def extract_file_id(url):
    """Extract file ID from Google Drive URL"""
    # Handle different Google Drive URL formats
    if 'drive.google.com' in url:
        if '/file/d/' in url:
            # Format: https://drive.google.com/file/d/FILE_ID/view
            file_id = url.split('/file/d/')[1].split('/')[0]
        elif 'id=' in url:
            # Format: https://drive.google.com/open?id=FILE_ID
            parsed = urlparse(url)
            file_id = parse_qs(parsed.query)['id'][0]
        else:
            raise ValueError(f"Unable to extract file ID from URL: {url}")
    else:
        # Assume it's just the file ID
        file_id = url
    
    return file_id

# Model download configurations
MODELS = {
    '3DPW-SPIN-3D': {
        'url': 'https://drive.google.com/file/d/106MnTXFLfMlJ2W7Fvw2vAlsUuQFdUe6k',
        'filename': 'smoothnet_3dpw_spin_3d.pth',
        'description': 'Best for in-the-wild scenarios (treadmill running)'
    },
    'H36M-FCN-3D': {
        'url': 'https://drive.google.com/file/d/1ZketGlY4qA3kFp044T1-PaykV2llNUjB',
        'filename': 'smoothnet_h36m_fcn_3d.pth',
        'description': 'Best for general 3D pose estimation'
    },
    'AIST-VIBE-3D': {
        'url': 'https://drive.google.com/file/d/101TH_Z8uiXD58d_xkuFTh5bI4NtRm_cK',
        'filename': 'smoothnet_aist_vibe_3d.pth',
        'description': 'Best for dynamic movements'
    }
}

def main():
    # Create weights directory
    weights_dir = "smoothnet_weights"
    os.makedirs(weights_dir, exist_ok=True)
    
    # Download 3DPW-SPIN-3D model (recommended for treadmill)
    model_name = '3DPW-SPIN-3D'
    model_info = MODELS[model_name]
    
    print(f"\nDownloading {model_name} model...")
    print(f"Description: {model_info['description']}")
    
    file_id = extract_file_id(model_info['url'])
    destination = os.path.join(weights_dir, model_info['filename'])
    
    if os.path.exists(destination):
        print(f"Model already exists at {destination}")
        print(f"File size: {os.path.getsize(destination) / 1024 / 1024:.2f} MB")
    else:
        print(f"Downloading from Google Drive (ID: {file_id})...")
        download_file_from_google_drive(file_id, destination)
        print(f"Downloaded to: {destination}")
        print(f"File size: {os.path.getsize(destination) / 1024 / 1024:.2f} MB")
    
    print("\n✅ Download complete!")
    print(f"\nTo use in Modal, upload this file to S3:")
    print(f"aws s3 cp {destination} s3://maxwattz-videos/model-weights/{model_info['filename']}")

if __name__ == "__main__":
    main()