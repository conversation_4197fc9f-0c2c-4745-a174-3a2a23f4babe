"""
Test pose inference without SmoothNet to verify coordinate scaling issue
"""
import modal
import json

app = modal.App("test-no-smoothnet")

# Test configuration
TEST_CONFIG = {
    'analysis_id': 'test-no-smoothnet-fix',
    'video_s3_key': 'test-videos/Michael_test_side.mp4',
    'view_type': 'side',
    'disable_smoothnet': True
}

@app.local_entrypoint()
def run_test():
    """Run pose inference with SmoothNet disabled"""
    print("=== Testing Pose Inference WITHOUT SmoothNet ===")
    print(f"Video: {TEST_CONFIG['video_s3_key']}")
    print(f"Analysis ID: {TEST_CONFIG['analysis_id']}")
    print(f"SmoothNet: DISABLED")
    print("")
    
    # Import and look up the PoseEstimator class directly
    from pose_inference import PoseEstimator
    
    # Call the remote method directly
    result = PoseEstimator().process.remote(
        video_s3_key=TEST_CONFIG['video_s3_key'],
        analysis_id=TEST_CONFIG['analysis_id'],
        view_type=TEST_CONFIG['view_type'],
        disable_smoothnet=TEST_CONFIG['disable_smoothnet']
    )
    
    print("Processing complete!")
    print(f"Output S3 key: {result.get('output_s3_key', 'N/A')}")
    
    # Check first few frames for coordinate values
    if 'frames' in result and len(result['frames']) > 0:
        print("\n=== Coordinate Check (First Frame) ===")
        frame = result['frames'][0]
        
        # Calculate ROI center
        valid_keypoints = [kp for kp in frame['keypoints'] if kp['x'] != 0.0 or kp['y'] != 0.0]
        if valid_keypoints:
            avg_x = sum(kp['x'] for kp in valid_keypoints) / len(valid_keypoints)
            avg_y = sum(kp['y'] for kp in valid_keypoints) / len(valid_keypoints)
            
            print(f"ROI Center: ({avg_x:.1f}, {avg_y:.1f})")
            print(f"Expected Center: (~540, ~960)")
            print(f"Previous (with SmoothNet): (~40, ~95)")
            
            # Check if coordinates are now correct
            if avg_x > 400 and avg_x < 700 and avg_y > 800 and avg_y < 1100:
                print("\n✅ SUCCESS: Coordinates are now in the correct range!")
                print("   This confirms untrained SmoothNet was causing the scaling issue.")
            else:
                print("\n❌ Coordinates still incorrect - there may be another issue.")
    
    return result

