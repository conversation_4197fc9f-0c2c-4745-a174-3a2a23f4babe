"""
Test AWS signature issues
"""

import modal

image = modal.Image.debian_slim().pip_install(["boto3", "requests"])

app = modal.App(
    name="maxwattz-aws-sig-test",
    image=image,
    secrets=[modal.Secret.from_name("maxwattz-videos")]
)

@app.function()
def test_aws_signature():
    """Debug AWS signature issues"""
    import os
    import boto3
    from botocore.exceptions import ClientError
    import hashlib
    
    print("=== AWS Signature Debugging ===\n")
    
    # 1. Check credential format
    access_key = os.environ.get('AWS_ACCESS_KEY_ID', '')
    secret_key = os.environ.get('AWS_SECRET_ACCESS_KEY', '')
    
    print(f"Access Key Length: {len(access_key)}")
    print(f"Access Key Format: {'Valid' if len(access_key) == 20 else 'Invalid - should be 20 chars'}")
    print(f"Access Key Pattern: {access_key[:4]}...{access_key[-4:]}")
    
    print(f"\nSecret Key Length: {len(secret_key)}")
    print(f"Secret Key Format: {'Valid' if len(secret_key) == 40 else 'May be invalid - usually 40 chars'}")
    
    # Check for common issues
    if ' ' in secret_key or '\n' in secret_key or '\t' in secret_key:
        print("⚠️  WARNING: Secret key contains whitespace characters!")
    
    if secret_key.startswith('"') or secret_key.endswith('"'):
        print("⚠️  WARNING: Secret key has quotes - these should be removed!")
    
    # 2. Try different S3 operations
    print("\n=== Testing S3 Operations ===\n")
    
    # Test with explicit region
    for region in ['us-east-1', 'us-west-2']:
        print(f"\nTrying region: {region}")
        try:
            s3 = boto3.client(
                's3',
                region_name=region,
                aws_access_key_id=access_key,
                aws_secret_access_key=secret_key
            )
            
            # Try a simpler operation first
            response = s3.list_buckets()
            print(f"✅ Success with region {region}!")
            print(f"   Found {len(response['Buckets'])} buckets")
            
            # List first few buckets
            for bucket in response['Buckets'][:3]:
                print(f"   - {bucket['Name']}")
            
            break  # If successful, stop trying other regions
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            print(f"❌ Failed with {error_code}: {e.response['Error']['Message']}")
    
    # 3. Try with STS to get caller identity
    print("\n=== Testing STS (Security Token Service) ===\n")
    try:
        sts = boto3.client(
            'sts',
            region_name='us-east-1',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key
        )
        
        identity = sts.get_caller_identity()
        print("✅ STS Success! Identity:")
        print(f"   Account: {identity['Account']}")
        print(f"   ARN: {identity['Arn']}")
        print(f"   UserId: {identity['UserId']}")
        
    except ClientError as e:
        print(f"❌ STS Failed: {e}")
    
    # 4. Test with v4 signature explicitly
    print("\n=== Testing with Signature V4 ===\n")
    try:
        from botocore.config import Config
        
        s3_v4 = boto3.client(
            's3',
            region_name='us-east-1',
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key,
            config=Config(signature_version='s3v4')
        )
        
        # Try to list objects in maxwattz-videos
        response = s3_v4.list_objects_v2(
            Bucket='maxwattz-videos',
            MaxKeys=5
        )
        print("✅ S3 V4 Signature Success!")
        print(f"   Found {response.get('KeyCount', 0)} objects")
        
    except ClientError as e:
        print(f"❌ S3 V4 Failed: {e.response['Error']['Code']} - {e.response['Error']['Message']}")
    
    return {"completed": True}

@app.local_entrypoint()
def main():
    result = test_aws_signature.remote()
    print(f"\nTest completed: {result}")

if __name__ == "__main__":
    main()