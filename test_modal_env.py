"""
Test Modal environment and secrets
"""

import modal

# Add boto3 to the image
image = modal.Image.debian_slim().pip_install("boto3")

app = modal.App(
    name="maxwattz-env-test",
    image=image,
    secrets=[modal.Secret.from_name("maxwattz-videos")]
)

@app.function()
def test_environment():
    """Check what environment variables are available"""
    import os
    
    print("Checking Modal environment...")
    
    # Check for AWS credentials
    aws_vars = [
        'AWS_ACCESS_KEY_ID',
        'AWS_SECRET_ACCESS_KEY',
        'AWS_DEFAULT_REGION',
        'AWS_REGION',
        'AWS_SESSION_TOKEN'
    ]
    
    print("\nAWS Environment Variables:")
    for var in aws_vars:
        value = os.environ.get(var)
        if value:
            if 'KEY' in var or 'SECRET' in var or 'TOKEN' in var:
                # Mask sensitive values
                print(f"  {var}: {'*' * 10}{value[-4:]}")
            else:
                print(f"  {var}: {value}")
        else:
            print(f"  {var}: Not set")
    
    # Try to import and check boto3
    try:
        import boto3
        print("\n✅ boto3 imported successfully")
        print(f"   Version: {boto3.__version__}")
    except ImportError as e:
        print(f"\n❌ Failed to import boto3: {e}")
    
    return {"success": True}

@app.local_entrypoint()
def main():
    result = test_environment.remote()
    print(f"\nResult: {result}")

if __name__ == "__main__":
    main()