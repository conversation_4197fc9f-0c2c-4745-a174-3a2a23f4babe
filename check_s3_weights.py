#!/usr/bin/env python3
"""
Verify SmoothNet weights and config are properly stored on S3
"""

import boto3
import os

def check_s3_files():
    """Check if both config and weights are stored on S3"""
    
    try:
        s3_client = boto3.client('s3')
        bucket_name = 'maxwattz-videos'
        
        # Files to check
        files_to_check = [
            'model-weights/pw3d_spin_3D.yaml',
            'model-weights/smoothnet_3dpw_spin_3d_checkpoint_32.pth.tar'
        ]
        
        print("=== S3 File Check ===")
        
        for s3_key in files_to_check:
            try:
                response = s3_client.head_object(Bucket=bucket_name, Key=s3_key)
                file_size = response['ContentLength']
                last_modified = response['LastModified']
                print(f"✅ {s3_key}")
                print(f"   Size: {file_size:,} bytes ({file_size / 1024 / 1024:.2f} MB)")
                print(f"   Modified: {last_modified}")
                print()
            except s3_client.exceptions.NoSuchKey:
                print(f"❌ {s3_key} - NOT FOUND")
                print()
            except Exception as e:
                print(f"⚠️  {s3_key} - ERROR: {e}")
                print()
        
        # Check if local weights exist and need uploading
        local_weights = "smoothnet_weights_official/checkpoint_32.pth.tar"
        if os.path.exists(local_weights):
            stat = os.stat(local_weights)
            print(f"📁 Local weights available: {local_weights}")
            print(f"   Size: {stat.st_size:,} bytes ({stat.st_size / 1024 / 1024:.2f} MB)")
            
            # Check if we need to upload
            if 'model-weights/smoothnet_3dpw_spin_3d_checkpoint_32.pth.tar' not in [f for f in files_to_check if '✅' in str(f)]:
                print(f"🔄 Weights need to be uploaded to S3")
        
    except Exception as e:
        print(f"Error checking S3: {e}")

def upload_weights_if_needed():
    """Upload weights to S3 if not already there"""
    
    try:
        s3_client = boto3.client('s3')
        bucket_name = 'maxwattz-videos'
        local_weights = "smoothnet_weights_official/checkpoint_32.pth.tar"
        s3_key = 'model-weights/smoothnet_3dpw_spin_3d_checkpoint_32.pth.tar'
        
        # Check if file exists on S3
        try:
            s3_client.head_object(Bucket=bucket_name, Key=s3_key)
            print(f"✅ Weights already exist on S3: {s3_key}")
            return True
        except s3_client.exceptions.NoSuchKey:
            print(f"🔄 Uploading weights to S3: {s3_key}")
        
        # Upload file
        if os.path.exists(local_weights):
            s3_client.upload_file(local_weights, bucket_name, s3_key)
            
            # Verify upload
            response = s3_client.head_object(Bucket=bucket_name, Key=s3_key)
            print(f"✅ Upload successful!")
            print(f"   Size: {response['ContentLength']:,} bytes")
            print(f"   S3 URL: s3://{bucket_name}/{s3_key}")
            return True
        else:
            print(f"❌ Local weights file not found: {local_weights}")
            return False
            
    except Exception as e:
        print(f"Error uploading weights: {e}")
        return False

if __name__ == "__main__":
    print("=== SmoothNet S3 Verification ===")
    check_s3_files()
    print("\n=== Upload Check ===")
    upload_weights_if_needed()