#!/usr/bin/env python3
"""
Download official SmoothNet 3dpw_spin_3D.yaml config file to S3
"""

import os
import sys
import boto3
import requests
import yaml
from pathlib import Path

def download_config_from_github():
    """Download the official 3dpw_spin_3D.yaml config from SmoothNet GitHub"""
    
    # Official SmoothNet GitHub config URL
    config_url = "https://raw.githubusercontent.com/cure-lab/SmoothNet/main/configs/pw3d_spin_3D.yaml"
    
    print(f"Downloading config from: {config_url}")
    
    try:
        response = requests.get(config_url)
        response.raise_for_status()
        
        # Parse YAML to validate it's correct
        config_data = yaml.safe_load(response.text)
        
        print("\n=== Config Overview ===")
        print(f"Model type: {config_data.get('model', {}).get('name', 'Unknown')}")
        print(f"Window size: {config_data.get('slide_window_size', 'Unknown')}")
        print(f"Architecture settings:")
        for key, value in config_data.get('model', {}).items():
            if key != 'name':
                print(f"  - {key}: {value}")
        
        return response.text, config_data
        
    except Exception as e:
        print(f"Error downloading config: {e}")
        return None, None

def upload_to_s3(config_content, bucket_name='maxwattz-videos'):
    """Upload config file to S3"""
    
    s3_key = 'model-weights/pw3d_spin_3D.yaml'
    
    try:
        # Initialize S3 client
        s3_client = boto3.client('s3')
        
        # Upload the config file
        s3_client.put_object(
            Bucket=bucket_name,
            Key=s3_key,
            Body=config_content,
            ContentType='text/yaml'
        )
        
        print(f"\n✅ Config uploaded to: s3://{bucket_name}/{s3_key}")
        
        # Verify upload
        response = s3_client.head_object(Bucket=bucket_name, Key=s3_key)
        print(f"File size: {response['ContentLength']} bytes")
        print(f"Last modified: {response['LastModified']}")
        
        return True
        
    except Exception as e:
        print(f"Error uploading to S3: {e}")
        return False

def save_local_copy(config_content):
    """Save a local copy for reference"""
    local_path = Path("smoothnet_weights_official/pw3d_spin_3D.yaml")
    local_path.parent.mkdir(exist_ok=True)
    
    with open(local_path, 'w') as f:
        f.write(config_content)
    
    print(f"\nLocal copy saved to: {local_path}")

def main():
    print("=== SmoothNet Config Downloader ===")
    print("Downloading official 3dpw_spin_3D.yaml configuration...")
    
    # Download from GitHub
    config_content, config_data = download_config_from_github()
    
    if not config_content:
        print("Failed to download config file")
        sys.exit(1)
    
    # Save local copy
    save_local_copy(config_content)
    
    # Upload to S3
    if upload_to_s3(config_content):
        print("\n✅ Config file successfully uploaded to S3!")
        print("\nNext steps:")
        print("1. Update pose_inference.py to load this config")
        print("2. Ensure model architecture matches config parameters")
        print("3. Test with pre-trained weights")
    else:
        print("\n❌ Failed to upload to S3")
        sys.exit(1)

if __name__ == "__main__":
    main()