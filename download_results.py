#!/usr/bin/env python3
"""
Download processed results from S3 for local testing
"""

import boto3
import json
import os
from pathlib import Path

def download_results():
    """Download the processed results JSON for local testing"""
    
    # S3 configuration
    bucket = 'maxwattz-videos'
    key = 'maxwattz-running-metrics-side/Michael_test_side_pose.json'
    local_file = 'pose-detection/src/analysis-app/test-results.json'
    
    try:
        # Initialize S3 client (uses AWS credentials from environment)
        s3 = boto3.client('s3')
        
        print(f"📥 Downloading {key} from {bucket}...")
        
        # Download the file
        s3.download_file(bucket, key, local_file)
        
        print(f"✅ Downloaded to: {local_file}")
        
        # Validate the JSON
        with open(local_file, 'r') as f:
            data = json.load(f)
            
        print(f"📊 Results summary:")
        print(f"   Model: {data.get('modelType', 'Unknown')}")
        print(f"   View: {data.get('viewType', 'Unknown')}")
        print(f"   Frames: {len(data.get('frames', []))}")
        print(f"   Processing time: {data.get('processingTime', 0):.1f}s")
        print(f"   Video dimensions: {data.get('videoWidth', 0)}×{data.get('videoHeight', 0)}")
        
        if data.get('frames'):
            first_frame = data['frames'][0]
            keypoint_count = len(first_frame.get('keypoints', []))
            avg_confidence = sum(kp.get('score', 0) for kp in first_frame.get('keypoints', [])) / max(keypoint_count, 1)
            
            print(f"   Keypoints per frame: {keypoint_count}")
            print(f"   Avg confidence (first frame): {avg_confidence:.1%}")
            
            # Show first few keypoints for validation
            print(f"\n🔍 First 3 keypoints from frame 0:")
            for i, kp in enumerate(first_frame.get('keypoints', [])[:3]):
                print(f"   {kp.get('name', 'Unknown')}: x={kp.get('x', 0):.1f}, y={kp.get('y', 0):.1f}, score={kp.get('score', 0):.2f}")
        
        print(f"\n🌐 Now you can test locally by updating the test page to use:")
        print(f"   resultsUrl: './test-results.json'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error downloading results: {e}")
        print(f"\n💡 Alternative: Use AWS CLI:")
        print(f"   aws s3 cp s3://{bucket}/{key} {local_file}")
        return False

if __name__ == "__main__":
    success = download_results()
    
    if success:
        print(f"\n🚀 Ready to test! Run:")
        print(f"   cd pose-detection/src/analysis-app")
        print(f"   python -m http.server 8000")
        print(f"   # Then visit: http://localhost:8000/test-processed.html")
    else:
        print(f"\n🔧 Setup AWS credentials first:")
        print(f"   export AWS_ACCESS_KEY_ID=your-key")
        print(f"   export AWS_SECRET_ACCESS_KEY=your-secret")