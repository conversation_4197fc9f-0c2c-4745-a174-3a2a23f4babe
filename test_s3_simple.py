"""
Simple S3 test - try bucket name directly
"""

import modal

# Test 1: Try direct bucket name
app = modal.App(
    name="maxwattz-s3-simple-test",
    secrets=[modal.Secret.from_name("maxwattz-videos")],
    volumes={
        "/mnt/s3": modal.CloudBucketMount(
            "maxwattz-videos",  # Try bucket name directly
            secret=modal.Secret.from_name("maxwattz-videos")
        )
    }
)

@app.function()
def test_simple_s3():
    """Test with simple bucket name"""
    import os
    
    print("Testing S3 with direct bucket name...")
    mount_path = "/mnt/s3"
    
    try:
        # Check if mount exists
        if os.path.exists(mount_path):
            print("✅ Mount path exists")
            
            # List contents
            contents = os.listdir(mount_path)
            print(f"Found {len(contents)} items:")
            
            for item in contents[:10]:
                print(f"  - {item}")
                
            return {"success": True, "method": "direct bucket name"}
        else:
            print("❌ Mount path does not exist")
            return {"success": False, "error": "Mount path not found"}
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return {"success": False, "error": str(e)}

@app.local_entrypoint()
def main():
    result = test_simple_s3.remote()
    print(f"\nResult: {result}")

if __name__ == "__main__":
    main()