# Directory Analysis Findings

**Analysis Date:** Current  
**Scope:** Pose-detection directory structure after moving models to archived_models  
**Purpose:** Identify file conflicts, broken imports, and architectural issues

## 🚨 Critical Issues Discovered

### 1. **BROKEN FACTORY PATTERN**
Moving `blazepose_mediapipe/` and `movenet/` directories has broken the core factory pattern in `create_detector.ts`:

```typescript
// BROKEN IMPORTS in create_detector.ts:
import {load as loadMoveNetDetector} from './movenet/detector';
import {load as loadBlazePoseMediaPipeDetector} from './blazepose_mediapipe/detector';
import {MoveNetModelConfig} from './movenet/types';
import {BlazePoseMediaPipeModelConfig} from './blazepose_mediapipe/types';
```

### 2. **MISSING POSENET DIRECTORY**
`PoseNet` is listed in `SupportedModels` enum but the directory is completely missing:
```typescript
export enum SupportedModels {
  MoveNet = 'MoveNet',      // ❌ Moved to archived_models/
  BlazePose = 'BlazePose',  // ⚠️ Partially working (tfjs only)
  PoseNet = 'PoseNet'       // ❌ Completely missing
}
```

### 3. **<PERSON><PERSON>EN EXPORTS**
`index.ts` exports from non-existent paths:
```typescript
// BROKEN EXPORTS:
export {PoseNetEstimationConfig, PosenetModelConfig} from './posenet/types';
export {SINGLEPOSE_LIGHTNING, SINGLEPOSE_THUNDER, MULTIPOSE_LIGHTNING} from './movenet/constants';
```

---

## 📁 Current Directory Structure

### Root `/src` Files:
- ✅ `constants.ts` - **SHARED** - Contains BLAZEPOSE_KEYPOINTS, COCO_KEYPOINTS
- ✅ `types.ts` - **SHARED** - Core interfaces (SupportedModels, Pose, EstimationConfig)
- ✅ `create_detector.ts` - **CORE API** - Factory for all model types
- ✅ `index.ts` - **ENTRY POINT** - Main module exports
- ✅ `pose_detector.ts` - **INTERFACE** - Base detector interface
- ✅ `util.ts`, `version.ts`, `setup_test.ts` - **UTILITIES**

### Active Model Directory:
- ✅ `blazepose_tfjs/` - **WORKING MODEL**
  - `constants.ts` - Model-specific URLs and configs
  - `detector.ts` - BlazePose TFJS implementation
  - `detector_utils.ts` - Model utilities
  - `types.ts` - Model-specific interfaces
  - `blazepose_test.ts` - Model tests

### Archived Model Directories:
- ⚠️ `archived_models/blazepose_mediapipe/` - **MOVED** (breaks imports)
- ⚠️ `archived_models/movenet/` - **MOVED** (breaks imports)

### Infrastructure Directories:
- ✅ `shared/` - Shared calculators and filters
- ✅ `calculators/` - High-level tracking interfaces

---

## 🔄 File Name Conflicts Analysis

### Identical File Names Across Directories:

| File Name | Root `src/` | `blazepose_tfjs/` | `archived_models/` |
|-----------|-------------|-------------------|-------------------|
| `constants.ts` | ✅ SHARED KEYPOINTS | ✅ MODEL CONFIG | ⚠️ BOTH MODELS |
| `types.ts` | ✅ CORE INTERFACES | ✅ MODEL TYPES | ⚠️ BOTH MODELS |
| `detector.ts` | ❌ | ✅ IMPLEMENTATION | ⚠️ BOTH MODELS |
| `detector_utils.ts` | ❌ | ✅ UTILITIES | ⚠️ BOTH MODELS |

### Purpose Differentiation:
- **Root `constants.ts`**: Global keypoint definitions (BLAZEPOSE_KEYPOINTS, COCO_KEYPOINTS)
- **Model `constants.ts`**: Model URLs, tensor configs, anchor configurations
- **Root `types.ts`**: Core API interfaces (SupportedModels, Pose, EstimationConfig)
- **Model `types.ts`**: Model-specific configurations and interfaces

---

## 🔍 Import Dependency Analysis

### Critical Broken Imports:

#### In `create_detector.ts`:
```typescript
// ❌ BROKEN - Directory moved
import {load as loadMoveNetDetector} from './movenet/detector';
import {MoveNetModelConfig} from './movenet/types';
import {load as loadBlazePoseMediaPipeDetector} from './blazepose_mediapipe/detector';
import {BlazePoseMediaPipeModelConfig} from './blazepose_mediapipe/types';

// ❌ BROKEN - Directory missing
import {load as loadPoseNetDetector} from './posenet/detector';
import {PosenetModelConfig} from './posenet/types';

// ✅ WORKING - Directory active
import {load as loadBlazePoseTfjsDetector} from './blazepose_tfjs/detector';
import {BlazePoseTfjsModelConfig} from './blazepose_tfjs/types';
```

#### In `index.ts`:
```typescript
// ❌ BROKEN exports
export {PoseNetEstimationConfig, PosenetModelConfig} from './posenet/types';
export {SINGLEPOSE_LIGHTNING, SINGLEPOSE_THUNDER, MULTIPOSE_LIGHTNING} from './movenet/constants';
export {BlazePoseMediaPipeEstimationConfig, BlazePoseMediaPipeModelConfig} from './blazepose_mediapipe/types';
```

#### In `pose_detector.ts`:
```typescript
// ❌ BROKEN types
import {PoseNetEstimationConfig} from './posenet/types';
import {MoveNetEstimationConfig} from './movenet/types';
import {BlazePoseMediaPipeEstimationConfig} from './blazepose_mediapipe/types';
```

---

## 🏗️ Architecture Issues

### 1. **Dual Shared Directories**
- `/Users/<USER>/tfjs-models/shared/` (Root level)
- `/Users/<USER>/pose-detection/src/shared/` (Pose-detection specific)

Both contain identical calculator files - **potential duplication issue**.

### 2. **SupportedModels vs Reality Mismatch**
```typescript
// What's declared:
export enum SupportedModels {
  MoveNet = 'MoveNet',      // ❌ Moved to archived
  BlazePose = 'BlazePose',  // ⚠️ Only tfjs runtime works
  PoseNet = 'PoseNet'       // ❌ Directory missing
}

// What actually works:
SupportedModels.BlazePose with runtime: 'tfjs' only
```

### 3. **Factory Pattern Breakdown**
The `createDetector()` function cannot instantiate moved models, breaking the unified API.

---

## 🛠️ Resolution Strategies

### **Option A: Minimal Fix (Recommended)**
1. **Update imports** to reference `archived_models/` paths
2. **Keep all models accessible** but clearly archived
3. **Fix exports** in `index.ts`
4. **Create posenet directory** or remove from SupportedModels

### **Option B: BlazePose-Only Focus**
1. **Remove all references** to moved models from SupportedModels
2. **Update factory pattern** to only support BlazePose TFJS
3. **Clean up imports** to remove unused model types
4. **Update documentation** to reflect BlazePose-only focus

### **Option C: Restore Architecture**
1. **Move models back** to original locations
2. **Keep archive as backup** reference
3. **Maintain full multi-model support**
4. **Create proper isolation** for analysis-app

---

## 📋 Immediate Action Required

### Priority 1 - Critical Fixes:
- [ ] Fix broken imports in `create_detector.ts`
- [ ] Update exports in `index.ts`
- [ ] Resolve missing PoseNet directory
- [ ] Test build process

### Priority 2 - Architecture Decisions:
- [ ] Choose resolution strategy (A, B, or C above)
- [ ] Decide on archived model accessibility
- [ ] Consolidate shared directory structure
- [ ] Update SupportedModels enum

### Priority 3 - Implementation:
- [ ] Create analysis-app structure
- [ ] Verify BlazePose TFJS functionality
- [ ] Update documentation
- [ ] Add tests for new structure

---

## 🎯 Recommendation

**Given the BlazePose-only focus for this project, I recommend Option B:**
1. Update the codebase to only support BlazePose TFJS
2. Remove references to moved models from the API
3. Maintain archived models for reference only
4. This simplifies the architecture for the analysis-app development

This approach aligns with the project goal of creating a BlazePose-specific running analysis tool while preserving the ability to reference other models if needed later.