
steps:

# Install common dependencies.
- name: 'node:16'
  id: 'yarn-common'
  entrypoint: 'yarn'
  args: ['install']

# Install pose-detection dependencies.
- name: 'node:16'
  dir: 'pose-detection'
  entrypoint: 'yarn'
  id: 'yarn'
  args: ['install']
  waitFor: ['yarn-common']

# Lint.
- name: 'node:16'
  dir: 'pose-detection'
  entrypoint: 'yarn'
  id: 'lint'
  args: ['lint']
  waitFor: ['yarn']

# Build.
- name: 'node:16'
  dir: 'pose-detection'
  entrypoint: 'yarn'
  id: 'build'
  args: ['build']
  waitFor: ['yarn']

# Run node tests.
- name: 'node:16'
  dir: 'pose-detection'
  entrypoint: 'yarn'
  id: 'test-node'
  args: ['test-node']
  waitFor: ['build']

# Run browser tests.
- name: 'node:16'
  dir: 'pose-detection'
  entrypoint: 'yarn'
  id: 'test'
  args: ['test-ci']
  env: ['BROWSERSTACK_USERNAME=deeplearnjs1']
  secretEnv: ['BROWSERSTACK_KEY']
  waitFor: ['test-node']

# General configuration
secrets:
- kmsKeyName: projects/learnjs-174218/locations/global/keyRings/tfjs/cryptoKeys/enc
  secretEnv:
    BROWSERSTACK_KEY: CiQAkwyoIW0LcnxymzotLwaH4udVTQFBEN4AEA5CA+a3+yflL2ASPQAD8BdZnGARf78MhH5T9rQqyz9HNODwVjVIj64CTkFlUCGrP1B2HX9LXHWHLmtKutEGTeFFX9XhuBzNExA=
timeout: 1800s
logsBucket: 'gs://tfjs-build-logs'
substitutions:
  _NIGHTLY: ''
options:
  logStreamingOption: 'STREAM_ON'
  substitution_option: 'ALLOW_LOOSE'
