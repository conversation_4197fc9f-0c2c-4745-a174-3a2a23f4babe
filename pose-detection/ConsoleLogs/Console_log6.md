dev-app.html:481 {"timestamp":"11:07:11 PM","type":"info","message":"Setting TensorFlow.js backend to WebGL...","data":null}
dev-app.html:481 {"timestamp":"11:07:11 PM","type":"info","message":"TensorFlow.js backend initialized: webgl","data":null}
dev-app.html:481 {"timestamp":"11:07:11 PM","type":"info","message":"Loading BlazePose Full model from default CDN...","data":null}
dev-app.html:481 {"timestamp":"11:07:11 PM","type":"info","message":"BlazePose detector created successfully","data":null}
dev-app.html:481 {"timestamp":"11:07:11 PM","type":"info","message":"Validating detector configuration...","data":null}
dev-app.html:481 {"timestamp":"11:07:11 PM","type":"info","message":"Detector class: e","data":null}
dev-app.html:481 {"timestamp":"11:07:12 PM","type":"info","message":"Test result: 0 poses detected","data":null}
dev-app.html:481 {"timestamp":"11:07:12 PM","type":"info","message":"Validation passed: No pose detected on test pattern (expected behavior)","data":null}
dev-app.html:481 {"timestamp":"11:07:12 PM","type":"info","message":"BlazePose detector fully initialized and tested","data":null}
dev-app.html:481 {"timestamp":"11:07:21 PM","type":"info","message":"Video loaded: 1080x1920, Canvas: 640x1137","data":{"videoRes":{"width":1080,"height":1920},"canvasRes":{"width":640,"height":1137},"aspectRatio":1.7777777777777777}}
dev-app.html:481 {"timestamp":"11:07:24 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:24 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:49 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
favicon.ico:1  GET http://localhost:8080/favicon.ico 404 (File not found)
dev-app.html:481 {"timestamp":"11:07:49 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:49 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:50 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.998","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.991","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:51 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:52 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.996","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.998","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:53 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:58 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 0.999","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:07:59 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"11:08:00 PM","type":"info","message":"Valid pose detected: 33 keypoints, score: 1.000","data":null}
