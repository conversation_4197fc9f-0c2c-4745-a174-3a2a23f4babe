dev-app.html:475 {"timestamp":"7:12:15 PM","type":"info","message":"Setting TensorFlow.js backend to WebGL...","data":null}
dev-app.html:475 {"timestamp":"7:12:15 PM","type":"info","message":"TensorFlow.js backend initialized: webgl","data":null}
dev-app.html:475 {"timestamp":"7:12:15 PM","type":"info","message":"Loading BlazePose Full model from default CDN...","data":null}
dev-app.html:475 {"timestamp":"7:12:15 PM","type":"info","message":"BlazePose detector created successfully","data":null}
dev-app.html:475 {"timestamp":"7:12:15 PM","type":"info","message":"Validating detector configuration...","data":null}
dev-app.html:475 {"timestamp":"7:12:15 PM","type":"info","message":"Detector class: e","data":null}
dev-app.html:475 {"timestamp":"7:12:17 PM","type":"info","message":"Test result: 0 poses detected","data":null}
dev-app.html:475 {"timestamp":"7:12:17 PM","type":"info","message":"Validation passed: No pose detected on test pattern (expected behavior)","data":null}
dev-app.html:475 {"timestamp":"7:12:17 PM","type":"info","message":"BlazePose detector fully initialized and tested","data":null}
dev-app.html:475 {"timestamp":"7:12:23 PM","type":"info","message":"Video loaded: 1080x1920","data":null}
dev-app.html:475 {"timestamp":"7:12:42 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
favicon.ico:1  GET http://localhost:8080/favicon.ico 404 (File not found)
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:01 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:02 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:03 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:04 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:05 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
dev-app.html:475 {"timestamp":"7:13:06 PM","type":"error","message":"INVALID POSE: Score is NaN/invalid (NaN) - STOPPING PROCESSING","data":null}
