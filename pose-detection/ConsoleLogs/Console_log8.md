(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
(anonymous) @ (index):64
transformScriptTags.ts:253 You are using the in-browser Babel transformer. Be sure to precompile your scripts for production - https://babeljs.io/docs/setup/
(anonymous) @ transformScriptTags.ts:253
error_handler.js:1 Warning: ReactDOM.render is no longer supported in React 18. Use createRoot instead. Until you switch to the new API, your app will behave as if it's running React 17. Learn more: https://reactjs.org/link/switch-to-createroot
wr.error @ error_handler.js:1
Inline Babel script:26 ✅ Loaded data: ObjectanalysisId: "e5dde56d-f375-4cd4-8866-2bc50a5aedef"fps: 29.97002997002997frames: Array(285)[0 … 99]0: {frameNumber: 0, timestamp: 0, keypoints: Array(33)}1: {frameNumber: 1, timestamp: 0.03336666666666667, keypoints: Array(33)}2: {frameNumber: 2, timestamp: 0.06673333333333334, keypoints: Array(33)}3: {frameNumber: 3, timestamp: 0.10010000000000001, keypoints: Array(33)}4: {frameNumber: 4, timestamp: 0.13346666666666668, keypoints: Array(33)}5: {frameNumber: 5, timestamp: 0.16683333333333333, keypoints: Array(33)}6: {frameNumber: 6, timestamp: 0.20020000000000002, keypoints: Array(33)}7: {frameNumber: 7, timestamp: 0.23356666666666667, keypoints: Array(33)}8: {frameNumber: 8, timestamp: 0.26693333333333336, keypoints: Array(33)}9: {frameNumber: 9, timestamp: 0.3003, keypoints: Array(33)}10: {frameNumber: 10, timestamp: 0.33366666666666667, keypoints: Array(33)}11: {frameNumber: 11, timestamp: 0.3670333333333333, keypoints: Array(33)}12: {frameNumber: 12, timestamp: 0.40040000000000003, keypoints: Array(33)}13: {frameNumber: 13, timestamp: 0.4337666666666667, keypoints: Array(33)}14: {frameNumber: 14, timestamp: 0.46713333333333334, keypoints: Array(33)}15: {frameNumber: 15, timestamp: 0.5005000000000001, keypoints: Array(33)}16: {frameNumber: 16, timestamp: 0.5338666666666667, keypoints: Array(33)}17: {frameNumber: 17, timestamp: 0.5672333333333334, keypoints: Array(33)}18: {frameNumber: 18, timestamp: 0.6006, keypoints: Array(33)}19: {frameNumber: 19, timestamp: 0.6339666666666667, keypoints: Array(33)}20: {frameNumber: 20, timestamp: 0.6673333333333333, keypoints: Array(33)}21: {frameNumber: 21, timestamp: 0.7007, keypoints: Array(33)}22: {frameNumber: 22, timestamp: 0.7340666666666666, keypoints: Array(33)}23: {frameNumber: 23, timestamp: 0.7674333333333333, keypoints: Array(33)}24: {frameNumber: 24, timestamp: 0.8008000000000001, keypoints: Array(33)}25: {frameNumber: 25, timestamp: 0.8341666666666667, keypoints: Array(33)}26: {frameNumber: 26, timestamp: 0.8675333333333334, keypoints: Array(33)}27: {frameNumber: 27, timestamp: 0.9009, keypoints: Array(33)}28: {frameNumber: 28, timestamp: 0.9342666666666667, keypoints: Array(33)}29: {frameNumber: 29, timestamp: 0.9676333333333333, keypoints: Array(33)}30: {frameNumber: 30, timestamp: 1.0010000000000001, keypoints: Array(33)}31: {frameNumber: 31, timestamp: 1.0343666666666667, keypoints: Array(33)}32: {frameNumber: 32, timestamp: 1.0677333333333334, keypoints: Array(33)}33: {frameNumber: 33, timestamp: 1.1011, keypoints: Array(33)}34: {frameNumber: 34, timestamp: 1.1344666666666667, keypoints: Array(33)}35: {frameNumber: 35, timestamp: 1.1678333333333333, keypoints: Array(33)}36: {frameNumber: 36, timestamp: 1.2012, keypoints: Array(33)}37: {frameNumber: 37, timestamp: 1.2345666666666666, keypoints: Array(33)}38: {frameNumber: 38, timestamp: 1.2679333333333334, keypoints: Array(33)}39: {frameNumber: 39, timestamp: 1.3013000000000001, keypoints: Array(33)}40: {frameNumber: 40, timestamp: 1.3346666666666667, keypoints: Array(33)}41: {frameNumber: 41, timestamp: 1.3680333333333334, keypoints: Array(33)}42: {frameNumber: 42, timestamp: 1.4014, keypoints: Array(33)}43: {frameNumber: 43, timestamp: 1.4347666666666667, keypoints: Array(33)}44: {frameNumber: 44, timestamp: 1.4681333333333333, keypoints: Array(33)}45: {frameNumber: 45, timestamp: 1.5015, keypoints: Array(33)}46: {frameNumber: 46, timestamp: 1.5348666666666666, keypoints: Array(33)}47: {frameNumber: 47, timestamp: 1.5682333333333334, keypoints: Array(33)}48: {frameNumber: 48, timestamp: 1.6016000000000001, keypoints: Array(33)}49: {frameNumber: 49, timestamp: 1.6349666666666667, keypoints: Array(33)}50: {frameNumber: 50, timestamp: 1.6683333333333334, keypoints: Array(33)}51: {frameNumber: 51, timestamp: 1.7017, keypoints: Array(33)}52: {frameNumber: 52, timestamp: 1.7350666666666668, keypoints: Array(33)}53: {frameNumber: 53, timestamp: 1.7684333333333333, keypoints: Array(33)}54: {frameNumber: 54, timestamp: 1.8018, keypoints: Array(33)}55: {frameNumber: 55, timestamp: 1.8351666666666666, keypoints: Array(33)}56: {frameNumber: 56, timestamp: 1.8685333333333334, keypoints: Array(33)}57: {frameNumber: 57, timestamp: 1.9019, keypoints: Array(33)}58: {frameNumber: 58, timestamp: 1.9352666666666667, keypoints: Array(33)}59: {frameNumber: 59, timestamp: 1.9686333333333335, keypoints: Array(33)}60: {frameNumber: 60, timestamp: 2.0020000000000002, keypoints: Array(33)}61: {frameNumber: 61, timestamp: 2.0353666666666665, keypoints: Array(33)}62: {frameNumber: 62, timestamp: 2.0687333333333333, keypoints: Array(33)}63: {frameNumber: 63, timestamp: 2.1021, keypoints: Array(33)}64: {frameNumber: 64, timestamp: 2.135466666666667, keypoints: Array(33)}65: {frameNumber: 65, timestamp: 2.168833333333333, keypoints: Array(33)}66: {frameNumber: 66, timestamp: 2.2022, keypoints: Array(33)}67: {frameNumber: 67, timestamp: 2.2355666666666667, keypoints: Array(33)}68: {frameNumber: 68, timestamp: 2.2689333333333335, keypoints: Array(33)}69: {frameNumber: 69, timestamp: 2.3023000000000002, keypoints: Array(33)}70: {frameNumber: 70, timestamp: 2.3356666666666666, keypoints: Array(33)}71: {frameNumber: 71, timestamp: 2.3690333333333333, keypoints: Array(33)}72: {frameNumber: 72, timestamp: 2.4024, keypoints: Array(33)}73: {frameNumber: 73, timestamp: 2.435766666666667, keypoints: Array(33)}74: {frameNumber: 74, timestamp: 2.469133333333333, keypoints: Array(33)}75: {frameNumber: 75, timestamp: 2.5025, keypoints: Array(33)}76: {frameNumber: 76, timestamp: 2.5358666666666667, keypoints: Array(33)}77: {frameNumber: 77, timestamp: 2.5692333333333335, keypoints: Array(33)}78: {frameNumber: 78, timestamp: 2.6026000000000002, keypoints: Array(33)}79: {frameNumber: 79, timestamp: 2.6359666666666666, keypoints: Array(33)}80: {frameNumber: 80, timestamp: 2.6693333333333333, keypoints: Array(33)}81: {frameNumber: 81, timestamp: 2.7027, keypoints: Array(33)}82: {frameNumber: 82, timestamp: 2.736066666666667, keypoints: Array(33)}83: {frameNumber: 83, timestamp: 2.769433333333333, keypoints: Array(33)}84: {frameNumber: 84, timestamp: 2.8028, keypoints: Array(33)}85: {frameNumber: 85, timestamp: 2.8361666666666667, keypoints: Array(33)}86: {frameNumber: 86, timestamp: 2.8695333333333335, keypoints: Array(33)}87: {frameNumber: 87, timestamp: 2.9029000000000003, keypoints: Array(33)}88: {frameNumber: 88, timestamp: 2.9362666666666666, keypoints: Array(33)}89: {frameNumber: 89, timestamp: 2.9696333333333333, keypoints: Array(33)}90: {frameNumber: 90, timestamp: 3.003, keypoints: Array(33)}91: {frameNumber: 91, timestamp: 3.036366666666667, keypoints: Array(33)}92: {frameNumber: 92, timestamp: 3.069733333333333, keypoints: Array(33)}93: {frameNumber: 93, timestamp: 3.1031, keypoints: Array(33)}94: {frameNumber: 94, timestamp: 3.1364666666666667, keypoints: Array(33)}95: {frameNumber: 95, timestamp: 3.1698333333333335, keypoints: Array(33)}96: {frameNumber: 96, timestamp: 3.2032000000000003, keypoints: Array(33)}97: {frameNumber: 97, timestamp: 3.2365666666666666, keypoints: Array(33)}98: {frameNumber: 98, timestamp: 3.2699333333333334, keypoints: Array(33)}99: {frameNumber: 99, timestamp: 3.3033, keypoints: Array(33)}[100 … 199]100: {frameNumber: 100, timestamp: 3.336666666666667, keypoints: Array(33)}101: {frameNumber: 101, timestamp: 3.370033333333333, keypoints: Array(33)}102: {frameNumber: 102, timestamp: 3.4034, keypoints: Array(33)}103: {frameNumber: 103, timestamp: 3.4367666666666667, keypoints: Array(33)}104: {frameNumber: 104, timestamp: 3.4701333333333335, keypoints: Array(33)}105: {frameNumber: 105, timestamp: 3.5035000000000003, keypoints: Array(33)}106: {frameNumber: 106, timestamp: 3.5368666666666666, keypoints: Array(33)}107: {frameNumber: 107, timestamp: 3.5702333333333334, keypoints: Array(33)}108: {frameNumber: 108, timestamp: 3.6036, keypoints: Array(33)}109: {frameNumber: 109, timestamp: 3.636966666666667, keypoints: Array(33)}110: {frameNumber: 110, timestamp: 3.6703333333333332, keypoints: Array(33)}111: {frameNumber: 111, timestamp: 3.7037, keypoints: Array(33)}112: {frameNumber: 112, timestamp: 3.7370666666666668, keypoints: Array(33)}113: {frameNumber: 113, timestamp: 3.7704333333333335, keypoints: Array(33)}114: {frameNumber: 114, timestamp: 3.8038, keypoints: Array(33)}115: {frameNumber: 115, timestamp: 3.8371666666666666, keypoints: Array(33)}116: {frameNumber: 116, timestamp: 3.8705333333333334, keypoints: Array(33)}117: {frameNumber: 117, timestamp: 3.9039, keypoints: Array(33)}118: {frameNumber: 118, timestamp: 3.937266666666667, keypoints: Array(33)}119: {frameNumber: 119, timestamp: 3.9706333333333332, keypoints: Array(33)}120: {frameNumber: 120, timestamp: 4.0040000000000004, keypoints: Array(33)}121: {frameNumber: 121, timestamp: 4.037366666666666, keypoints: Array(33)}122: {frameNumber: 122, timestamp: 4.070733333333333, keypoints: Array(33)}123: {frameNumber: 123, timestamp: 4.1041, keypoints: Array(33)}124: {frameNumber: 124, timestamp: 4.137466666666667, keypoints: Array(33)}125: {frameNumber: 125, timestamp: 4.170833333333333, keypoints: Array(33)}126: {frameNumber: 126, timestamp: 4.2042, keypoints: Array(33)}127: {frameNumber: 127, timestamp: 4.237566666666667, keypoints: Array(33)}128: {frameNumber: 128, timestamp: 4.270933333333334, keypoints: Array(33)}129: {frameNumber: 129, timestamp: 4.3043000000000005, keypoints: Array(33)}130: {frameNumber: 130, timestamp: 4.337666666666666, keypoints: Array(33)}131: {frameNumber: 131, timestamp: 4.371033333333333, keypoints: Array(33)}132: {frameNumber: 132, timestamp: 4.4044, keypoints: Array(33)}133: {frameNumber: 133, timestamp: 4.437766666666667, keypoints: Array(33)}134: {frameNumber: 134, timestamp: 4.471133333333333, keypoints: Array(33)}135: {frameNumber: 135, timestamp: 4.5045, keypoints: Array(33)}136: {frameNumber: 136, timestamp: 4.537866666666667, keypoints: Array(33)}137: {frameNumber: 137, timestamp: 4.571233333333334, keypoints: Array(33)}138: {frameNumber: 138, timestamp: 4.6046000000000005, keypoints: Array(33)}139: {frameNumber: 139, timestamp: 4.637966666666666, keypoints: Array(33)}140: {frameNumber: 140, timestamp: 4.671333333333333, keypoints: Array(33)}141: {frameNumber: 141, timestamp: 4.7047, keypoints: Array(33)}142: {frameNumber: 142, timestamp: 4.738066666666667, keypoints: Array(33)}143: {frameNumber: 143, timestamp: 4.771433333333333, keypoints: Array(33)}144: {frameNumber: 144, timestamp: 4.8048, keypoints: Array(33)}145: {frameNumber: 145, timestamp: 4.838166666666667, keypoints: Array(33)}146: {frameNumber: 146, timestamp: 4.871533333333334, keypoints: Array(33)}147: {frameNumber: 147, timestamp: 4.9049000000000005, keypoints: Array(33)}148: {frameNumber: 148, timestamp: 4.938266666666666, keypoints: Array(33)}149: {frameNumber: 149, timestamp: 4.971633333333333, keypoints: Array(33)}150: {frameNumber: 150, timestamp: 5.005, keypoints: Array(33)}151: {frameNumber: 151, timestamp: 5.038366666666667, keypoints: Array(33)}152: {frameNumber: 152, timestamp: 5.071733333333333, keypoints: Array(33)}153: {frameNumber: 153, timestamp: 5.1051, keypoints: Array(33)}154: {frameNumber: 154, timestamp: 5.138466666666667, keypoints: Array(33)}155: {frameNumber: 155, timestamp: 5.171833333333334, keypoints: Array(33)}156: {frameNumber: 156, timestamp: 5.2052000000000005, keypoints: Array(33)}157: {frameNumber: 157, timestamp: 5.238566666666666, keypoints: Array(33)}158: {frameNumber: 158, timestamp: 5.271933333333333, keypoints: Array(33)}159: {frameNumber: 159, timestamp: 5.3053, keypoints: Array(33)}160: {frameNumber: 160, timestamp: 5.338666666666667, keypoints: Array(33)}161: {frameNumber: 161, timestamp: 5.372033333333333, keypoints: Array(33)}162: {frameNumber: 162, timestamp: 5.4054, keypoints: Array(33)}163: {frameNumber: 163, timestamp: 5.438766666666667, keypoints: Array(33)}164: {frameNumber: 164, timestamp: 5.472133333333334, keypoints: Array(33)}165: {frameNumber: 165, timestamp: 5.5055000000000005, keypoints: Array(33)}166: {frameNumber: 166, timestamp: 5.538866666666666, keypoints: Array(33)}167: {frameNumber: 167, timestamp: 5.572233333333333, keypoints: Array(33)}168: {frameNumber: 168, timestamp: 5.6056, keypoints: Array(33)}169: {frameNumber: 169, timestamp: 5.638966666666667, keypoints: Array(33)}170: {frameNumber: 170, timestamp: 5.6723333333333334, keypoints: Array(33)}171: {frameNumber: 171, timestamp: 5.7057, keypoints: Array(33)}172: {frameNumber: 172, timestamp: 5.739066666666667, keypoints: Array(33)}173: {frameNumber: 173, timestamp: 5.772433333333334, keypoints: Array(33)}174: {frameNumber: 174, timestamp: 5.8058000000000005, keypoints: Array(33)}175: {frameNumber: 175, timestamp: 5.839166666666666, keypoints: Array(33)}176: {frameNumber: 176, timestamp: 5.872533333333333, keypoints: Array(33)}177: {frameNumber: 177, timestamp: 5.9059, keypoints: Array(33)}178: {frameNumber: 178, timestamp: 5.939266666666667, keypoints: Array(33)}179: {frameNumber: 179, timestamp: 5.9726333333333335, keypoints: Array(33)}180: {frameNumber: 180, timestamp: 6.006, keypoints: Array(33)}181: {frameNumber: 181, timestamp: 6.039366666666667, keypoints: Array(33)}182: {frameNumber: 182, timestamp: 6.072733333333334, keypoints: Array(33)}183: {frameNumber: 183, timestamp: 6.1061000000000005, keypoints: Array(33)}184: {frameNumber: 184, timestamp: 6.139466666666666, keypoints: Array(33)}185: {frameNumber: 185, timestamp: 6.172833333333333, keypoints: Array(33)}186: {frameNumber: 186, timestamp: 6.2062, keypoints: Array(33)}187: {frameNumber: 187, timestamp: 6.239566666666667, keypoints: Array(33)}188: {frameNumber: 188, timestamp: 6.2729333333333335, keypoints: Array(33)}189: {frameNumber: 189, timestamp: 6.3063, keypoints: Array(33)}190: {frameNumber: 190, timestamp: 6.339666666666667, keypoints: Array(33)}191: {frameNumber: 191, timestamp: 6.373033333333334, keypoints: Array(33)}192: {frameNumber: 192, timestamp: 6.4064000000000005, keypoints: Array(33)}193: {frameNumber: 193, timestamp: 6.439766666666666, keypoints: Array(33)}194: {frameNumber: 194, timestamp: 6.473133333333333, keypoints: Array(33)}195: {frameNumber: 195, timestamp: 6.5065, keypoints: Array(33)}196: {frameNumber: 196, timestamp: 6.539866666666667, keypoints: Array(33)}197: {frameNumber: 197, timestamp: 6.5732333333333335, keypoints: Array(33)}198: {frameNumber: 198, timestamp: 6.6066, keypoints: Array(33)}199: {frameNumber: 199, timestamp: 6.639966666666667, keypoints: Array(33)}[200 … 284]length: 285[[Prototype]]: Array(0)at: ƒ at()concat: ƒ concat()constructor: ƒ Array()copyWithin: ƒ copyWithin()entries: ƒ entries()every: ƒ every()fill: ƒ fill()filter: ƒ filter()find: ƒ find()findIndex: ƒ findIndex()findLast: ƒ findLast()findLastIndex: ƒ findLastIndex()flat: ƒ flat()flatMap: ƒ flatMap()forEach: ƒ forEach()includes: ƒ includes()indexOf: ƒ indexOf()join: ƒ join()keys: ƒ keys()lastIndexOf: ƒ lastIndexOf()length: 0map: ƒ map()pop: ƒ pop()push: ƒ push()reduce: ƒ reduce()reduceRight: ƒ reduceRight()reverse: ƒ reverse()shift: ƒ shift()slice: ƒ slice()some: ƒ some()sort: ƒ sort()splice: ƒ splice()toLocaleString: ƒ toLocaleString()toReversed: ƒ toReversed()toSorted: ƒ toSorted()toSpliced: ƒ toSpliced()toString: ƒ toString()unshift: ƒ unshift()values: ƒ values()with: ƒ with()Symbol(Symbol.iterator): ƒ values()Symbol(Symbol.unscopables): {at: true, copyWithin: true, entries: true, fill: true, find: true, …}[[Prototype]]: ObjectmodelType: "heavy_smoothnet"processingTime: 35.004766video: "Michael_test_side.mp4"videoHeight: 1920videoWidth: 1080viewType: "side"[[Prototype]]: Object
error_handler.js:1 Video error: SyntheticBaseEventbubbles: falsecancelable: truecurrentTarget: nulldefaultPrevented: falseeventPhase: 2isDefaultPrevented: ƒ functionThatReturnsFalse()length: 0name: "functionThatReturnsFalse"prototype: {}arguments: (...)caller: (...)[[FunctionLocation]]: react-dom.development.js:6824[[Prototype]]: ƒ ()[[Scopes]]: Scopes[2]isPropagationStopped: ƒ functionThatReturnsFalse()isTrusted: truenativeEvent: Event {isTrusted: true, type: 'error', target: video, currentTarget: null, eventPhase: 0, …}target: videotimeStamp: 412.89999997615814type: "error"_reactName: "onError"_targetInst: null[[Prototype]]: Object
wr.error @ error_handler.js:1
Inline Babel script:245 Uncaught (in promise) NotSupportedError: The element has no supported sources.
Inline Babel script:245 Uncaught (in promise) NotSupportedError: The element has no supported sources.
Inline Babel script:245 Uncaught (in promise) NotSupportedError: The element has no supported sources.
Inline Babel script:245 Uncaught (in promise) NotSupportedError: The element has no supported sources.
