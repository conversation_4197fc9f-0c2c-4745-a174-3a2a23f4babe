dev-app.html:481 {"timestamp":"10:16:47 PM","type":"info","message":"Setting TensorFlow.js backend to WebGL...","data":null}
dev-app.html:481 {"timestamp":"10:16:47 PM","type":"info","message":"TensorFlow.js backend initialized: webgl","data":null}
dev-app.html:481 {"timestamp":"10:16:47 PM","type":"info","message":"Loading BlazePose Full model from default CDN...","data":null}
dev-app.html:481 {"timestamp":"10:16:47 PM","type":"info","message":"BlazePose detector created successfully","data":null}
dev-app.html:481 {"timestamp":"10:16:47 PM","type":"info","message":"Validating detector configuration...","data":null}
dev-app.html:481 {"timestamp":"10:16:47 PM","type":"info","message":"Detector class: e","data":null}
dev-app.html:481 {"timestamp":"10:16:48 PM","type":"info","message":"Test result: 0 poses detected","data":null}
dev-app.html:481 {"timestamp":"10:16:48 PM","type":"info","message":"Validation passed: No pose detected on test pattern (expected behavior)","data":null}
dev-app.html:481 {"timestamp":"10:16:48 PM","type":"info","message":"BlazePose detector fully initialized and tested","data":null}
dev-app.html:481 {"timestamp":"10:17:05 PM","type":"info","message":"Video loaded: 1080x1920, Canvas: 640x1137","data":{"videoRes":{"width":1080,"height":1920},"canvasRes":{"width":640,"height":1137},"aspectRatio":1.7777777777777777}}
dev-app.html:481 {"timestamp":"10:17:15 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:571 [Violation] 'click' handler took 597ms
dev-app.html:481 {"timestamp":"10:17:16 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
favicon.ico:1  GET http://localhost:8080/favicon.ico 404 (File not found)
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:34 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:35 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:36 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:37 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:38 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"debug","message":"Detector input: Canvas 640x1137","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
dev-app.html:481 {"timestamp":"10:17:39 PM","type":"error","message":"INVALID POSE: Expected 39 keypoints, got 33 - STOPPING PROCESSING","data":null}
