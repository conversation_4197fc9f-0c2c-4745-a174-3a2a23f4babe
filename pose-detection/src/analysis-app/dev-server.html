<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlazePose Analysis App - Dev Environment</title>
    
    <!-- React and ReactDOM from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Babel Standalone for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-core@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-converter@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-webgl@4.15.0"></script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
        }
        
        /* Tailwind-like utility classes */
        .min-h-screen { min-height: 100vh; }
        .bg-gray-50 { background-color: #f9fafb; }
        .bg-white { background-color: white; }
        .shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
        .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }
        .border-b { border-bottom: 1px solid #e5e7eb; }
        .rounded-lg { border-radius: 8px; }
        .p-4 { padding: 1rem; }
        .p-6 { padding: 1.5rem; }
        .p-8 { padding: 2rem; }
        .px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
        .py-4 { padding-top: 1rem; padding-bottom: 1rem; }
        .space-y-2 > * + * { margin-top: 0.5rem; }
        .space-y-4 > * + * { margin-top: 1rem; }
        .space-y-6 > * + * { margin-top: 1.5rem; }
        .space-y-8 > * + * { margin-top: 2rem; }
        .space-x-4 > * + * { margin-left: 1rem; }
        .text-center { text-align: center; }
        .text-sm { font-size: 0.875rem; }
        .text-lg { font-size: 1.125rem; }
        .text-2xl { font-size: 1.5rem; }
        .text-3xl { font-size: 1.875rem; }
        .text-4xl { font-size: 2.25rem; }
        .font-medium { font-weight: 500; }
        .font-semibold { font-weight: 600; }
        .font-bold { font-weight: 700; }
        .text-gray-500 { color: #6b7280; }
        .text-gray-600 { color: #4b5563; }
        .text-gray-700 { color: #374151; }
        .text-gray-800 { color: #1f2937; }
        .text-gray-900 { color: #111827; }
        .text-blue-600 { color: #2563eb; }
        .text-green-600 { color: #059669; }
        .text-orange-600 { color: #ea580c; }
        .text-red-600 { color: #dc2626; }
        .bg-gray-100 { background-color: #f3f4f6; }
        .bg-gray-200 { background-color: #e5e7eb; }
        .bg-gray-300 { background-color: #d1d5db; }
        .bg-green-100 { background-color: #d1fae5; }
        .bg-orange-100 { background-color: #fed7aa; }
        .bg-red-100 { background-color: #fee2e2; }
        .bg-blue-50 { background-color: #eff6ff; }
        .bg-blue-600 { background-color: #2563eb; }
        .bg-blue-700 { background-color: #1d4ed8; }
        .text-white { color: white; }
        .border { border: 1px solid #e5e7eb; }
        .border-2 { border-width: 2px; }
        .border-gray-200 { border-color: #e5e7eb; }
        .border-gray-300 { border-color: #d1d5db; }
        .border-dashed { border-style: dashed; }
        .border-blue-400 { border-color: #60a5fa; }
        .grid { display: grid; }
        .flex { display: flex; }
        .inline-flex { display: inline-flex; }
        .items-center { align-items: center; }
        .justify-center { justify-content: center; }
        .justify-between { justify-content: space-between; }
        .gap-2 { gap: 0.5rem; }
        .gap-3 { gap: 0.75rem; }
        .gap-4 { gap: 1rem; }
        .gap-6 { gap: 1.5rem; }
        .gap-8 { gap: 2rem; }
        .max-w-xs { max-width: 20rem; }
        .max-w-3xl { max-width: 48rem; }
        .max-w-4xl { max-width: 56rem; }
        .max-w-7xl { max-width: 80rem; }
        .mx-auto { margin-left: auto; margin-right: auto; }
        .mt-2 { margin-top: 0.5rem; }
        .mt-4 { margin-top: 1rem; }
        .mt-8 { margin-top: 2rem; }
        .mb-2 { margin-bottom: 0.5rem; }
        .mb-4 { margin-bottom: 1rem; }
        .mb-6 { margin-bottom: 1.5rem; }
        .w-full { width: 100%; }
        .h-auto { height: auto; }
        .w-3 { width: 0.75rem; }
        .h-3 { height: 0.75rem; }
        .w-5 { width: 1.25rem; }
        .h-5 { height: 1.25rem; }
        .w-6 { width: 1.5rem; }
        .h-6 { height: 1.5rem; }
        .w-10 { width: 2.5rem; }
        .h-10 { height: 2.5rem; }
        .w-12 { width: 3rem; }
        .h-12 { height: 3rem; }
        .w-20 { width: 5rem; }
        .h-2 { height: 0.5rem; }
        .h-4 { height: 1rem; }
        .h-32 { height: 8rem; }
        .h-48 { height: 12rem; }
        .max-h-48 { max-height: 12rem; }
        .overflow-hidden { overflow: hidden; }
        .overflow-y-auto { overflow-y: auto; }
        .rounded-full { border-radius: 9999px; }
        .rounded-md { border-radius: 0.375rem; }
        .object-cover { object-fit: cover; }
        .object-contain { object-fit: contain; }
        .relative { position: relative; }
        .absolute { position: absolute; }
        .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
        .z-10 { z-index: 10; }
        .transition-all { transition: all 150ms; }
        .transition-colors { transition: color 150ms; }
        .duration-200 { transition-duration: 200ms; }
        .duration-300 { transition-duration: 300ms; }
        .ease-out { transition-timing-function: ease-out; }
        .cursor-pointer { cursor: pointer; }
        .cursor-not-allowed { cursor: not-allowed; }
        .animate-spin { animation: spin 1s linear infinite; }
        .animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
        .hidden { display: none; }
        .block { display: block; }
        .capitalize { text-transform: capitalize; }
        .pointer-events-none { pointer-events: none; }
        .flex-1 { flex: 1; }
        .hover\:bg-gray-50:hover { background-color: #f9fafb; }
        .hover\:bg-blue-700:hover { background-color: #1d4ed8; }
        .hover\:text-gray-900:hover { color: #111827; }
        .hover\:text-red-500:hover { color: #ef4444; }
        .hover\:border-blue-400:hover { border-color: #60a5fa; }
        .hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }
        .focus\:outline-none:focus { outline: none; }
        .focus\:ring-2:focus { box-shadow: 0 0 0 2px; }
        .focus\:ring-blue-500:focus { box-shadow: 0 0 0 2px #3b82f6; }
        .focus\:border-blue-500:focus { border-color: #3b82f6; }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        @media (min-width: 768px) {
            .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
            .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
            .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
        }
        
        @media (min-width: 1024px) {
            .lg\:grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
            .lg\:col-span-3 { grid-column: span 3 / span 3; }
            .lg\:col-span-4 { grid-column: span 4 / span 4; }
        }
        
        button {
            border: none;
            cursor: pointer;
            font-size: inherit;
            font-family: inherit;
        }
        
        select, input {
            font-family: inherit;
            font-size: inherit;
        }
        
        .video-container video {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .canvas-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        // Import pose-detection from the built bundle
        // Note: In development, we'll use a mock implementation
        // In production, this would load from the built dist/pose-detection.min.js
        
        const poseDetection = {
            SupportedModels: {
                BlazePose: 'BlazePose'
            },
            createDetector: async (model, config) => {
                console.log('Creating BlazePose detector with config:', config);
                
                // Simulate detector loading
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                return {
                    estimatePoses: async (video, config) => {
                        // Simulate pose detection
                        const keypoints = [];
                        for (let i = 0; i < 39; i++) {
                            keypoints.push({
                                x: Math.random() * 640,
                                y: Math.random() * 480,
                                z: Math.random() * 0.5,
                                score: 0.7 + Math.random() * 0.3,
                                name: `keypoint_${i}`
                            });
                        }
                        
                        return [{
                            keypoints,
                            keypoints3D: keypoints,
                            score: 0.85 + Math.random() * 0.15
                        }];
                    },
                    reset: () => console.log('Detector reset'),
                    dispose: () => console.log('Detector disposed')
                };
            }
        };

        // Mock Components (simplified versions for development)
        const { useState, useEffect, useRef, useCallback } = React;

        // VideoPlayer Component
        const VideoPlayer = ({ videoUrl, userHeight, onPoseData }) => {
            const videoRef = useRef(null);
            const canvasRef = useRef(null);
            const [isProcessing, setIsProcessing] = useState(false);
            
            useEffect(() => {
                const video = videoRef.current;
                const canvas = canvasRef.current;
                if (!video || !canvas) return;
                
                const processFrame = () => {
                    if (!video.paused && !video.ended) {
                        const ctx = canvas.getContext('2d');
                        canvas.width = video.videoWidth;
                        canvas.height = video.videoHeight;
                        
                        // Draw video frame
                        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                        
                        // Simulate pose overlay
                        ctx.fillStyle = 'rgba(0, 255, 0, 0.8)';
                        for (let i = 0; i < 10; i++) {
                            const x = Math.random() * canvas.width;
                            const y = Math.random() * canvas.height;
                            ctx.beginPath();
                            ctx.arc(x, y, 4, 0, 2 * Math.PI);
                            ctx.fill();
                        }
                        
                        // Send mock pose data
                        if (onPoseData) {
                            onPoseData({
                                keypoints: Array(39).fill(null).map((_, i) => ({
                                    x: Math.random() * canvas.width,
                                    y: Math.random() * canvas.height,
                                    score: 0.8 + Math.random() * 0.2
                                })),
                                timestamp: video.currentTime * 1000
                            });
                        }
                        
                        requestAnimationFrame(processFrame);
                    }
                };
                
                video.addEventListener('play', () => {
                    setIsProcessing(true);
                    processFrame();
                });
                
                video.addEventListener('pause', () => setIsProcessing(false));
                video.addEventListener('ended', () => setIsProcessing(false));
            }, [onPoseData]);
            
            return (
                <div className="relative bg-black rounded-lg overflow-hidden">
                    <video
                        ref={videoRef}
                        src={videoUrl}
                        className="w-full h-auto"
                        controls
                    />
                    <canvas
                        ref={canvasRef}
                        className="canvas-overlay"
                        style={{ display: isProcessing ? 'block' : 'none' }}
                    />
                </div>
            );
        };

        // UploadPanel Component
        const UploadPanel = ({ title, description, video, onVideoUpload }) => {
            const fileInputRef = useRef(null);
            const [isDragging, setIsDragging] = useState(false);
            
            const handleFileSelect = (file) => {
                if (file && file.type.startsWith('video/')) {
                    const videoFile = {
                        file,
                        url: URL.createObjectURL(file),
                        name: file.name
                    };
                    onVideoUpload(videoFile);
                }
            };
            
            const handleDragOver = (e) => {
                e.preventDefault();
                setIsDragging(true);
            };
            
            const handleDragLeave = (e) => {
                e.preventDefault();
                setIsDragging(false);
            };
            
            const handleDrop = (e) => {
                e.preventDefault();
                setIsDragging(false);
                const file = e.dataTransfer.files[0];
                handleFileSelect(file);
            };
            
            return (
                <div className="bg-white rounded-lg border p-6">
                    <h3 className="text-lg font-semibold mb-2">{title}</h3>
                    <p className="text-sm text-gray-600 mb-4">{description}</p>
                    
                    {!video ? (
                        <div
                            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all ${
                                isDragging ? 'border-blue-400 bg-blue-50' : 'border-gray-300 hover:border-blue-400'
                            }`}
                            onDragOver={handleDragOver}
                            onDragLeave={handleDragLeave}
                            onDrop={handleDrop}
                            onClick={() => fileInputRef.current?.click()}
                        >
                            <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-4">
                                <span className="text-2xl">📹</span>
                            </div>
                            <p className="text-lg font-medium mb-1">Drop video here or click to upload</p>
                            <p className="text-sm text-gray-500">MP4, MOV, AVI or WebM • Max 100MB</p>
                            <input
                                ref={fileInputRef}
                                type="file"
                                accept="video/*"
                                className="hidden"
                                onChange={(e) => handleFileSelect(e.target.files[0])}
                            />
                        </div>
                    ) : (
                        <div className="space-y-4">
                            <div className="bg-gray-50 rounded-lg p-4 border">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="font-medium">{video.name}</p>
                                        <p className="text-sm text-gray-500">Ready for analysis</p>
                                    </div>
                                    <button
                                        onClick={() => onVideoUpload(null)}
                                        className="text-gray-400 hover:text-red-500"
                                    >
                                        ✕
                                    </button>
                                </div>
                            </div>
                            <video src={video.url} className="w-full rounded-lg" controls />
                        </div>
                    )}
                </div>
            );
        };

        // ConfigurationPanel Component
        const ConfigurationPanel = ({ userConfig, onConfigChange }) => {
            return (
                <div className="bg-white rounded-lg border p-6">
                    <h3 className="text-lg font-semibold mb-6 text-center">User Information</h3>
                    
                    <div className="grid md:grid-cols-3 gap-8">
                        <div className="text-center">
                            <label className="block text-sm font-medium text-gray-700 mb-3">
                                Height <span className="text-red-600">*</span>
                            </label>
                            <div className="flex gap-2 justify-center">
                                <select
                                    value={userConfig.height.feet}
                                    onChange={(e) => onConfigChange({
                                        ...userConfig,
                                        height: { ...userConfig.height, feet: parseInt(e.target.value) }
                                    })}
                                    className="px-3 py-2 border rounded-md"
                                >
                                    {[4, 5, 6, 7].map(ft => (
                                        <option key={ft} value={ft}>{ft} ft</option>
                                    ))}
                                </select>
                                <select
                                    value={userConfig.height.inches}
                                    onChange={(e) => onConfigChange({
                                        ...userConfig,
                                        height: { ...userConfig.height, inches: parseInt(e.target.value) }
                                    })}
                                    className="px-3 py-2 border rounded-md"
                                >
                                    {Array.from({length: 12}, (_, i) => (
                                        <option key={i} value={i}>{i} in</option>
                                    ))}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // Main App Component
        const AnalysisApp = () => {
            const [currentState, setCurrentState] = useState('upload');
            const [sideVideo, setSideVideo] = useState(null);
            const [userConfig, setUserConfig] = useState({
                height: { feet: 5, inches: 10 },
                weightUnit: 'lbs'
            });
            
            const canStartAnalysis = sideVideo !== null;
            
            const handleStartAnalysis = () => {
                if (canStartAnalysis) {
                    console.log('Starting analysis...');
                    setCurrentState('processing');
                    setTimeout(() => {
                        setCurrentState('results');
                    }, 3000);
                }
            };
            
            const handleNewAnalysis = () => {
                setCurrentState('upload');
                setSideVideo(null);
            };
            
            if (currentState === 'processing') {
                return (
                    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                        <div className="bg-white rounded-lg shadow-lg p-8 max-w-2xl w-full">
                            <div className="text-center space-y-6">
                                <h2 className="text-3xl font-bold">3D Pose Processing</h2>
                                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
                                <p className="text-lg text-gray-600">Processing BlazePose Full...</p>
                            </div>
                        </div>
                    </div>
                );
            }
            
            if (currentState === 'results') {
                return (
                    <div className="min-h-screen bg-gray-50">
                        <div className="bg-white shadow-sm border-b p-4">
                            <h1 className="text-2xl font-bold text-center">Running Analysis Results</h1>
                        </div>
                        <div className="max-w-7xl mx-auto p-6">
                            <div className="grid lg:grid-cols-2 gap-6">
                                <div>
                                    {sideVideo && (
                                        <VideoPlayer
                                            videoUrl={sideVideo.url}
                                            userHeight={userConfig.height}
                                            onPoseData={(data) => console.log('Pose data:', data)}
                                        />
                                    )}
                                </div>
                                <div className="space-y-4">
                                    <div className="bg-white rounded-lg shadow-lg p-6">
                                        <h3 className="text-lg font-semibold mb-4">Movement Analysis</h3>
                                        <div className="space-y-3">
                                            <div className="flex justify-between">
                                                <span>Stride Length</span>
                                                <span className="text-green-600">1.2m - Excellent</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Cadence</span>
                                                <span className="text-green-600">180 steps/min - Excellent</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div className="text-center mt-8">
                                <button
                                    onClick={handleNewAnalysis}
                                    className="px-8 py-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                                >
                                    Start New Analysis
                                </button>
                            </div>
                        </div>
                    </div>
                );
            }
            
            return (
                <div className="min-h-screen bg-gray-50">
                    <div className="bg-white shadow-sm border-b p-4">
                        <div className="text-center">
                            <h1 className="text-4xl font-bold">🏃‍♂️ BlazePose Running Analysis</h1>
                            <p className="text-lg text-gray-600 mt-2">
                                Upload your treadmill running video for analysis
                            </p>
                        </div>
                    </div>
                    
                    <div className="max-w-7xl mx-auto p-6 space-y-8">
                        <div className="grid md:grid-cols-2 gap-6">
                            <UploadPanel
                                title="Upload Side View Video"
                                description="Record from the side to capture leg extension"
                                video={sideVideo}
                                onVideoUpload={setSideVideo}
                            />
                            <div className="bg-gray-100 rounded-lg p-6 flex items-center justify-center">
                                <p className="text-gray-500 text-center">
                                    Rear view upload (optional) available in full version
                                </p>
                            </div>
                        </div>
                        
                        <ConfigurationPanel
                            userConfig={userConfig}
                            onConfigChange={setUserConfig}
                        />
                        
                        <div className="text-center">
                            <button
                                onClick={handleStartAnalysis}
                                disabled={!canStartAnalysis}
                                className={`px-8 py-4 rounded-lg text-lg font-semibold ${
                                    canStartAnalysis
                                        ? 'bg-blue-600 text-white hover:bg-blue-700'
                                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                }`}
                            >
                                Start 3D Analysis
                            </button>
                            {!canStartAnalysis && (
                                <p className="text-sm text-gray-500 mt-2">
                                    Please upload a side view video to begin
                                </p>
                            )}
                        </div>
                    </div>
                </div>
            );
        };

        // Render the app
        ReactDOM.render(<AnalysisApp />, document.getElementById('root'));
        
        console.log('🚀 BlazePose Analysis App - Dev Environment Ready');
        console.log('📹 Upload a video to test the analysis workflow');
    </script>
</body>
</html>