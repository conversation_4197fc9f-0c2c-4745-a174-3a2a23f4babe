<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BlazePose Video Analysis Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .upload-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .upload-zone {
            border: 3px dashed #cbd5e1;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-zone:hover {
            border-color: #2563eb;
            background: #f8faff;
        }
        
        .upload-zone.dragover {
            border-color: #2563eb;
            background: #eff6ff;
        }
        
        .video-container {
            margin-top: 20px;
            position: relative;
        }
        
        .video-player {
            width: 100%;
            border-radius: 10px;
            background: black;
        }
        
        .canvas-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            border-radius: 10px;
        }
        
        .status-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .status-item {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .metrics-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .metric-item:last-child {
            border-bottom: none;
        }
        
        .hidden {
            display: none;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e5e7eb;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #10b981);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }
        
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }
        
        button:hover {
            background: #1d4ed8;
        }
        
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        
        .height-inputs {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin: 20px 0;
        }
        
        .height-inputs select {
            padding: 8px 12px;
            border: 2px solid #d1d5db;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🏃‍♂️ BlazePose Video Analysis Test</h1>
            <p>Test BlazePose TFJS integration with video analysis and pose overlay</p>
        </div>

        <!-- Status Section -->
        <div class="status-section">
            <h3>BlazePose Status</h3>
            <div id="status" class="status-item">Initializing...</div>
            <div id="progress-container" class="hidden">
                <div class="progress-bar">
                    <div id="progress-fill" class="progress-fill"></div>
                </div>
                <div id="progress-text">0% Complete</div>
            </div>
        </div>

        <!-- Height Configuration -->
        <div class="status-section">
            <h3>User Configuration</h3>
            <div class="height-inputs">
                <label>Height:</label>
                <select id="height-feet">
                    <option value="4">4 ft</option>
                    <option value="5" selected>5 ft</option>
                    <option value="6">6 ft</option>
                    <option value="7">7 ft</option>
                </select>
                <select id="height-inches">
                    <option value="0">0 in</option>
                    <option value="1">1 in</option>
                    <option value="2">2 in</option>
                    <option value="3">3 in</option>
                    <option value="4">4 in</option>
                    <option value="5">5 in</option>
                    <option value="6">6 in</option>
                    <option value="7">7 in</option>
                    <option value="8">8 in</option>
                    <option value="9">9 in</option>
                    <option value="10" selected>10 in</option>
                    <option value="11">11 in</option>
                </select>
                <span id="height-display">(1.78m)</span>
            </div>
        </div>

        <!-- Upload Section -->
        <div class="upload-section">
            <h3>Upload Video for Analysis</h3>
            <div id="upload-zone" class="upload-zone">
                <div>
                    <h4>📹 Drop video here or click to select</h4>
                    <p>Supports MP4, MOV, AVI, WebM • Max 100MB</p>
                    <p style="font-size: 12px; color: #666; margin-top: 10px;">
                        Record from 5 feet away • Side view • Landscape orientation
                    </p>
                </div>
                <input type="file" id="video-input" accept="video/*" style="display: none;">
            </div>
            
            <div id="video-container" class="video-container hidden">
                <video id="video-player" class="video-player" controls></video>
                <canvas id="pose-canvas" class="canvas-overlay"></canvas>
            </div>
        </div>

        <!-- Metrics Section -->
        <div id="metrics-section" class="metrics-section hidden">
            <h3>Analysis Metrics</h3>
            <div id="metrics-list">
                <div class="metric-item">
                    <span>Frames Processed:</span>
                    <span id="frames-count">0</span>
                </div>
                <div class="metric-item">
                    <span>Average Confidence:</span>
                    <span id="avg-confidence">0%</span>
                </div>
                <div class="metric-item">
                    <span>Keypoints Detected:</span>
                    <span id="keypoints-count">0</span>
                </div>
                <div class="metric-item">
                    <span>Processing FPS:</span>
                    <span id="processing-fps">0</span>
                </div>
            </div>
        </div>
    </div>

    <!-- TensorFlow.js and dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-core@4.15.0/dist/tf-core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-converter@4.15.0/dist/tf-converter.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-webgl@4.15.0/dist/tf-backend-webgl.min.js"></script>

    <script>
        // Global variables
        let detector = null;
        let currentVideo = null;
        let isProcessing = false;
        let frameCount = 0;
        let totalConfidence = 0;
        let lastFrameTime = 0;
        let animationFrame = null;

        // DOM elements
        const statusElement = document.getElementById('status');
        const uploadZone = document.getElementById('upload-zone');
        const videoInput = document.getElementById('video-input');
        const videoContainer = document.getElementById('video-container');
        const videoPlayer = document.getElementById('video-player');
        const poseCanvas = document.getElementById('pose-canvas');
        const metricsSection = document.getElementById('metrics-section');
        const progressContainer = document.getElementById('progress-container');
        const progressFill = document.getElementById('progress-fill');
        const progressText = document.getElementById('progress-text');

        // Height inputs
        const heightFeet = document.getElementById('height-feet');
        const heightInches = document.getElementById('height-inches');
        const heightDisplay = document.getElementById('height-display');

        // Update height display
        function updateHeightDisplay() {
            const feet = parseInt(heightFeet.value);
            const inches = parseInt(heightInches.value);
            const totalInches = feet * 12 + inches;
            const meters = (totalInches * 0.0254).toFixed(2);
            heightDisplay.textContent = `(${meters}m)`;
        }

        heightFeet.addEventListener('change', updateHeightDisplay);
        heightInches.addEventListener('change', updateHeightDisplay);

        // Initialize BlazePose
        async function initializeBlazePose() {
            try {
                updateStatus('Loading BlazePose Full model...', 'loading');
                
                console.log('🚀 Initializing BlazePose...');
                
                // Note: In actual implementation, you would import from your built pose-detection module
                // For this test, we'll simulate the initialization
                
                // Simulated initialization delay
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('✅ BlazePose initialized successfully');
                updateStatus('BlazePose Full model ready', 'ready');
                
                return true; // Simulated detector
            } catch (error) {
                console.error('❌ Failed to initialize BlazePose:', error);
                updateStatus(`Error: ${error.message}`, 'error');
                return null;
            }
        }

        // Update status display
        function updateStatus(message, type = 'info') {
            statusElement.textContent = message;
            statusElement.className = `status-item status-${type}`;
        }

        // Handle file upload
        function handleFileUpload(file) {
            if (!file || !file.type.startsWith('video/')) {
                alert('Please select a valid video file.');
                return;
            }

            if (file.size > 100 * 1024 * 1024) {
                alert('File too large. Maximum size is 100MB.');
                return;
            }

            currentVideo = file;
            const videoUrl = URL.createObjectURL(file);
            
            videoPlayer.src = videoUrl;
            videoContainer.classList.remove('hidden');
            metricsSection.classList.remove('hidden');
            
            console.log(`📹 Video loaded: ${file.name}`);
            
            // Reset metrics
            frameCount = 0;
            totalConfidence = 0;
            updateMetrics();
        }

        // Simulated pose processing
        async function processFrame() {
            if (!currentVideo || !detector) return null;

            // Simulate pose detection
            const simulatedPose = generateSimulatedPose();
            
            frameCount++;
            totalConfidence += simulatedPose.score;
            
            // Draw pose overlay
            drawPoseOverlay(simulatedPose);
            
            // Update metrics
            updateMetrics();
            
            return simulatedPose;
        }

        // Generate simulated pose data
        function generateSimulatedPose() {
            const keypoints = [];
            
            // Generate 39 keypoints with random positions
            for (let i = 0; i < 39; i++) {
                keypoints.push({
                    x: Math.random() * poseCanvas.width,
                    y: Math.random() * poseCanvas.height,
                    z: Math.random() * 0.5,
                    score: 0.7 + Math.random() * 0.3,
                    name: `keypoint_${i}`
                });
            }
            
            return {
                keypoints,
                score: 0.8 + Math.random() * 0.2,
                timestamp: Date.now()
            };
        }

        // Draw pose overlay on canvas
        function drawPoseOverlay(poseData) {
            const canvas = poseCanvas;
            const ctx = canvas.getContext('2d');
            
            // Set canvas size to match video
            canvas.width = videoPlayer.videoWidth || 640;
            canvas.height = videoPlayer.videoHeight || 480;
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Draw keypoints
            poseData.keypoints.forEach((keypoint, index) => {
                if (keypoint.score < 0.5) return;
                
                const { x, y, score } = keypoint;
                
                // Color based on confidence
                const alpha = score;
                let color = `rgba(0, 255, 0, ${alpha})`;
                
                if (score < 0.7) color = `rgba(255, 165, 0, ${alpha})`;
                if (score < 0.5) color = `rgba(255, 0, 0, ${alpha})`;
                
                // Draw keypoint
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, 4, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // Draw info overlay
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(10, 10, 250, 60);
            
            ctx.fillStyle = '#00ff00';
            ctx.font = '14px Arial';
            ctx.fillText(`Pose Score: ${(poseData.score * 100).toFixed(1)}%`, 20, 30);
            ctx.fillText(`Keypoints: ${poseData.keypoints.length}`, 20, 50);
            ctx.fillText(`Frame: ${frameCount}`, 20, 70);
        }

        // Update metrics display
        function updateMetrics() {
            document.getElementById('frames-count').textContent = frameCount;
            document.getElementById('avg-confidence').textContent = 
                frameCount > 0 ? `${((totalConfidence / frameCount) * 100).toFixed(1)}%` : '0%';
            document.getElementById('keypoints-count').textContent = '39';
            
            const currentTime = Date.now();
            const fps = frameCount > 0 && lastFrameTime > 0 
                ? (1000 / (currentTime - lastFrameTime)).toFixed(1) 
                : '0';
            document.getElementById('processing-fps').textContent = fps;
            lastFrameTime = currentTime;
        }

        // Video processing loop
        function startProcessingLoop() {
            if (!isProcessing) return;
            
            if (videoPlayer.currentTime && !videoPlayer.paused && !videoPlayer.ended) {
                processFrame();
                
                // Update progress
                const progress = (videoPlayer.currentTime / videoPlayer.duration) * 100;
                progressFill.style.width = `${progress}%`;
                progressText.textContent = `${progress.toFixed(1)}% Complete`;
            }
            
            animationFrame = requestAnimationFrame(startProcessingLoop);
        }

        // Event listeners
        uploadZone.addEventListener('click', () => videoInput.click());
        
        uploadZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadZone.classList.add('dragover');
        });
        
        uploadZone.addEventListener('dragleave', () => {
            uploadZone.classList.remove('dragover');
        });
        
        uploadZone.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadZone.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            handleFileUpload(file);
        });
        
        videoInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            handleFileUpload(file);
        });
        
        videoPlayer.addEventListener('play', () => {
            isProcessing = true;
            progressContainer.classList.remove('hidden');
            startProcessingLoop();
        });
        
        videoPlayer.addEventListener('pause', () => {
            isProcessing = false;
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }
        });
        
        videoPlayer.addEventListener('ended', () => {
            isProcessing = false;
            progressFill.style.width = '100%';
            progressText.textContent = '100% Complete';
        });

        // Initialize on page load
        window.addEventListener('load', () => {
            console.log('🏃‍♂️ BlazePose Video Analysis Test starting...');
            updateHeightDisplay();
            initializeBlazePose().then(result => {
                detector = result;
            });
        });
    </script>
</body>
</html>