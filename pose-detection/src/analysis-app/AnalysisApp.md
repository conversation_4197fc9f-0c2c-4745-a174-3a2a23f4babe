# Analysis App Architecture Map

This document maps the existing analysis-app folder structure and identifies components we can reuse for the Modal integration.

## 🏗️ Existing Architecture Overview

**Type**: React-based biomechanical analysis application using BlazePose  
**Purpose**: Upload, process, and analyze running videos with pose detection  
**Integration**: Supabase database + S3 storage + Modal processing

---

## 📁 Folder Structure Analysis

### Root Files
- **HTML Test Pages**: `dev-app.html`, `test-app.html`, `modal-test.html`, `index.html` (various test environments)
- **Configuration**: `serve.py`, `start-dev.sh`, `.gitignore`
- **Test Data**: Multiple `.json` files with pose data, `Michael_test_side.mp4`
- **TypeScript Tests**: `blazepose_test.ts`
- **Python Debug**: `check_roi.py`, `debug_coordinates.py`

### 🧩 React Components (`components/`)
- **`AnalysisApp.tsx`**: Main orchestrator component for entire workflow
- **`ConfigurationPanel.tsx`**: User input (height, personal data)
- **`UploadPanel.tsx`**: Video upload functionality 
- **`VideoPlayer.tsx`**: Real-time BlazePose integration
- **`ProcessedVideoPlayer.tsx`**: Displays pre-processed pose data ⭐ **REUSABLE**

### 🎨 Styles (`styles/`)
- **`main.css`**: Main application stylesheet ⭐ **REUSABLE**
- **`components.css`**: Component-specific styles ⭐ **REUSABLE**
- **`form-updates.css`**: Form styling ⭐ **REUSABLE**

### ⚙️ JavaScript Logic (`js/`)
- **`app.js`**: Main application controller ⭐ **REUSABLE**
- **`navigation.js`**: Page navigation and view management ⭐ **REUSABLE** 
- **`analysis-common.js`**: Shared pose detection utilities ⭐ **REUSABLE**
- **`side-view.js`**: Side view specific logic ⭐ **REUSABLE**
- **`rear-view.js`**: Rear view specific logic ⭐ **REUSABLE**
- **`upload.js`**: File upload and validation ⭐ **REUSABLE**

### 🔧 Utilities (`utils/`)
- **`coordinates.ts`**: Coordinate transformations ⭐ **REUSABLE**
- **`runningMetrics.ts`**: Running metrics calculations ⭐ **REUSABLE**

### 🎣 React Hooks (`hooks/`)
- **`useBlazePose.ts`**: BlazePose model integration ⭐ **REUSABLE**

### 🗃️ Database (`database/`)
- **`bio-run-schema.sql`**: Complete PostgreSQL schema (videos, analyses, processing queues)

### 🌐 Supabase (`supabase/`)
- **`functions/s3-presigned/index.ts`**: S3 pre-signed URL generation

### 📝 Types (`types/`)
- **`index.ts`**: TypeScript definitions for entire application ⭐ **REUSABLE**

### ⚙️ Config (`config/`)
- **`env.js`**: Environment configuration (contains sensitive keys)

---

## 🎯 Integration Plan for Modal Results Page

Based on the existing architecture, we need to create a **3rd page** (Results View) that integrates seamlessly:

### ✅ What We Can Reuse (No Duplication)
1. **`ProcessedVideoPlayer.tsx`** - Perfect for Modal results playback
2. **`runningMetrics.ts`** - Calculate metrics from Modal JSON
3. **`coordinates.ts`** - Handle coordinate transformations
4. **`main.css` + `components.css`** - Existing styling system
5. **`navigation.js`** - Tab switching between Side/Rear views
6. **`analysis-common.js`** - Shared utilities
7. **Type definitions** - All existing TypeScript types

### 🆕 What We Need to Create
1. **`ResultsPage.tsx`** - New React component for results display
2. **`MetricsDisplay.tsx`** - Component to show calculated metrics
3. **`TabsContainer.tsx`** - Side/Rear view switching tabs
4. **`modal-integration.js`** - Connect to Modal processing status
5. **Enhanced navigation** - Add results page to existing navigation

### 🔄 Multi-Step Wizard Flow
```
Page 1: Upload (Existing) → Page 2: Processing (Existing) → Page 3: Results (NEW)
    ↓                           ↓                              ↓
UploadPanel.tsx            Processing Status              ResultsPage.tsx
                          + Modal Triggers                + Side/Rear Tabs
                                                         + Metrics Display
                                                         + Video Playback
```

### 🎯 Results Page Architecture
```
ResultsPage.tsx
├── TabsContainer (Side/Rear switching)
├── For Each Tab:
│   ├── ProcessedVideoPlayer (reuse existing)
│   ├── MetricsDisplay (new - unique per view)
│   └── PoseOverlay (extract from existing)
└── ExportOptions (future enhancement)
```

---

## 🚀 Next Steps

1. **Create `ResultsPage.tsx`** - New main results component
2. **Extract common tab logic** from existing navigation
3. **Create `MetricsDisplay.tsx`** - Use existing `runningMetrics.ts`
4. **Integrate Modal status polling** - Build on existing Supabase integration
5. **Test with existing video/JSON data**

---

**Last Updated**: July 28, 2025  
**Analysis By**: Gemini CLI + Claude Code