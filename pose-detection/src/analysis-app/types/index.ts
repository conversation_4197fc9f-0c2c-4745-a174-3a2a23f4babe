/**
 * TypeScript type definitions for the Analysis App
 * Based on reference from Index-OLD.tsx and BlazePose requirements
 */

// App State Management
export type AppState = 'upload' | 'processing' | 'results';

// Video Management
export interface VideoFile {
  file: File;
  url: string;
  name: string;
}

// User Configuration
export interface UserHeight {
  feet: number;
  inches: number;
}

export interface UserConfiguration {
  height: UserHeight;
  gender?: 'male' | 'female' | 'other' | 'prefer-not-to-say';
  weight?: number;
  weightUnit: 'lbs' | 'kg';
}

// BlazePose Analysis Configuration
export interface AnalysisConfiguration {
  analysisType: 'running';
  analysisMode: '3D';
  activityType: 'Running';
  videoSetup: 'Treadmill';
  overlayStyle: 'Medical';
  analysisQuality: 'Full';
  viewType: 'side' | 'rear';
}

// Pose Detection Results
export interface PoseKeypoint {
  x: number;
  y: number;
  z?: number;
  score?: number;
  name?: string;
}

export interface PoseData {
  keypoints: PoseKeypoint[];
  keypoints3D?: PoseKeypoint[];
  score?: number;
  timestamp: number;
}

// Processed pose data from Modal/SmoothNet
export interface ProcessedPoseData {
  keypoints: PoseKeypoint[];
  score: number;
  timestamp: number;
  frameNumber: number;
  modelType: string;
  smoothed: boolean;
}

// Running Metrics
export interface RunningMetrics {
  strideLength: {
    value: number;
    unit: 'm';
    score: number;
    status: 'excellent' | 'good' | 'needs-work';
    description: string;
  };
  cadence: {
    value: number;
    unit: 'steps/min';
    score: number;
    status: 'excellent' | 'good' | 'needs-work';
    description: string;
  };
  footStrike: {
    type: 'heel' | 'midfoot' | 'forefoot';
    score: number;
    status: 'excellent' | 'good' | 'needs-work';
    description: string;
  };
  posture: {
    alignment: number;
    score: number;
    status: 'excellent' | 'good' | 'needs-work';
    description: string;
  };
  kneeFlexion: {
    peakAngle: number;
    unit: 'degrees';
    score: number;
    status: 'excellent' | 'good' | 'needs-work';
    description: string;
  };
  groundContactTime: {
    value: number;
    unit: 'ms';
    score: number;
    status: 'excellent' | 'good' | 'needs-work';
    description: string;
  };
}

// Video Player Configuration
export interface VideoPlayerProps {
  videoUrl: string;
  analysisType: 'running';
  viewType: 'side' | 'rear';
  analysisMode: '3D';
  videoSetup: 'Treadmill';
  overlayStyle: 'Medical';
  userHeight: UserHeight;
  onPoseData?: (data: PoseData) => void;
  onMetrics?: (metrics: RunningMetrics) => void;
}

// Coordinate System
export interface CoordinateTransform {
  scale: number;
  offsetX: number;
  offsetY: number;
  pixelsPerMeter: number;
  userHeightInMeters: number;
}

// Processing Status
export interface ProcessingStatus {
  isProcessing: boolean;
  progress: number;
  currentFrame: number;
  totalFrames: number;
  elapsed: number;
  remaining: number;
  status: 'idle' | 'loading' | 'processing' | 'complete' | 'error' | 'ready' | 'playing';
  message?: string;
}

// Component Props Interfaces
export interface UploadPanelProps {
  title: string;
  description: string;
  acceptedFormats: string;
  video: VideoFile | null;
  onVideoUpload: (video: VideoFile | null) => void;
}

export interface ConfigurationPanelProps {
  userConfig: UserConfiguration;
  onConfigChange: (config: UserConfiguration) => void;
}

export interface ProcessingPanelProps {
  analysisMode: '3D';
  status: ProcessingStatus;
}

export interface ResultsPanelProps {
  sideVideo: VideoFile | null;
  rearVideo: VideoFile | null;
  viewType: 'side' | 'rear';
  analysisConfig: AnalysisConfiguration;
  userConfig: UserConfiguration;
  metrics?: RunningMetrics;
  onNewAnalysis: () => void;
  onViewTypeChange: (viewType: 'side' | 'rear') => void;
}

// Processed Video Player Props
export interface ProcessedVideoPlayerProps {
  videoUrl: string;
  resultsUrl: string;
  analysisType: 'running';
  viewType: 'side' | 'rear';
  overlayStyle: 'Medical';
  userHeight: UserHeight;
  onPoseData?: (data: ProcessedPoseData) => void;
  onMetrics?: (metrics: RunningMetrics) => void;
}