#!/usr/bin/env python3
"""
Simple HTTP server for BlazePose Analysis App development
Serves static files with proper MIME types and CORS headers
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

PORT = 8080

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """HTTP request handler with CORS headers."""
    
    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.send_header('Cache-Control', 'no-store, no-cache, must-revalidate')
        return super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def guess_type(self, path):
        mimetype = super().guess_type(path)
        if path.endswith('.js'):
            mimetype = 'application/javascript'
        elif path.endswith('.mjs'):
            mimetype = 'application/javascript'
        elif path.endswith('.json'):
            mimetype = 'application/json'
        elif path.endswith('.wasm'):
            mimetype = 'application/wasm'
        return mimetype

def serve():
    """Start the development server."""
    # Change to the script directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    with socketserver.TCPServer(("", PORT), CORSHTTPRequestHandler) as httpd:
        print(f"🚀 BlazePose Analysis App Development Server")
        print(f"📡 Serving at http://localhost:{PORT}")
        print(f"📁 Root directory: {os.getcwd()}")
        print(f"\n✨ Available endpoints:")
        print(f"   • http://localhost:{PORT}/dev-server.html - React development environment")
        print(f"   • http://localhost:{PORT}/test-app.html - Simple test environment")
        print(f"\n⌨️  Press Ctrl+C to stop the server\n")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n👋 Server stopped")
            sys.exit(0)

if __name__ == "__main__":
    serve()