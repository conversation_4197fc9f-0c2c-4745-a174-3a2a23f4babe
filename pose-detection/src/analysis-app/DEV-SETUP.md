# Development Environment Setup

## 🚀 Quick Start

Run the BlazePose Analysis App development environment locally:

```bash
# Navigate to the analysis app directory
cd /Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app

# Start the development server (Option 1)
./start-dev.sh

# OR start manually (Option 2)
python3 serve.py
```

## 📱 Access the App

Once the server starts, open your browser to:

- **React Development Environment**: http://localhost:8080/dev-server.html
- **Simple Test Environment**: http://localhost:8080/test-app.html

## 🧪 Testing Instructions

### 1. React Development Environment (`dev-server.html`)
- Full React-based interface with mock BlazePose integration
- Upload video files via drag-and-drop or file picker
- Test user height configuration
- View simulated pose analysis results
- Complete upload → processing → results workflow

### 2. Simple Test Environment (`test-app.html`)
- Lightweight HTML/JavaScript implementation
- Direct video upload and processing simulation
- Basic pose overlay visualization
- Metrics tracking and display

## 🎥 Video Requirements for Testing

- **Formats**: MP4, MOV, AVI, WebM
- **Size**: Up to 200MB
- **Content**: Running/exercise videos work best
- **Duration**: 10 seconds recommended

## 🔧 Development Features

### React Environment Features:
- ✅ Video upload with validation
- ✅ User height configuration
- ✅ Simulated pose detection
- ✅ Canvas overlay visualization
- ✅ Metrics calculation display
- ✅ State management (upload/processing/results)
- ✅ Responsive design

### Mock Implementation:
- Simulates BlazePose detector initialization
- Generates 39 mock keypoints with realistic coordinates
- Provides confidence scoring (0.7-1.0 range)
- Shows processing progress and frame analysis

## 🛠️ Development Workflow

1. **Upload Test Video**: Use the drag-and-drop interface
2. **Configure Height**: Set user height for coordinate scaling
3. **Start Analysis**: Click "Start 3D Analysis" button
4. **View Processing**: Watch simulated BlazePose processing
5. **Review Results**: See metrics and pose visualization
6. **Restart**: Click "Start New Analysis" to reset

## 📊 Expected Behavior

### Upload Phase:
- Video file validation and preview
- User configuration input
- Analysis readiness check

### Processing Phase:
- Simulated BlazePose model loading
- Real-time video playback with pose overlay
- Progress indicators and frame counting

### Results Phase:
- Running metrics display (stride, cadence, posture)
- Color-coded performance indicators
- Interactive video player with pose visualization

## 🔍 Debugging

### Browser Console:
- Monitor BlazePose initialization logs
- View pose data objects
- Track processing performance
- Check for JavaScript errors

### Network Tab:
- Verify video file loading
- Monitor TensorFlow.js model requests (in production)
- Check for CORS issues

## 📝 Notes

- This is a **development/demo environment** with mocked BlazePose functionality
- For production use, integrate with the actual built BlazePose TFJS modules
- The React components can be easily adapted for real implementation
- All TypeScript types and interfaces are production-ready

## ⚡ Performance Tips

- Use Chrome or Edge for best WebGL support
- Test with smaller video files for faster loading
- Monitor browser memory usage during extended testing
- Clear browser cache if experiencing issues

## 🔧 Customization

### Modify Mock Data:
Edit the `generateSimulatedPose()` function in `dev-server.html` to adjust:
- Number of keypoints (currently 39)
- Confidence scores
- Coordinate ranges
- Processing timing

### Add Features:
- Extend the React components
- Add new metrics calculations
- Implement additional video analysis features
- Customize the UI styling

---

**Ready to test!** 🏃‍♂️ Upload a running video and watch the BlazePose analysis in action.
