<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Results Page - React TSX Components</title>
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Babel Standalone for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Load our CSS -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/results-page.css">
    
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Mock ProcessedVideoPlayer component
        const ProcessedVideoPlayer = ({ videoUrl, onVideoLoad }) => {
            return (
                <div style={{ 
                    background: '#000', 
                    borderRadius: '8px', 
                    aspectRatio: '16/9',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white'
                }}>
                    <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: '2rem', marginBottom: '1rem' }}>📹</div>
                        <div>Processed Video Player</div>
                        <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                            {videoUrl ? `Loading: ${videoUrl}` : 'No video URL'}
                        </div>
                    </div>
                </div>
            );
        };

        // Mock TabsContainer component (inline version of our TSX)
        const TabsContainer = ({ activeTab, availableViews, onTabChange }) => {
            const getTabLabel = (view) => view === 'side' ? 'Side View' : 'Rear View';
            const getTabIcon = (view) => view === 'side' ? '👤' : '🔄';
            const getTabDescription = (view) => {
                return view === 'side' 
                    ? 'Stride length, vertical oscillation, forward lean analysis'
                    : 'Hip drop, knee alignment, lateral movement analysis';
            };

            return (
                <div className="tabs-container" style={{ marginBottom: '30px' }}>
                    <div className="tabs-nav" style={{ 
                        display: 'flex', 
                        gap: '0', 
                        background: '#f8f9fa', 
                        borderRadius: '12px', 
                        padding: '4px',
                        border: '1px solid #e5e5e5'
                    }}>
                        {availableViews.map((view) => (
                            <button
                                key={view}
                                className={`tab-button ${activeTab === view ? 'active' : ''}`}
                                onClick={() => onTabChange(view)}
                                style={{
                                    flex: 1,
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '12px',
                                    padding: '16px 20px',
                                    border: 'none',
                                    background: activeTab === view ? '#007bff' : 'transparent',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    color: activeTab === view ? 'white' : 'inherit',
                                    transition: 'all 0.3s ease'
                                }}
                            >
                                <span style={{ fontSize: '1.5rem' }}>{getTabIcon(view)}</span>
                                <div style={{ display: 'flex', flexDirection: 'column', textAlign: 'left' }}>
                                    <span style={{ fontWeight: 600, fontSize: '1.1rem' }}>
                                        {getTabLabel(view)}
                                    </span>
                                    <span style={{ fontSize: '0.9rem', opacity: 0.8 }}>
                                        {getTabDescription(view)}
                                    </span>
                                </div>
                            </button>
                        ))}
                    </div>
                </div>
            );
        };

        // Mock MetricsDisplay component (inline version of our TSX)
        const MetricsDisplay = ({ analysisData, viewType }) => {
            // Mock metrics data
            const mockMetrics = {
                side: [
                    { label: 'Stride Length', value: '45.2', unit: 'inches', status: 'good', icon: '📏' },
                    { label: 'Cadence', value: '175', unit: 'steps/min', status: 'good', icon: '⏱️' },
                    { label: 'Vertical Oscillation', value: '3.1', unit: 'inches', status: 'good', icon: '↕️' },
                    { label: 'Forward Lean', value: '5.2', unit: 'degrees', status: 'warning', icon: '📐' },
                    { label: 'Ground Contact Time', value: '220', unit: 'ms', status: 'good', icon: '👟' },
                    { label: 'Flight Time', value: '120', unit: 'ms', status: 'neutral', icon: '🦅' }
                ],
                rear: [
                    { label: 'Hip Drop', value: '4.1', unit: 'degrees', status: 'good', icon: '🏋️' },
                    { label: 'Knee Alignment', value: '2.3', unit: 'degrees', status: 'good', icon: '🦵' },
                    { label: 'Lateral Movement', value: '1.8', unit: 'inches', status: 'good', icon: '↔️' },
                    { label: 'Pelvic Rotation', value: '6.5', unit: 'degrees', status: 'neutral', icon: '🔄' },
                    { label: 'Foot Strike Symmetry', value: '92', unit: '%', status: 'good', icon: '⚖️' },
                    { label: 'Hip Stability', value: '8.2', unit: 'score', status: 'good', icon: '🎯' }
                ]
            };

            const viewMetrics = mockMetrics[viewType] || mockMetrics.side;

            const getStatusIcon = (status) => {
                switch (status) {
                    case 'good': return '✅';
                    case 'warning': return '⚠️';
                    case 'needs-attention': return '🔴';
                    default: return 'ℹ️';
                }
            };

            const getStatusClass = (status) => `metric-card ${status}`;

            return (
                <div className="metrics-display">
                    <div className="metrics-header" style={{ marginBottom: '25px', textAlign: 'center' }}>
                        <h3 style={{ color: '#333', fontSize: '1.4rem', fontWeight: 700, margin: '0 0 8px 0' }}>
                            {viewType === 'side' ? 'Side View Metrics' : 'Rear View Metrics'}
                        </h3>
                        <p style={{ color: '#666', fontSize: '0.95rem', margin: 0 }}>
                            {viewType === 'side' 
                                ? 'Forward motion and efficiency analysis'
                                : 'Stability and alignment analysis'
                            }
                        </p>
                    </div>

                    <div className="metrics-grid" style={{ 
                        display: 'grid', 
                        gridTemplateColumns: 'repeat(auto-fit, minmax(160px, 1fr))', 
                        gap: '15px',
                        marginBottom: '25px'
                    }}>
                        {viewMetrics.map((metric, index) => (
                            <div key={index} className={getStatusClass(metric.status)} style={{
                                background: '#f8f9fa',
                                borderRadius: '12px',
                                padding: '16px',
                                textAlign: 'center',
                                border: '2px solid',
                                borderColor: metric.status === 'good' ? '#28a745' : 
                                            metric.status === 'warning' ? '#ffc107' : '#e5e5e5'
                            }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
                                    <span style={{ fontSize: '1.3rem' }}>{metric.icon}</span>
                                    <span style={{ fontSize: '1rem' }}>{getStatusIcon(metric.status)}</span>
                                </div>
                                <div style={{ marginBottom: '8px' }}>
                                    <span style={{ display: 'block', fontSize: '1.8rem', fontWeight: 700, color: '#333', lineHeight: 1 }}>
                                        {metric.value}
                                    </span>
                                    <span style={{ fontSize: '0.85rem', color: '#666', fontWeight: 500 }}>
                                        {metric.unit}
                                    </span>
                                </div>
                                <div style={{ fontWeight: 600, color: '#333', fontSize: '0.95rem', marginBottom: '6px' }}>
                                    {metric.label}
                                </div>
                            </div>
                        ))}
                    </div>

                    <div className="metrics-summary" style={{ 
                        marginTop: '20px', 
                        paddingTop: '20px', 
                        borderTop: '1px solid #e5e5e5' 
                    }}>
                        <div className="summary-header">
                            <h4 style={{ color: '#333', fontSize: '1.2rem', fontWeight: 600, margin: '0 0 12px 0' }}>
                                Analysis Summary
                            </h4>
                        </div>
                        <div className="summary-content" style={{
                            fontSize: '0.95rem',
                            lineHeight: 1.5,
                            color: '#666',
                            padding: '15px',
                            background: '#f8f9fa',
                            borderRadius: '8px',
                            borderLeft: '4px solid #007bff'
                        }}>
                            {viewType === 'side' 
                                ? 'Excellent running form! 5/6 metrics in optimal range. Your stride efficiency and forward motion mechanics are well-balanced.'
                                : 'Outstanding stability and alignment! Your hip and knee mechanics show excellent control and symmetry.'
                            }
                        </div>
                    </div>
                </div>
            );
        };

        // Main ResultsPage component (inline version of our TSX)
        const ResultsPage = ({ analysisId, userEmail, onBack, onNewAnalysis }) => {
            const [activeTab, setActiveTab] = useState('side');
            const [analysisData, setAnalysisData] = useState(null);
            const [processingStatus, setProcessingStatus] = useState('completed');

            // Mock analysis data
            useEffect(() => {
                console.log('🔄 ResultsPage loaded with:', { analysisId, userEmail });
                
                // Simulate loading analysis data
                setTimeout(() => {
                    setAnalysisData({
                        sideView: {
                            videoUrl: 'mock-side-video.mp4',
                            processed: true
                        },
                        rearView: {
                            videoUrl: 'mock-rear-video.mp4', 
                            processed: true
                        }
                    });
                }, 1000);
            }, [analysisId, userEmail]);

            const availableViews = ['side', 'rear'];

            if (processingStatus === 'loading') {
                return (
                    <div style={{ 
                        minHeight: '100vh', 
                        display: 'flex', 
                        justifyContent: 'center', 
                        alignItems: 'center' 
                    }}>
                        <div style={{ textAlign: 'center', padding: '40px' }}>
                            <div style={{ 
                                width: '40px', 
                                height: '40px', 
                                border: '4px solid #f0f0f0', 
                                borderTop: '4px solid #007bff', 
                                borderRadius: '50%', 
                                animation: 'spin 1s linear infinite',
                                margin: '0 auto 20px auto'
                            }}></div>
                            <h2>Loading Analysis Results...</h2>
                        </div>
                    </div>
                );
            }

            return (
                <div className="results-page" style={{ 
                    maxWidth: '1400px', 
                    margin: '0 auto', 
                    padding: '20px',
                    background: '#ffffff',
                    minHeight: '100vh'
                }}>
                    {/* Header */}
                    <div className="results-header" style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: '30px',
                        paddingBottom: '20px',
                        borderBottom: '2px solid #e5e5e5'
                    }}>
                        <h1 style={{ color: '#333', fontSize: '2.5rem', fontWeight: 700, margin: 0 }}>
                            🏃‍♂️ Running Analysis Results
                        </h1>
                        <div className="header-actions" style={{ display: 'flex', gap: '15px' }}>
                            <button 
                                onClick={onBack}
                                style={{
                                    padding: '12px 24px',
                                    border: '1px solid #e5e5e5',
                                    borderRadius: '8px',
                                    background: '#f8f9fa',
                                    color: '#333',
                                    cursor: 'pointer'
                                }}
                            >
                                ← Back
                            </button>
                            <button 
                                onClick={onNewAnalysis}
                                style={{
                                    padding: '12px 24px',
                                    border: 'none',
                                    borderRadius: '8px',
                                    background: '#007bff',
                                    color: 'white',
                                    cursor: 'pointer'
                                }}
                            >
                                🔄 New Analysis
                            </button>
                        </div>
                    </div>

                    {/* Tabs */}
                    <TabsContainer
                        activeTab={activeTab}
                        availableViews={availableViews}
                        onTabChange={setActiveTab}
                    />

                    {/* Content */}
                    <div className="results-content" style={{ 
                        display: 'grid', 
                        gridTemplateColumns: '1fr 400px', 
                        gap: '30px',
                        alignItems: 'start'
                    }}>
                        {/* Video Section */}
                        <div className="video-section" style={{
                            background: 'white',
                            borderRadius: '12px',
                            padding: '20px',
                            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e5e5e5'
                        }}>
                            <h3 style={{ marginBottom: '1rem' }}>
                                {activeTab === 'side' ? 'Side View Analysis' : 'Rear View Analysis'}
                            </h3>
                            <ProcessedVideoPlayer
                                videoUrl={analysisData?.[`${activeTab}View`]?.videoUrl}
                                onVideoLoad={() => console.log('Video loaded')}
                            />
                        </div>

                        {/* Metrics Section */}
                        <div className="metrics-section" style={{
                            background: 'white',
                            borderRadius: '12px',
                            padding: '20px',
                            boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #e5e5e5',
                            position: 'sticky',
                            top: '20px'
                        }}>
                            <MetricsDisplay
                                analysisData={analysisData}
                                viewType={activeTab}
                            />
                        </div>
                    </div>
                </div>
            );
        };

        // Test App
        const TestApp = () => {
            const [showResults, setShowResults] = useState(true);

            if (!showResults) {
                return (
                    <div style={{ padding: '2rem', textAlign: 'center' }}>
                        <h1>Test Results Page</h1>
                        <button 
                            onClick={() => setShowResults(true)}
                            style={{
                                padding: '1rem 2rem',
                                background: '#007bff',
                                color: 'white',
                                border: 'none',
                                borderRadius: '8px',
                                cursor: 'pointer'
                            }}
                        >
                            Show Results Page
                        </button>
                    </div>
                );
            }

            return (
                <ResultsPage
                    analysisId="test-analysis-123"
                    userEmail="<EMAIL>"
                    onBack={() => setShowResults(false)}
                    onNewAnalysis={() => {
                        console.log('New analysis requested');
                        setShowResults(false);
                    }}
                />
            );
        };

        // Render the test app
        ReactDOM.render(<TestApp />, document.getElementById('root'));
        
        console.log('🧪 Test Results Page loaded successfully!');
    </script>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</body>
</html>