import json

# Load both the input data and Modal results to compare coordinate transformation
with open('Michael_test_2_pose.json', 'r') as f:
    modal_data = json.load(f)

print("=== COORDINATE TRANSFORMATION DEBUG ===\n")

# Check video dimensions from Modal output
video_width = modal_data['videoWidth']  # Should be 1080
video_height = modal_data['videoHeight']  # Should be 1920
print(f"Video dimensions from Modal: {video_width}x{video_height}")

# Expected center coordinates for 1080x1920 video
expected_center_x = video_width / 2  # 540
expected_center_y = video_height / 2  # 960
print(f"Expected center coordinates: ({expected_center_x}, {expected_center_y})")

# Analyze first few frames with valid keypoints
print("\n=== ACTUAL KEYPOINT COORDINATES ===")
for frame_idx in range(min(5, len(modal_data['frames']))):
    frame = modal_data['frames'][frame_idx]
    
    # Find keypoints that aren't at (0,0)
    valid_keypoints = [kp for kp in frame['keypoints'] if kp['x'] != 0.0 or kp['y'] != 0.0]
    
    if valid_keypoints:
        # Calculate actual center from valid keypoints
        avg_x = sum(kp['x'] for kp in valid_keypoints) / len(valid_keypoints)
        avg_y = sum(kp['y'] for kp in valid_keypoints) / len(valid_keypoints)
        
        # Calculate scaling factors
        scale_x = avg_x / expected_center_x if expected_center_x != 0 else 0
        scale_y = avg_y / expected_center_y if expected_center_y != 0 else 0
        
        print(f"Frame {frame_idx}:")
        print(f"  Valid keypoints: {len(valid_keypoints)}/{len(frame['keypoints'])}")
        print(f"  Actual center: ({avg_x:.1f}, {avg_y:.1f})")
        print(f"  Scale factors: x={scale_x:.4f}, y={scale_y:.4f}")
        
        # Show some specific keypoints for debugging
        sample_kps = valid_keypoints[:3]
        for kp in sample_kps:
            print(f"    {kp['name']}: ({kp['x']:.1f}, {kp['y']:.1f}) score={kp['score']:.3f}")
    else:
        print(f"Frame {frame_idx}: All keypoints invalid (0,0)")

print("\n=== COORDINATE RANGE ANALYSIS ===")
all_x = []
all_y = []
for frame in modal_data['frames'][:10]:  # First 10 frames
    for kp in frame['keypoints']:
        if kp['x'] != 0.0 or kp['y'] != 0.0:  # Skip invalid coordinates
            all_x.append(kp['x'])
            all_y.append(kp['y'])

if all_x and all_y:
    print(f"X coordinate range: {min(all_x):.1f} to {max(all_x):.1f}")
    print(f"Y coordinate range: {min(all_y):.1f} to {max(all_y):.1f}")
    print(f"Max X as fraction of video width: {max(all_x)/video_width:.4f}")
    print(f"Max Y as fraction of video height: {max(all_y)/video_height:.4f}")
    
    # Check if coordinates look like they're still normalized (0-1 range)
    if max(all_x) <= 1.0 and max(all_y) <= 1.0:
        print("⚠️  WARNING: Coordinates appear to be normalized (0-1) instead of pixels!")
        print("   This suggests SmoothNet output wasn't properly converted back to pixels.")
else:
    print("No valid coordinates found to analyze!")