/**
 * Custom React hook for BlazePose integration
 * Provides video frame analysis with 39 keypoint detection
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import * as poseDetection from '../../index';
import { PoseData, ProcessingStatus, UserHeight } from '../types';

interface UseBlazePoseOptions {
  userHeight: UserHeight;
  onPoseData?: (data: PoseData) => void;
  onStatusChange?: (status: ProcessingStatus) => void;
}

interface UseBlazePoseReturn {
  detector: poseDetection.PoseDetector | null;
  isLoading: boolean;
  isReady: boolean;
  error: string | null;
  processFrame: (videoElement: HTMLVideoElement) => Promise<PoseData | null>;
  dispose: () => void;
  reset: () => void;
}

export const useBlazePose = (options: UseBlazePoseOptions): UseBlazePoseReturn => {
  const { userHeight, onPoseData, onStatusChange } = options;
  
  const [detector, setDetector] = useState<poseDetection.PoseDetector | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const detectorRef = useRef<poseDetection.PoseDetector | null>(null);
  const frameCountRef = useRef(0);

  // Initialize BlazePose detector
  useEffect(() => {
    let mounted = true;

    const initializeDetector = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        onStatusChange?.({
          isProcessing: true,
          progress: 0,
          currentFrame: 0,
          totalFrames: 0,
          elapsed: 0,
          remaining: 0,
          status: 'loading',
          message: 'Loading BlazePose Full model...'
        });

        console.log('🚀 Initializing BlazePose TFJS detector...');
        
        const modelConfig: poseDetection.BlazePoseTfjsModelConfig = {
          runtime: 'tfjs',
          modelType: 'full', // Use Full model for 39 keypoints
          enableSmoothing: true
        };

        const detectorInstance = await poseDetection.createDetector(
          poseDetection.SupportedModels.BlazePose,
          modelConfig
        );

        if (!mounted) {
          detectorInstance.dispose();
          return;
        }

        detectorRef.current = detectorInstance;
        setDetector(detectorInstance);
        setIsReady(true);
        setIsLoading(false);

        console.log('✅ BlazePose detector initialized successfully');
        
        onStatusChange?.({
          isProcessing: false,
          progress: 0,
          currentFrame: 0,
          totalFrames: 0,
          elapsed: 0,
          remaining: 0,
          status: 'idle',
          message: 'BlazePose detector ready'
        });

      } catch (err) {
        console.error('❌ Failed to initialize BlazePose detector:', err);
        
        if (!mounted) return;
        
        const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
        setError(errorMessage);
        setIsLoading(false);
        setIsReady(false);
        
        onStatusChange?.({
          isProcessing: false,
          progress: 0,
          currentFrame: 0,
          totalFrames: 0,
          elapsed: 0,
          remaining: 0,
          status: 'error',
          message: `Failed to load BlazePose: ${errorMessage}`
        });
      }
    };

    initializeDetector();

    return () => {
      mounted = false;
      if (detectorRef.current) {
        detectorRef.current.dispose();
        detectorRef.current = null;
      }
    };
  }, []); // Empty dependency array - initialize once

  // Process single video frame
  const processFrame = useCallback(async (videoElement: HTMLVideoElement): Promise<PoseData | null> => {
    if (!detector || !isReady) {
      console.warn('⚠️ BlazePose detector not ready');
      return null;
    }

    try {
      frameCountRef.current += 1;
      
      const estimationConfig: poseDetection.BlazePoseTfjsEstimationConfig = {
        maxPoses: 1, // Single person detection
        flipHorizontal: false // Side view doesn't need flipping
      };

      const poses = await detector.estimatePoses(videoElement, estimationConfig);
      
      if (poses.length === 0) {
        console.log(`⚠️ No poses detected in frame ${frameCountRef.current}`);
        return null;
      }

      const pose = poses[0];
      const timestamp = videoElement.currentTime * 1000; // Convert to milliseconds

      // Validate keypoint count for BlazePose Full (39 keypoints expected)
      if (pose.keypoints.length !== 39) {
        console.warn(`⚠️ Expected 39 keypoints, got ${pose.keypoints.length}`);
      }

      const poseData: PoseData = {
        keypoints: pose.keypoints.map((kp, index) => ({
          x: kp.x,
          y: kp.y,
          z: kp.z,
          score: kp.score,
          name: kp.name
        })),
        keypoints3D: pose.keypoints3D?.map((kp, index) => ({
          x: kp.x,
          y: kp.y,
          z: kp.z,
          score: kp.score,
          name: kp.name
        })),
        score: pose.score,
        timestamp
      };

      // Log progress every 30 frames
      if (frameCountRef.current % 30 === 0) {
        console.log(`📊 Processed ${frameCountRef.current} frames, current score: ${pose.score?.toFixed(3)}`);
      }

      onPoseData?.(poseData);
      return poseData;

    } catch (err) {
      console.error('❌ Error processing frame:', err);
      setError(err instanceof Error ? err.message : 'Frame processing error');
      return null;
    }
  }, [detector, isReady, onPoseData]);

  // Reset detector state
  const reset = useCallback(() => {
    if (detector) {
      detector.reset();
      frameCountRef.current = 0;
      console.log('🔄 BlazePose detector reset');
    }
  }, [detector]);

  // Dispose detector and cleanup
  const dispose = useCallback(() => {
    if (detectorRef.current) {
      detectorRef.current.dispose();
      detectorRef.current = null;
      setDetector(null);
      setIsReady(false);
      frameCountRef.current = 0;
      console.log('🗑️ BlazePose detector disposed');
    }
  }, []);

  return {
    detector,
    isLoading,
    isReady,
    error,
    processFrame,
    dispose,
    reset
  };
};