<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxWattz - AI Running Analysis</title>
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/form-updates.css">
    
    <!-- TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-core@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-converter@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-webgl@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/pose-detection@2.1.0"></script>
</head>
<body>
    <!-- Sidebar Navigation -->
    <nav class="sidebar">
        <button class="sidebar-toggle" id="sidebarToggle" aria-label="Toggle sidebar">
            <svg class="toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
        </button>
        <div class="sidebar-header">
            <div class="logo-container">
                <img src="../../Logos/MaxWattzLogoSm-White.png" alt="MaxWattz" class="logo-image">
                <h2 class="sidebar-title">MaxWattz</h2>
            </div>
        </div>
        
        <ul class="nav-items">
            <li class="nav-item active" data-page="insights" data-tooltip="Max's Insights">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 11L12 14L22 4"></path>
                    <path d="M21 12V19C21 20.1 20.1 21 19 21H5C3.9 21 3 20.1 3 19V5C3 3.9 3.9 3 5 3H16"></path>
                </svg>
                <span>Max's Insights</span>
            </li>
            <li class="nav-item" data-page="dashboard" data-tooltip="Dashboard">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                <span>Dashboard</span>
            </li>
            <li class="nav-item" data-page="biometrics" data-tooltip="BioMetrics">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M22 12H18L15 21L9 3L6 12H2"></path>
                </svg>
                <span>BioMetrics</span>
            </li>
            <li class="nav-item" data-page="calendar" data-tooltip="Calendar">
                <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                <span>Calendar</span>
            </li>
        </ul>
        
        <div class="sidebar-footer">
            <div class="theme-toggle">
                <button class="theme-button" id="themeToggle" aria-label="Toggle theme">
                    <svg class="theme-icon sun-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                    <svg class="theme-icon moon-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z"></path>
                    </svg>
                    <span class="theme-label">Light</span>
                </button>
            </div>
            <div class="profile-button">
                <div class="avatar-placeholder">U</div>
                <span class="profile-text">Profile</span>
                <svg class="chevron" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <polyline points="6 9 12 15 18 9"></polyline>
                </svg>
            </div>
        </div>
    </nav>
    
    <!-- Main Content Area -->
    <main class="main-content">
        <!-- Header -->
        <header class="main-header">
            <div class="header-content">
                <h1>Max's Insights</h1>
                <p class="subtitle">AI-Powered Running Analysis</p>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary">View History</button>
                <button class="btn btn-primary" id="newAnalysisBtn">New Analysis</button>
            </div>
        </header>
        
        <!-- Page Container -->
        <div class="page-container">
            <!-- Upload Page -->
            <div id="uploadPage" class="page active">
                <!-- Content will be dynamically loaded -->
            </div>
            
            <!-- Side View Analysis Page -->
            <div id="sideAnalysisPage" class="page" hidden>
                <!-- Content will be dynamically loaded -->
            </div>
            
            <!-- Rear View Analysis Page -->
            <div id="rearAnalysisPage" class="page" hidden>
                <!-- Content will be dynamically loaded -->
            </div>
        </div>
    </main>
    
    <!-- Scripts -->
    <script type="module" src="js/app.js"></script>
    <script src="js/navigation.js"></script>
    <script src="js/upload.js"></script>
    <script src="js/analysis-common.js"></script>
    <script src="js/side-view.js"></script>
    <script src="js/rear-view.js"></script>
</body>
</html>