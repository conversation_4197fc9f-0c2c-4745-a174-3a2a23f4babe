# BlazePose Running Analysis App

**A complete React-based application for running biomechanics analysis using BlazePose TFJS with 39 keypoint detection.**

---

## 🏗️ Architecture Overview

This analysis app is built on top of the customized BlazePose TFJS foundation to provide real-time running analysis with the following components:

### **Core Components**

#### **1. AnalysisApp.tsx** - Main Application
- State management for upload → processing → results workflow
- Integration of all sub-components
- Real-time metrics calculation and display
- View switching between side/rear camera angles

#### **2. VideoPlayer.tsx** - Video Analysis Engine
- HTML5 video player with canvas overlay
- Real-time BlazePose pose detection
- 33 keypoint visualization with confidence scoring
- Frame-by-frame processing with performance monitoring

#### **3. UploadPanel.tsx** - File Management
- Drag-and-drop video upload
- File validation (format, size)
- Video preview with metadata display
- Support for MP4, MOV, AVI, WebM formats

#### **4. ConfigurationPanel.tsx** - User Settings
- Height input (feet/inches with metric conversion)
- Optional gender and weight collection
- Real-time height scaling calculation display

### **Core Utilities**

#### **5. useBlazePose.ts** - React Hook
- BlazePose detector initialization and lifecycle management
- Frame processing with tensor memory management
- Error handling and status reporting
- Real-time pose data extraction

#### **6. coordinates.ts** - Coordinate System
- User height-based coordinate scaling
- Pixel-to-meter coordinate transformation
- Distance and angle calculations between keypoints
- Essential keypoint validation for running analysis

#### **7. runningMetrics.ts** - Biomechanics Engine
- Real-time running metrics calculation
- Stride length, cadence, and ground contact time analysis
- Foot strike pattern detection (heel/midfoot/forefoot)
- Posture and knee flexion assessment
- Scoring system with actionable feedback

---

## 🎯 Key Features

### **Real-time Analysis**
- **39 Keypoint Detection**: Full BlazePose model with enhanced wrist tracking
- **Live Video Overlay**: Real-time pose visualization on video playback
- **Performance Monitoring**: Frame rate and confidence tracking
- **Memory Management**: Automatic tensor disposal for sustained performance

### **Running-Specific Metrics**
- **Stride Analysis**: Length, cadence, and ground contact time
- **Form Assessment**: Posture alignment and knee drive evaluation
- **Foot Strike Detection**: Heel, midfoot, or forefoot landing patterns
- **Biomechanical Scoring**: 0-100 scale with improvement recommendations

### **User-Centric Design**
- **Height Calibration**: Real-world coordinate scaling based on user height
- **Multi-View Support**: Side view (primary) and rear view analysis
- **Progressive Workflow**: Upload → Configure → Process → Analyze
- **Export Ready**: Structured data format for Supabase integration

### **Technical Excellence**
- **WebGL Acceleration**: Optimized for real-time video processing
- **TypeScript Safety**: Full type coverage for reliable development
- **React Integration**: Modern hooks-based architecture
- **Error Resilience**: Comprehensive error handling and recovery

---

## 📁 Project Structure

```
src/analysis-app/
├── components/                 # React Components
│   ├── AnalysisApp.tsx        # Main application orchestrator
│   ├── VideoPlayer.tsx        # Video analysis with pose overlay
│   ├── UploadPanel.tsx        # File upload and management
│   └── ConfigurationPanel.tsx # User configuration inputs
├── hooks/                      # Custom React Hooks
│   └── useBlazePose.ts        # BlazePose integration hook
├── utils/                      # Core Utilities
│   ├── coordinates.ts         # Coordinate transformations
│   └── runningMetrics.ts      # Biomechanics calculations
├── types/                      # TypeScript Definitions
│   └── index.ts               # All app-specific types
├── test-app.html              # Standalone HTML test file
├── index.ts                   # Main exports
└── README.md                  # This documentation
```

---

## 🚀 Usage Examples

### **Basic Integration**

```typescript
import { AnalysisApp } from './analysis-app';

// Use the complete application
function App() {
  return <AnalysisApp />;
}
```

### **Individual Component Usage**

```typescript
import { VideoPlayer, useBlazePose } from './analysis-app';

function CustomAnalysis() {
  const { detector, isReady, processFrame } = useBlazePose({
    userHeight: { feet: 5, inches: 10 },
    onPoseData: (data) => console.log('Pose detected:', data)
  });

  return (
    <VideoPlayer
      videoUrl="/path/to/video.mp4"
      analysisType="running"
      viewType="side"
      analysisMode="3D"
      videoSetup="Treadmill"
      overlayStyle="Medical"
      userHeight={{ feet: 5, inches: 10 }}
      onPoseData={(data) => console.log('Real-time pose:', data)}
    />
  );
}
```

### **Metrics Calculation**

```typescript
import { RunningMetricsCalculator } from './analysis-app';

const calculator = new RunningMetricsCalculator({ feet: 6, inches: 0 });

// Process video frames
poseFrames.forEach((poseData, index) => {
  const metrics = calculator.addFrame(poseData, 1920, 1080);
  if (metrics) {
    console.log('Running metrics:', {
      strideLength: metrics.strideLength.value,
      cadence: metrics.cadence.value,
      footStrike: metrics.footStrike.type
    });
  }
});
```

---

## 📊 Output Data Structure

### **Pose Data**
```typescript
interface PoseData {
  keypoints: PoseKeypoint[];      // 39 keypoints with x,y,z,score
  keypoints3D?: PoseKeypoint[];   // 3D world coordinates
  score?: number;                 // Overall pose confidence
  timestamp: number;              // Video timestamp (ms)
}
```

### **Running Metrics**
```typescript
interface RunningMetrics {
  strideLength: MetricResult;     // Meters, 0-100 score
  cadence: MetricResult;          // Steps/min, 0-100 score
  footStrike: FootStrikeResult;   // Pattern + score
  posture: PostureResult;         // Alignment + score
  kneeFlexion: FlexionResult;     // Peak angle + score
  groundContactTime: TimeResult;  // Milliseconds + score
}
```

---

## 🧪 Testing

### **HTML Test File**
Use the included `test-app.html` for standalone testing:

```bash
# Open in browser for isolated testing
open src/analysis-app/test-app.html
```

### **Integration Test**
```typescript
import { testBlazePoseUserJourney } from '../blazepose_test';

// Run end-to-end pipeline test
testBlazePoseUserJourney().then(success => {
  console.log('Pipeline test:', success ? 'PASSED' : 'FAILED');
});
```

---

## ⚡ Performance Considerations

### **Real-time Requirements**
- **Target FPS**: 30 FPS for smooth analysis
- **Memory Management**: Automatic tensor disposal
- **WebGL Acceleration**: Required for real-time performance
- **Frame Skipping**: Adaptive processing for performance

### **Optimization Tips**
- Use Chrome/Edge for best WebGL support
- Ensure adequate GPU memory (2GB+ recommended)
- Process videos at 1080p or lower for real-time analysis
- Monitor memory usage during extended sessions

---

## 🔧 Configuration Options

### **Video Requirements**
- **Formats**: MP4, MOV, AVI, WebM
- **Max Size**: 100MB per file
- **Resolution**: 1080×1920 HD or 2160×3840 4K
- **Frame Rate**: 30 FPS recommended
- **Orientation**: Landscape preferred

### **Camera Setup**
- **Distance**: 5 feet from runner
- **Height**: Camera level with runner's abdomen
- **Angle**: Perpendicular to running direction (side view)
- **Lighting**: Even lighting, avoid shadows
- **Duration**: 30+ seconds for multiple stride cycles

### **Analysis Parameters**
- **Confidence Threshold**: 0.5 (configurable)
- **Smoothing**: Enabled by default
- **Coordinate Scaling**: Based on user height
- **Metrics Window**: 3-second rolling average

---

## 🚨 Error Handling

### **Common Issues**
1. **Model Loading Failure**: Check internet connection for TFJS model download
2. **Memory Errors**: Refresh browser to clear WebGL memory
3. **Performance Issues**: Reduce video resolution or close other tabs
4. **Pose Detection Failure**: Ensure runner is clearly visible and well-lit

### **Recovery Strategies**
- Automatic tensor cleanup on errors
- Graceful degradation for low-confidence poses
- User feedback for remediation steps
- Diagnostic logging for debugging

---

## 🔄 Integration with Existing Code

### **Adapting Reference Components**
The analysis app can integrate with the existing reference components:

```typescript
// Adapt Index-OLD.tsx patterns
import { AnalysisApp } from './analysis-app';

// Replace ProcessingPanel.tsx with built-in processing
// Replace ResultsPanel.tsx with built-in results display
// Maintain same user height configuration patterns
```

### **Data Export**
Ready for Supabase integration:

```typescript
// Export analysis results
const exportData = {
  userConfig,
  analysisConfig,
  metrics: currentMetrics,
  poseData: frameHistory,
  timestamp: Date.now()
};

// Send to Supabase or other storage
await supabase.from('analyses').insert(exportData);
```

---

## 📈 Future Enhancements

### **Planned Features**
- **Multi-Person Detection**: Support for multiple runners
- **Advanced Metrics**: Ground reaction forces, energy efficiency
- **Video Export**: Analysis overlay burned into video
- **Comparison Mode**: Side-by-side analysis comparison
- **Mobile Optimization**: Touch-friendly interface
- **Offline Mode**: Local model caching for offline use

### **Performance Improvements**
- **WebAssembly**: Faster tensor operations
- **Web Workers**: Background processing
- **Progressive Loading**: Partial model loading
- **Edge Computing**: Server-side processing option

---

## 🏃‍♂️ Ready for Production

This analysis app represents a complete, production-ready implementation of BlazePose-based running analysis. It's designed to be:

- **Stable**: Built on verified BlazePose TFJS foundation
- **Scalable**: Modular architecture for easy extension
- **User-Friendly**: Intuitive workflow with clear feedback
- **Developer-Friendly**: Full TypeScript coverage and documentation

The app is ready for immediate deployment and integration into larger applications or can serve as a standalone running analysis tool.

---

*Built with BlazePose TFJS • 39 Keypoint Detection • Real-time Analysis*
