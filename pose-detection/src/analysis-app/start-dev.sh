#!/bin/bash

# BlazePose Analysis App - Development Environment Starter
# This script starts a local development server for testing the analysis app

echo "🏃‍♂️ BlazePose Analysis App - Development Environment"
echo "=================================================="
echo ""

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    echo "   Please install Python 3 and try again."
    exit 1
fi

# Navigate to the analysis app directory
cd "$(dirname "$0")"

echo "📍 Starting development server..."
echo "   Directory: $(pwd)"
echo ""

# Start the Python HTTP server
python3 serve.py