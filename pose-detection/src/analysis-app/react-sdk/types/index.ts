/**
 * Type Definitions Export
 * Central export point for all TypeScript types in the SDK
 */

// Export all pose-related types
export * from './pose';

// Export all analysis-related types
export * from './analysis';

// Export all metrics-related types
export * from './metrics';

// Export all Supabase database types
export * from './supabase';

// Export all component prop types
export * from './components';

// =============================================================================
// RE-EXPORTS FOR CONVENIENCE
// =============================================================================

// Re-export common types for easier imports
// These are already exported above through * exports