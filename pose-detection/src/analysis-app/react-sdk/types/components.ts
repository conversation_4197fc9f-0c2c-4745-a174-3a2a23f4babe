/**
 * Component Props Type Definitions
 * Props interfaces for all React SDK components
 */

import { ReactNode } from 'react';
import { 
  AnalysisData, 
  ProcessedVideo, 
  UserConfiguration,
  ViewType,
  ProcessingStatus
} from './analysis';
import { 
  RunningMetrics,
  MetricCard,
  MetricGroup,
  MetricStatus
} from './metrics';
import { ProcessedPoseData, BlazePoseModelType } from './pose';

// =============================================================================
// RESULTS PAGE COMPONENTS
// =============================================================================

/**
 * Main results page props
 */
export interface ResultsPageProps {
  analysisId: string;
  userEmail: string;
  onBack?: () => void;
  onNewAnalysis?: () => void;
  onExport?: (format: 'pdf' | 'csv' | 'json') => void;
}

/**
 * Tabs container props
 */
export interface TabsContainerProps {
  activeTab: ViewType;
  availableViews: ViewType[];
  onTabChange: (tab: ViewType) => void;
  disabled?: boolean;
}

/**
 * Metrics display props
 */
export interface MetricsDisplayProps {
  metrics: Partial<RunningMetrics>;
  viewType: ViewType;
  showDetails?: boolean;
  onMetricClick?: (metricId: string) => void;
}

// =============================================================================
// VIDEO PLAYER COMPONENTS
// =============================================================================

/**
 * Processed video player props
 */
export interface ProcessedVideoPlayerProps {
  videoUrl: string;
  resultsUrl: string;
  analysisType: 'running';
  viewType: ViewType;
  overlayStyle?: 'medical' | 'athletic' | 'minimal' | 'debug';
  userHeight?: { feet: number; inches: number };
  onPoseData?: (data: ProcessedPoseData) => void;
  onMetrics?: (metrics: Partial<RunningMetrics>) => void;
  autoPlay?: boolean;
  controls?: boolean;
  loop?: boolean;
}

/**
 * Video controls props
 */
export interface VideoControlsProps {
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  onPlayPause: () => void;
  onSeek: (time: number) => void;
  onSpeedChange?: (speed: number) => void;
  showSpeed?: boolean;
}

/**
 * Pose overlay props
 */
export interface PoseOverlayProps {
  pose: ProcessedPoseData;
  width: number;
  height: number;
  overlayStyle: 'medical' | 'athletic' | 'minimal' | 'debug';
  showKeypoints?: boolean;
  showSkeleton?: boolean;
  showLabels?: boolean;
}

// =============================================================================
// METRIC COMPONENTS
// =============================================================================

/**
 * Metric card props
 */
export interface MetricCardProps {
  metric: MetricCard;
  showDetails?: boolean;
  onClick?: () => void;
  className?: string;
}

/**
 * Metric group props
 */
export interface MetricGroupProps {
  group: MetricGroup;
  expanded?: boolean;
  onToggle?: () => void;
}

/**
 * Metric chart props
 */
export interface MetricChartProps {
  data: Array<{ timestamp: number; value: number }>;
  title: string;
  unit: string;
  referenceRange?: { min: number; max: number; optimal: number };
  height?: number;
}

/**
 * Metric status indicator props
 */
export interface MetricStatusProps {
  status: MetricStatus;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

// =============================================================================
// ANALYSIS COMPONENTS
// =============================================================================

/**
 * Analysis summary props
 */
export interface AnalysisSummaryProps {
  analysis: AnalysisData;
  showMetrics?: boolean;
  showInsights?: boolean;
  showRecommendations?: boolean;
}

/**
 * Analysis insight card props
 */
export interface InsightCardProps {
  insight: {
    id: string;
    title: string;
    description: string;
    severity: 'info' | 'warning' | 'critical';
    category: string;
  };
  onDismiss?: () => void;
  onClick?: () => void;
}

/**
 * Recommendation card props
 */
export interface RecommendationCardProps {
  recommendation: {
    id: string;
    title: string;
    description: string;
    priority: 'high' | 'medium' | 'low';
    category: string;
  };
  onMarkComplete?: () => void;
  onLearnMore?: () => void;
}

// =============================================================================
// LOADING & ERROR COMPONENTS
// =============================================================================

/**
 * Loading state props
 */
export interface LoadingStateProps {
  message?: string;
  progress?: number;
  status?: ProcessingStatus;
  showCancel?: boolean;
  onCancel?: () => void;
}

/**
 * Error state props
 */
export interface ErrorStateProps {
  error: Error | string;
  title?: string;
  showRetry?: boolean;
  onRetry?: () => void;
  showSupport?: boolean;
}

/**
 * Empty state props
 */
export interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

// =============================================================================
// UTILITY COMPONENTS
// =============================================================================

/**
 * Progress bar props
 */
export interface ProgressBarProps {
  value: number;
  max?: number;
  label?: string;
  showPercentage?: boolean;
  color?: 'primary' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Badge props
 */
export interface BadgeProps {
  children: ReactNode;
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

/**
 * Tooltip props
 */
export interface TooltipProps {
  content: ReactNode;
  children: ReactNode;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
}

// =============================================================================
// LAYOUT COMPONENTS
// =============================================================================

/**
 * Page header props
 */
export interface PageHeaderProps {
  title: string;
  subtitle?: string;
  actions?: Array<{
    label: string;
    onClick: () => void;
    variant?: 'primary' | 'secondary' | 'ghost';
    icon?: ReactNode;
  }>;
  breadcrumbs?: Array<{
    label: string;
    href?: string;
    onClick?: () => void;
  }>;
}

/**
 * Section props
 */
export interface SectionProps {
  title?: string;
  description?: string;
  children: ReactNode;
  actions?: ReactNode;
  className?: string;
}

/**
 * Card props
 */
export interface CardProps {
  title?: string;
  subtitle?: string;
  children: ReactNode;
  footer?: ReactNode;
  className?: string;
  onClick?: () => void;
}

// =============================================================================
// HOOK RETURN TYPES
// =============================================================================

/**
 * useBlazePose hook return type
 */
export interface UseBlazePoseReturn {
  isLoading: boolean;
  isReady: boolean;
  error: Error | null;
  detectPose: (video: HTMLVideoElement) => Promise<ProcessedPoseData | null>;
  detector: any; // PoseDetector instance
  reset: () => void;
  dispose: () => void;
  frameCount: number;
  modelInfo: {
    type: BlazePoseModelType;
    smoothing: boolean;
    segmentation: boolean;
    expectedKeypoints: number;
  };
}

/**
 * useAnalysisData hook return type
 */
export interface UseAnalysisDataReturn {
  analysis: AnalysisData | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  updateAnalysis: (updates: Partial<AnalysisData>) => Promise<void>;
}

/**
 * useSupabaseAnalysis hook return type
 */
export interface UseSupabaseAnalysisReturn {
  createAnalysis: (data: any) => Promise<string>;
  getAnalysis: (id: string) => Promise<any>;
  updateAnalysisStatus: (id: string, status: any, additionalData?: any) => Promise<void>;
  subscribeToUpdates: (id: string, callback: (data: any) => void) => () => void;
  getProcessingStatus: (analysisId: string) => Promise<any[]>;
  isLoading: boolean;
  error: Error | null;
}