/**
 * Pose Detection Type Definitions
 * BlazePose Full model (39 keypoints) type system
 */

// =============================================================================
// KEYPOINT TYPES
// =============================================================================

/**
 * Basic keypoint structure from TensorFlow.js
 */
export interface Keypoint {
  x: number;
  y: number;
  z?: number;
  score?: number;
  name?: string;
}

/**
 * Extended keypoint with additional metadata
 */
export interface ExtendedKeypoint extends Keypoint {
  visibility?: number;
  presence?: number;
  confidence?: number;
  frameNumber?: number;
  timestamp?: number;
}

/**
 * BlazePose keypoint names enum for type safety
 */
export enum BlazePoseKeypointName {
  // Face landmarks (0-10)
  NOSE = 'nose',
  LEFT_EYE_INNER = 'left_eye_inner',
  LEFT_EYE = 'left_eye',
  LEFT_EYE_OUTER = 'left_eye_outer',
  RIGHT_EYE_INNER = 'right_eye_inner',
  RIGHT_EYE = 'right_eye',
  RIGHT_EYE_OUTER = 'right_eye_outer',
  LEFT_EAR = 'left_ear',
  RIGHT_EAR = 'right_ear',
  MOUTH_LEFT = 'mouth_left',
  MOUTH_RIGHT = 'mouth_right',
  
  // Upper body landmarks (11-22)
  LEFT_SHOULDER = 'left_shoulder',
  RIGHT_SHOULDER = 'right_shoulder',
  LEFT_ELBOW = 'left_elbow',
  RIGHT_ELBOW = 'right_elbow',
  LEFT_WRIST = 'left_wrist',
  RIGHT_WRIST = 'right_wrist',
  LEFT_PINKY = 'left_pinky',
  RIGHT_PINKY = 'right_pinky',
  LEFT_INDEX = 'left_index',
  RIGHT_INDEX = 'right_index',
  LEFT_THUMB = 'left_thumb',
  RIGHT_THUMB = 'right_thumb',
  
  // Lower body landmarks (23-32)
  LEFT_HIP = 'left_hip',
  RIGHT_HIP = 'right_hip',
  LEFT_KNEE = 'left_knee',
  RIGHT_KNEE = 'right_knee',
  LEFT_ANKLE = 'left_ankle',
  RIGHT_ANKLE = 'right_ankle',
  LEFT_HEEL = 'left_heel',
  RIGHT_HEEL = 'right_heel',
  LEFT_FOOT_INDEX = 'left_foot_index',
  RIGHT_FOOT_INDEX = 'right_foot_index',
  
  // Additional Full model landmarks (33-38)
  LEFT_WRIST_PINKY = 'left_wrist_pinky',
  LEFT_WRIST_INDEX = 'left_wrist_index',
  LEFT_WRIST_THUMB = 'left_wrist_thumb',
  RIGHT_WRIST_PINKY = 'right_wrist_pinky',
  RIGHT_WRIST_INDEX = 'right_wrist_index',
  RIGHT_WRIST_THUMB = 'right_wrist_thumb'
}

/**
 * Keypoint indices for array access
 */
export const BLAZEPOSE_KEYPOINT_INDICES = {
  // Face landmarks (0-10)
  nose: 0,
  leftEyeInner: 1,
  leftEye: 2,
  leftEyeOuter: 3,
  rightEyeInner: 4,
  rightEye: 5,
  rightEyeOuter: 6,
  leftEar: 7,
  rightEar: 8,
  mouthLeft: 9,
  mouthRight: 10,
  
  // Upper body landmarks (11-22)
  leftShoulder: 11,
  rightShoulder: 12,
  leftElbow: 13,
  rightElbow: 14,
  leftWrist: 15,
  rightWrist: 16,
  leftPinky: 17,
  rightPinky: 18,
  leftIndex: 19,
  rightIndex: 20,
  leftThumb: 21,
  rightThumb: 22,
  
  // Lower body landmarks (23-32)
  leftHip: 23,
  rightHip: 24,
  leftKnee: 25,
  rightKnee: 26,
  leftAnkle: 27,
  rightAnkle: 28,
  leftHeel: 29,
  rightHeel: 30,
  leftFootIndex: 31,
  rightFootIndex: 32,
  
  // Additional Full model landmarks (33-38)
  leftWristPinky: 33,
  leftWristIndex: 34,
  leftWristThumb: 35,
  rightWristPinky: 36,
  rightWristIndex: 37,
  rightWristThumb: 38
} as const;

// =============================================================================
// POSE TYPES
// =============================================================================

/**
 * Single pose detection result
 */
export interface Pose {
  keypoints: Keypoint[];
  keypoints3D?: Keypoint[];
  score?: number;
  box?: BoundingBox;
  id?: number;
}

/**
 * Extended pose with additional metadata
 */
export interface ExtendedPose extends Pose {
  timestamp: number;
  frameNumber: number;
  smoothed?: boolean;
  modelType?: 'lite' | 'full' | 'heavy';
  viewType?: 'side' | 'rear';
}

/**
 * Bounding box for detected person
 */
export interface BoundingBox {
  xMin: number;
  yMin: number;
  xMax: number;
  yMax: number;
  width: number;
  height: number;
}

// =============================================================================
// BLAZEPOSE SPECIFIC TYPES
// =============================================================================

/**
 * BlazePose model variants
 */
export type BlazePoseModelType = 'lite' | 'full' | 'heavy';

/**
 * BlazePose estimation configuration
 */
export interface BlazePoseEstimationConfig {
  maxPoses?: number;
  flipHorizontal?: boolean;
  enableSmoothing?: boolean;
  enableSegmentation?: boolean;
  smoothSegmentation?: boolean;
  minPoseDetectionConfidence?: number;
  minPosePresenceConfidence?: number;
  minTrackingConfidence?: number;
}

/**
 * BlazePose detector configuration
 */
export interface BlazePoseDetectorConfig {
  runtime: 'tfjs' | 'mediapipe';
  modelType: BlazePoseModelType;
  detectorModelUrl?: string;
  landmarkModelUrl?: string;
  solutionPath?: string;
}

// =============================================================================
// POSE PROCESSING TYPES
// =============================================================================

/**
 * Pose frame data for video processing
 */
export interface PoseFrame {
  poses: Pose[];
  timestamp: number;
  frameNumber: number;
  videoTime: number;
  processingTime?: number;
}

/**
 * Pose sequence for temporal analysis
 */
export interface PoseSequence {
  frames: PoseFrame[];
  videoUrl: string;
  viewType: 'side' | 'rear';
  totalFrames: number;
  fps: number;
  duration: number;
}

/**
 * Processed pose data from Modal/SmoothNet
 */
export interface ProcessedPoseData {
  keypoints: Keypoint[];
  keypoints3D?: Keypoint[];
  score: number;
  timestamp: number;
  frameNumber: number;
  modelType: string;
  smoothed: boolean;
  smoothingMethod?: 'smoothnet' | 'one-euro' | 'kalman';
}

// =============================================================================
// COORDINATE SYSTEM TYPES
// =============================================================================

/**
 * Coordinate space definitions
 */
export type CoordinateSpace = 'normalized' | 'pixel' | 'world';

/**
 * Coordinate transformation parameters
 */
export interface CoordinateTransform {
  scale: number;
  offsetX: number;
  offsetY: number;
  pixelsPerMeter: number;
  userHeightInMeters: number;
  videoWidth: number;
  videoHeight: number;
}

// =============================================================================
// KEYPOINT CONNECTIONS
// =============================================================================

/**
 * Connection between two keypoints for skeleton rendering
 */
export interface KeypointConnection {
  start: number;
  end: number;
  color?: string;
  width?: number;
}

/**
 * BlazePose skeleton connections
 */
export const BLAZEPOSE_CONNECTIONS: KeypointConnection[] = [
  // Face
  { start: 0, end: 1 }, { start: 0, end: 4 },
  { start: 1, end: 2 }, { start: 2, end: 3 },
  { start: 3, end: 7 }, { start: 4, end: 5 },
  { start: 5, end: 6 }, { start: 6, end: 8 },
  { start: 9, end: 10 },
  
  // Torso
  { start: 11, end: 12 }, { start: 11, end: 13 },
  { start: 11, end: 23 }, { start: 12, end: 14 },
  { start: 12, end: 24 }, { start: 23, end: 24 },
  
  // Arms
  { start: 13, end: 15 }, { start: 14, end: 16 },
  { start: 15, end: 17 }, { start: 15, end: 19 },
  { start: 15, end: 21 }, { start: 16, end: 18 },
  { start: 16, end: 20 }, { start: 16, end: 22 },
  { start: 17, end: 19 }, { start: 18, end: 20 },
  
  // Legs
  { start: 23, end: 25 }, { start: 24, end: 26 },
  { start: 25, end: 27 }, { start: 26, end: 28 },
  { start: 27, end: 29 }, { start: 28, end: 30 },
  { start: 27, end: 31 }, { start: 28, end: 32 },
  { start: 29, end: 31 }, { start: 30, end: 32 }
];

// =============================================================================
// UTILITY TYPES
// =============================================================================

/**
 * Keypoint side classification
 */
export type KeypointSide = 'left' | 'right' | 'middle';

/**
 * Keypoint group classification
 */
export type KeypointGroup = 'face' | 'torso' | 'arms' | 'legs' | 'hands' | 'feet';

/**
 * Get keypoint by name type helper
 */
export type KeypointByName<T extends readonly Keypoint[]> = {
  [K in BlazePoseKeypointName]?: T[number];
};