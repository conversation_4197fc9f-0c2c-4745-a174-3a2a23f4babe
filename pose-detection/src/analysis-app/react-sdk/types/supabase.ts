/**
 * Supabase Database Type Definitions
 * Types matching the bio-run-schema.sql database tables
 */

// =============================================================================
// DATABASE ENUMS
// =============================================================================

/**
 * Video view types
 */
export type VideoViewType = 'side' | 'rear';

/**
 * Analysis status states
 */
export type AnalysisStatus = 
  | 'uploading'
  | 'pending'
  | 'processing_side'
  | 'processing_rear'
  | 'completed'
  | 'error';

/**
 * Processing queue status
 */
export type ProcessingQueueStatus = 
  | 'queued'
  | 'processing'
  | 'completed'
  | 'failed';

/**
 * User gender options
 */
export type UserGender = 'male' | 'female';

// =============================================================================
// TABLE: bio_run_videos
// =============================================================================

/**
 * Video file metadata
 */
export interface BioRunVideo {
  id: string;
  created_at: string;
  updated_at: string;
  
  // File Information
  filename: string;
  original_filename: string;
  file_size_bytes: number;
  mime_type: string;
  
  // S3 Storage
  s3_bucket: string;
  s3_key: string;
  s3_url: string;
  s3_upload_completed: boolean;
  
  // Video Metadata
  duration_seconds: number;
  width: number;
  height: number;
  fps: number;
  codec?: string;
  bitrate?: number;
  
  // Analysis Type
  view_type: VideoViewType;
  
  // Validation
  is_valid: boolean;
  validation_errors?: Record<string, any>;
  
  // User tracking (temporary)
  user_email: string;
  session_id: string;
}

/**
 * Video creation payload
 */
export interface BioRunVideoInsert {
  filename: string;
  original_filename: string;
  file_size_bytes: number;
  mime_type: string;
  s3_bucket: string;
  s3_key: string;
  s3_url: string;
  duration_seconds: number;
  width: number;
  height: number;
  fps: number;
  view_type: VideoViewType;
  user_email: string;
  session_id: string;
  codec?: string;
  bitrate?: number;
}

// =============================================================================
// TABLE: bio_run_analysis
// =============================================================================

/**
 * Main analysis session
 */
export interface BioRunAnalysis {
  id: string;
  created_at: string;
  updated_at: string;
  
  // User Information
  user_email: string;
  
  // Runner Biometrics
  height_inches: number;
  height_display: string; // "5'10"" or "1m75cm"
  gender: UserGender;
  weight_lbs: number;
  weight_display: string; // "150 lbs" or "68 kg"
  
  // Video References
  side_video_id?: string;
  rear_video_id?: string;
  
  // Processing Status
  status: AnalysisStatus;
  processing_started_at?: string;
  processing_completed_at?: string;
  error_message?: string;
  error_details?: Record<string, any>;
  
  // SmoothNet Processed Results
  side_analysis_json?: any; // JSONB - Complete smoothed keypoints
  rear_analysis_json?: any; // JSONB
  
  // S3 Backup URLs
  side_metrics_s3_url?: string;
  rear_metrics_s3_url?: string;
  
  // Processing Metadata
  modal_job_id?: string;
  processing_duration_seconds?: number;
  model_version?: string;
  smoothnet_version?: string;
  
  // Extracted Summary Metrics
  cadence_avg?: number;
  stride_length_avg?: number;
  vertical_oscillation_avg?: number;
  ground_contact_time_avg?: number;
  lean_angle_avg?: number;
  
  // Analysis Summary
  analysis_summary?: string;
  key_findings?: Record<string, any>;
  
  // Quality Metrics
  side_detection_confidence?: number;
  rear_detection_confidence?: number;
  side_frames_processed?: number;
  rear_frames_processed?: number;
}

/**
 * Analysis creation payload
 */
export interface BioRunAnalysisInsert {
  user_email: string;
  height_inches: number;
  height_display: string;
  gender: UserGender;
  weight_lbs: number;
  weight_display: string;
  status?: AnalysisStatus;
}

/**
 * Analysis update payload
 */
export interface BioRunAnalysisUpdate {
  side_video_id?: string;
  rear_video_id?: string;
  status?: AnalysisStatus;
  processing_started_at?: string;
  processing_completed_at?: string;
  error_message?: string;
  error_details?: Record<string, any>;
  side_analysis_json?: any;
  rear_analysis_json?: any;
  side_metrics_s3_url?: string;
  rear_metrics_s3_url?: string;
  modal_job_id?: string;
  processing_duration_seconds?: number;
  model_version?: string;
  smoothnet_version?: string;
  cadence_avg?: number;
  stride_length_avg?: number;
  vertical_oscillation_avg?: number;
  ground_contact_time_avg?: number;
  lean_angle_avg?: number;
  analysis_summary?: string;
  key_findings?: Record<string, any>;
  side_detection_confidence?: number;
  rear_detection_confidence?: number;
  side_frames_processed?: number;
  rear_frames_processed?: number;
}

// =============================================================================
// TABLE: bio_run_upload_sessions
// =============================================================================

/**
 * Upload session tracking
 */
export interface BioRunUploadSession {
  id: string;
  created_at: string;
  analysis_id: string;
  
  // Upload Progress
  side_video_uploaded: boolean;
  rear_video_uploaded: boolean;
  upload_completed: boolean;
  
  // S3 multipart upload tracking
  side_upload_id?: string;
  rear_upload_id?: string;
  side_temp_key?: string;
  rear_temp_key?: string;
  
  // Session Management
  session_token: string;
  expires_at: string;
  
  // Upload Metadata
  upload_started_at?: string;
  side_upload_completed_at?: string;
  rear_upload_completed_at?: string;
}

// =============================================================================
// TABLE: bio_modal_processing_queue
// =============================================================================

/**
 * Modal GPU processing queue
 */
export interface BioModalProcessingQueue {
  id: string;
  created_at: string;
  
  // Analysis Reference
  analysis_id: string;
  video_id: string;
  view_type: VideoViewType;
  
  // Queue Status
  status: ProcessingQueueStatus;
  priority: number;
  
  // Modal Job Details
  modal_job_id?: string;
  modal_function_name: string;
  modal_request_payload?: Record<string, any>;
  modal_response_payload?: Record<string, any>;
  
  // Processing Timeline
  queued_at: string;
  started_at?: string;
  completed_at?: string;
  
  // Error Tracking
  retry_count: number;
  last_error?: string;
  error_log?: string[];
  
  // Processing Results
  output_s3_url?: string;
  processing_stats?: Record<string, any>;
}

/**
 * Processing queue creation payload
 */
export interface BioModalProcessingQueueInsert {
  analysis_id: string;
  video_id: string;
  view_type: VideoViewType;
  priority?: number;
  modal_function_name?: string;
  modal_request_payload?: Record<string, any>;
}

// =============================================================================
// SUPABASE CLIENT TYPES
// =============================================================================

/**
 * Database schema type for Supabase client
 */
export interface Database {
  public: {
    Tables: {
      bio_run_videos: {
        Row: BioRunVideo;
        Insert: BioRunVideoInsert;
        Update: Partial<BioRunVideoInsert>;
      };
      bio_run_analysis: {
        Row: BioRunAnalysis;
        Insert: BioRunAnalysisInsert;
        Update: BioRunAnalysisUpdate;
      };
      bio_run_upload_sessions: {
        Row: BioRunUploadSession;
        Insert: Partial<BioRunUploadSession>;
        Update: Partial<BioRunUploadSession>;
      };
      bio_modal_processing_queue: {
        Row: BioModalProcessingQueue;
        Insert: BioModalProcessingQueueInsert;
        Update: Partial<BioModalProcessingQueue>;
      };
    };
    Views: {};
    Functions: {};
    Enums: {
      video_view_type: VideoViewType;
      analysis_status: AnalysisStatus;
      processing_queue_status: ProcessingQueueStatus;
      user_gender: UserGender;
    };
  };
}

// =============================================================================
// QUERY HELPERS
// =============================================================================

/**
 * Analysis query with video joins
 */
export type AnalysisWithVideos = BioRunAnalysis & {
  side_video?: BioRunVideo;
  rear_video?: BioRunVideo;
};

/**
 * Processing queue with analysis
 */
export type ProcessingQueueWithAnalysis = BioModalProcessingQueue & {
  analysis?: BioRunAnalysis;
  video?: BioRunVideo;
};

/**
 * Upload session with analysis
 */
export type UploadSessionWithAnalysis = BioRunUploadSession & {
  analysis?: BioRunAnalysis;
};