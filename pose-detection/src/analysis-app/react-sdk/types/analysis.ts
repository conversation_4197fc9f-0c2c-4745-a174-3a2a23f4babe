/**
 * Analysis Type Definitions
 * Core types for biomechanical analysis data and workflows
 */

import { Pose, ProcessedPoseData } from './pose';
import { RunningMetrics, FootStrikeMetric, MetricValue } from './metrics';

// =============================================================================
// ANALYSIS STATE TYPES
// =============================================================================

/**
 * Analysis workflow states
 */
export type AnalysisState = 'upload' | 'processing' | 'results' | 'error';

/**
 * Processing status for real-time updates
 */
export interface ProcessingStatus {
  isProcessing: boolean;
  progress: number;
  currentFrame: number;
  totalFrames: number;
  elapsed: number;
  remaining: number;
  status: 'idle' | 'loading' | 'processing' | 'complete' | 'error' | 'ready' | 'playing';
  message?: string;
  stage?: 'detection' | 'smoothing' | 'metrics' | 'finalizing';
}

// =============================================================================
// VIDEO TYPES
// =============================================================================

/**
 * Video file information
 */
export interface VideoFile {
  file: File;
  url: string;
  name: string;
  size: number;
  duration?: number;
  width?: number;
  height?: number;
  fps?: number;
}

/**
 * Processed video result
 */
export interface ProcessedVideo {
  originalUrl: string;
  processedUrl: string;
  thumbnailUrl?: string;
  poseDataUrl: string;
  viewType: 'side' | 'rear';
  duration: number;
  fps: number;
  frameCount: number;
}

// =============================================================================
// USER CONFIGURATION TYPES
// =============================================================================

/**
 * User height measurement
 */
export interface UserHeight {
  feet: number;
  inches: number;
  totalInches?: number;
  meters?: number;
}

/**
 * User configuration for analysis
 */
export interface UserConfiguration {
  height: UserHeight;
  gender?: 'male' | 'female' | 'other' | 'prefer-not-to-say';
  weight?: number;
  weightUnit: 'lbs' | 'kg';
  age?: number;
  experienceLevel?: 'beginner' | 'intermediate' | 'advanced' | 'elite';
  injuryHistory?: string[];
}

// =============================================================================
// ANALYSIS CONFIGURATION TYPES
// =============================================================================

/**
 * Analysis type options
 */
export type AnalysisType = 'running' | 'walking' | 'cycling' | 'swimming';

/**
 * View type for video capture
 */
export type ViewType = 'side' | 'rear' | 'front';

/**
 * Analysis quality levels
 */
export type AnalysisQuality = 'lite' | 'standard' | 'full';

/**
 * Analysis configuration
 */
export interface AnalysisConfiguration {
  analysisType: AnalysisType;
  analysisMode: '2D' | '3D';
  activityType: string;
  videoSetup: 'treadmill' | 'outdoor' | 'track' | 'stationary';
  overlayStyle: 'medical' | 'athletic' | 'minimal' | 'debug';
  analysisQuality: AnalysisQuality;
  viewType: ViewType;
  enableSmoothNet?: boolean;
  enableMetrics?: boolean;
  debugMode?: boolean;
}

// =============================================================================
// ANALYSIS DATA TYPES
// =============================================================================

/**
 * Complete analysis result
 */
export interface AnalysisData {
  id: string;
  userId: string;
  userEmail: string;
  createdAt: string;
  updatedAt: string;
  
  // Configuration
  userConfig: UserConfiguration;
  analysisConfig: AnalysisConfiguration;
  
  // Results
  videos: AnalysisVideo[];
  metrics: AnalysisMetrics;
  insights: AnalysisInsight[];
  recommendations: Recommendation[];
  
  // Processing info
  processingTime: number;
  modelVersion: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  error?: string;
}

/**
 * Video analysis result
 */
export interface AnalysisVideo {
  id: string;
  analysisId: string;
  viewType: ViewType;
  
  // URLs
  originalVideoUrl: string;
  processedVideoUrl: string;
  poseDataUrl: string;
  thumbnailUrl?: string;
  
  // Video info
  duration: number;
  fps: number;
  width: number;
  height: number;
  frameCount: number;
  
  // Pose data
  poseSequence?: ProcessedPoseData[];
  keyFrames?: KeyFrame[];
}

/**
 * Key frame for important moments
 */
export interface KeyFrame {
  frameNumber: number;
  timestamp: number;
  type: 'heel-strike' | 'toe-off' | 'mid-stance' | 'peak-flexion';
  pose: Pose;
  metrics?: Partial<RunningMetrics>;
}

// =============================================================================
// METRICS TYPES
// =============================================================================

/**
 * Complete metrics analysis
 */
export interface AnalysisMetrics {
  // View-specific metrics
  sideViewMetrics?: SideViewMetrics;
  rearViewMetrics?: RearViewMetrics;
  
  // Combined metrics
  overallScore: number;
  performanceLevel: 'beginner' | 'intermediate' | 'advanced' | 'elite';
  injuryRisk: 'low' | 'moderate' | 'high';
  efficiency: number;
}

/**
 * Side view specific metrics
 */
export interface SideViewMetrics {
  strideLength: MetricValue;
  cadence: MetricValue;
  verticalOscillation: MetricValue;
  forwardLean: MetricValue;
  groundContactTime: MetricValue;
  kneeFlexion: MetricValue;
  footStrike: FootStrikeMetric;
}

/**
 * Rear view specific metrics
 */
export interface RearViewMetrics {
  hipDrop: MetricValue;
  kneeAlignment: MetricValue;
  lateralMovement: MetricValue;
  pelvicRotation: MetricValue;
  footStrikeSymmetry: MetricValue;
  shoulderAlignment: MetricValue;
  armSwingSymmetry: MetricValue;
}

// MetricValue is defined in ./metrics.ts and imported there


// =============================================================================
// INSIGHTS & RECOMMENDATIONS
// =============================================================================

/**
 * Analysis insight
 */
export interface AnalysisInsight {
  id: string;
  category: 'form' | 'efficiency' | 'injury-prevention' | 'performance';
  severity: 'info' | 'warning' | 'critical';
  title: string;
  description: string;
  affectedMetrics: string[];
  videoTimestamp?: number;
  frameNumber?: number;
}

/**
 * Training recommendation
 */
export interface Recommendation {
  id: string;
  priority: 'high' | 'medium' | 'low';
  category: 'exercise' | 'drill' | 'cue' | 'equipment';
  title: string;
  description: string;
  rationale: string;
  exercises?: Exercise[];
  duration?: string;
  frequency?: string;
  relatedInsights: string[];
}

/**
 * Recommended exercise
 */
export interface Exercise {
  name: string;
  description: string;
  sets?: number;
  reps?: number;
  duration?: string;
  videoUrl?: string;
  imageUrl?: string;
}

// =============================================================================
// API RESPONSE TYPES
// =============================================================================

/**
 * Analysis creation request
 */
export interface CreateAnalysisRequest {
  videos: {
    side?: File;
    rear?: File;
  };
  userConfig: UserConfiguration;
  analysisConfig: AnalysisConfiguration;
}

/**
 * Analysis response
 */
export interface AnalysisResponse {
  analysis: AnalysisData;
  processingUrl?: string;
  estimatedTime?: number;
}

/**
 * Analysis list response
 */
export interface AnalysisListResponse {
  analyses: AnalysisData[];
  total: number;
  page: number;
  pageSize: number;
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

/**
 * Analysis export format
 */
export type ExportFormat = 'pdf' | 'csv' | 'json' | 'video';

/**
 * Analysis share options
 */
export interface ShareOptions {
  includeVideo: boolean;
  includeMetrics: boolean;
  includeRecommendations: boolean;
  expirationDays?: number;
  password?: string;
}