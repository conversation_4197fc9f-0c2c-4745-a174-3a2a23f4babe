/**
 * Metrics Type Definitions
 * Running biomechanical metrics and calculations
 */

// =============================================================================
// METRIC STATUS TYPES
// =============================================================================

/**
 * Metric status levels
 */
export type MetricStatus = 'excellent' | 'good' | 'needs-improvement' | 'poor';

/**
 * Metric category for grouping
 */
export type MetricCategory = 
  | 'efficiency'
  | 'form'
  | 'power'
  | 'stability'
  | 'symmetry'
  | 'timing';

// =============================================================================
// BASE METRIC TYPES
// =============================================================================

/**
 * Base metric structure
 */
export interface BaseMetric {
  value: number;
  unit: string;
  score: number;
  status: MetricStatus;
  description: string;
  category?: MetricCategory;
  confidence?: number;
}

/**
 * Extended metric with additional context
 */
export interface ExtendedMetric extends BaseMetric {
  percentile?: number;
  referenceRange?: ReferenceRange;
  trend?: MetricTrend;
  history?: MetricHistory[];
}

/**
 * Reference range for metric evaluation
 */
export interface ReferenceRange {
  min: number;
  max: number;
  optimal: number;
  unit: string;
  population?: string;
}

/**
 * Metric trend information
 */
export interface MetricTrend {
  direction: 'improving' | 'stable' | 'declining';
  changePercent: number;
  periodDays: number;
}

/**
 * Historical metric value
 */
export interface MetricHistory {
  value: number;
  timestamp: string;
  analysisId: string;
}

// =============================================================================
// RUNNING METRICS
// =============================================================================

/**
 * Simple metric value structure
 */
export interface MetricValue {
  value: number;
  unit: string;
  score: number;
  status: 'excellent' | 'good' | 'needs-work';
  description: string;
}

/**
 * Complete running metrics set
 */
export interface RunningMetrics {
  // Core metrics for Phase 7 implementation
  cadence: MetricValue;
  strideLength: MetricValue;
  stepHeight: MetricValue;
  verticalOscillation: MetricValue;
  groundContactTime: MetricValue;
  overallScore: number;
  
  // Full metrics set (for future phases)
  // Spatial metrics
  stepLength?: SpatialMetric;
  lateralMovement?: SpatialMetric;
  
  // Temporal metrics
  flightTime?: TemporalMetric;
  
  // Angular metrics
  forwardLean?: AngularMetric;
  kneeFlexion?: AngularMetric;
  hipExtension?: AngularMetric;
  ankleFlexion?: AngularMetric;
  
  // Form metrics
  footStrike?: FootStrikeMetric;
  posture?: PostureMetric;
  armSwing?: ArmSwingMetric;
  
  // Symmetry metrics
  symmetry?: SymmetryMetrics;
  
  // Efficiency metrics
  efficiency?: EfficiencyMetrics;
}

// =============================================================================
// SPATIAL METRICS
// =============================================================================

/**
 * Spatial measurement metric
 */
export interface SpatialMetric extends BaseMetric {
  unit: 'cm' | 'inches' | 'm' | 'ft';
  leftValue?: number;
  rightValue?: number;
  asymmetry?: number;
}

// =============================================================================
// TEMPORAL METRICS
// =============================================================================

/**
 * Time-based measurement metric
 */
export interface TemporalMetric extends BaseMetric {
  unit: 'ms' | 's' | 'steps/min' | 'bpm';
  variability?: number;
  consistency?: number;
}

// =============================================================================
// ANGULAR METRICS
// =============================================================================

/**
 * Angle measurement metric
 */
export interface AngularMetric extends BaseMetric {
  unit: 'degrees' | 'radians';
  minAngle?: number;
  maxAngle?: number;
  rangeOfMotion?: number;
  velocity?: number;
}

// =============================================================================
// SPECIALIZED METRICS
// =============================================================================

/**
 * Foot strike analysis
 */
export interface FootStrikeMetric {
  type: 'heel' | 'midfoot' | 'forefoot';
  angle: number;
  consistency: number;
  score: number;
  status: MetricStatus;
  description: string;
  distribution?: {
    heel: number;
    midfoot: number;
    forefoot: number;
  };
}

/**
 * Posture analysis
 */
export interface PostureMetric {
  alignment: number;
  score: number;
  status: MetricStatus;
  description: string;
  components: {
    head: AngularMetric;
    shoulders: AngularMetric;
    hips: AngularMetric;
    trunk: AngularMetric;
  };
}

/**
 * Arm swing analysis
 */
export interface ArmSwingMetric {
  amplitude: SpatialMetric;
  frequency: TemporalMetric;
  symmetry: number;
  crossover: boolean;
  score: number;
  status: MetricStatus;
  description: string;
}

// =============================================================================
// SYMMETRY METRICS
// =============================================================================

/**
 * Bilateral symmetry analysis
 */
export interface SymmetryMetrics {
  overall: number;
  temporal: number;
  spatial: number;
  force: number;
  score: number;
  status: MetricStatus;
  details: {
    strideLength: SymmetryDetail;
    groundContactTime: SymmetryDetail;
    kneeFlexion: SymmetryDetail;
    hipDrop: SymmetryDetail;
  };
}

/**
 * Individual symmetry measurement
 */
export interface SymmetryDetail {
  leftValue: number;
  rightValue: number;
  difference: number;
  percentDifference: number;
  acceptable: boolean;
}

// =============================================================================
// EFFICIENCY METRICS
// =============================================================================

/**
 * Running efficiency analysis
 */
export interface EfficiencyMetrics {
  overall: number;
  mechanical: number;
  metabolic: number;
  score: number;
  status: MetricStatus;
  factors: {
    verticalOscillation: EfficiencyFactor;
    groundContactTime: EfficiencyFactor;
    cadence: EfficiencyFactor;
    strideLength: EfficiencyFactor;
  };
}

/**
 * Efficiency contributing factor
 */
export interface EfficiencyFactor {
  name: string;
  impact: number;
  optimal: boolean;
  suggestion?: string;
}

// =============================================================================
// METRIC CARDS FOR UI
// =============================================================================

/**
 * Metric card for display
 */
export interface MetricCard {
  id: string;
  label: string;
  value: string | number;
  unit?: string;
  status: MetricStatus;
  icon?: string;
  color?: string;
  description?: string;
  details?: string;
  trend?: MetricTrend;
}

/**
 * Metric group for organized display
 */
export interface MetricGroup {
  id: string;
  title: string;
  category: MetricCategory;
  metrics: MetricCard[];
  summary?: string;
  priority?: number;
}

// =============================================================================
// METRIC CALCULATIONS
// =============================================================================

/**
 * Metric calculation input
 */
export interface MetricCalculationInput {
  poses: any[]; // ProcessedPoseData[]
  userHeight: number; // in meters
  videoFps: number;
  viewType: 'side' | 'rear';
}

/**
 * Metric calculation result
 */
export interface MetricCalculationResult {
  metrics: Partial<RunningMetrics>;
  confidence: number;
  warnings?: string[];
  frameCoverage: number;
}

// =============================================================================
// METRIC THRESHOLDS
// =============================================================================

/**
 * Metric evaluation thresholds
 */
export interface MetricThresholds {
  excellent: { min: number; max: number };
  good: { min: number; max: number };
  needsImprovement: { min: number; max: number };
  poor: { min: number; max: number };
}

/**
 * Complete threshold configuration
 */
export interface MetricThresholdConfig {
  strideLength: MetricThresholds;
  cadence: MetricThresholds;
  verticalOscillation: MetricThresholds;
  groundContactTime: MetricThresholds;
  forwardLean: MetricThresholds;
  kneeFlexion: MetricThresholds;
  [key: string]: MetricThresholds;
}

// =============================================================================
// EXPORT TYPES
// =============================================================================

/**
 * Metrics export format
 */
export interface MetricsExport {
  analysisId: string;
  timestamp: string;
  userInfo: {
    height: number;
    weight?: number;
    age?: number;
  };
  metrics: RunningMetrics;
  insights: string[];
  recommendations: string[];
}