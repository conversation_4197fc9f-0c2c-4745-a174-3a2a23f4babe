/**
 * ProcessedVideoPlayer Component Styles
 * 
 * Styling for portrait video optimization (9:16 aspect ratio)
 * and responsive layout handling.
 */

.video-player-container {
  width: 100%;
  max-width: 100%;
}

/* Video Wrapper - Optimized for Portrait Videos */
.video-wrapper {
  position: relative;
  width: 100%;
  margin: 0 auto;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #000;
  
  /* Default sizing for portrait videos */
  width: clamp(280px, 90vw, 400px);
  max-width: 100%;
  aspect-ratio: 9/16; /* Enforce 9:16 portrait aspect ratio */
}

/* Video Element */
.video-element {
  width: 100%;
  height: 100%;
  display: block;
  border-radius: 12px;
  object-fit: contain; /* Maintain aspect ratio without distortion */
  aspect-ratio: 9/16; /* Enforce 9:16 for portrait videos */
  background: #000;
}

/* Canvas Overlay */
.pose-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  border-radius: 12px;
  /* Canvas size is set programmatically to match video exactly */
}

/* Responsive Breakpoints */

/* Mobile Portrait (up to 480px) */
@media (max-width: 480px) {
  .video-wrapper {
    width: clamp(250px, 85vw, 350px);
    border-radius: 8px;
  }
  
  .video-element,
  .pose-overlay {
    border-radius: 8px;
  }
}

/* Mobile Landscape & Small Tablets (481px - 768px) */
@media (min-width: 481px) and (max-width: 768px) {
  .video-wrapper {
    width: clamp(300px, 60vw, 380px);
  }
}

/* Tablets (769px - 1024px) */
@media (min-width: 769px) and (max-width: 1024px) {
  .video-wrapper {
    width: clamp(320px, 50vw, 400px);
  }
}

/* Desktop (1025px - 1440px) */
@media (min-width: 1025px) and (max-width: 1440px) {
  .video-wrapper {
    width: clamp(350px, 30vw, 420px);
  }
}

/* Large Desktop (1441px+) */
@media (min-width: 1441px) {
  .video-wrapper {
    width: clamp(380px, 25vw, 450px);
  }
}

/* Special handling for landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  .video-wrapper {
    width: clamp(200px, 40vh, 300px);
    height: auto;
  }
}

/* Video Info Panel */
.video-info-panel {
  margin-top: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
}

.video-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.video-info-grid > div {
  display: flex;
  flex-direction: column;
}

.video-info-label {
  color: #888;
  font-size: 11px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 2px;
}

.video-info-value {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-weight: 500;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.portrait-optimized {
  background: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #a5d6a7;
}

.status-badge.check-orientation {
  background: #fff3e0;
  color: #ef6c00;
  border: 1px solid #ffb74d;
}

/* Warning Panel */
.orientation-warning {
  margin-top: 8px;
  padding: 8px 12px;
  background: #fff8e1;
  border: 1px solid #ffb74d;
  border-radius: 6px;
  font-size: 12px;
  color: #e65100;
}

.orientation-warning strong {
  font-weight: 600;
}

/* Loading States */
.video-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 9/16;
  background: #f5f5f5;
  border-radius: 12px;
  color: #666;
}

.video-loading .spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #2196f3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error States */
.video-error {
  display: flex;
  align-items: center;
  justify-content: center;
  aspect-ratio: 9/16;
  background: #ffebee;
  border: 1px solid #e57373;
  border-radius: 12px;
  color: #c62828;
  text-align: center;
  padding: 20px;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .video-wrapper {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .video-info-panel {
    background: #2a2a2a;
    color: #e0e0e0;
  }
  
  .video-info-grid {
    color: #bbb;
  }
  
  .video-info-label {
    color: #888;
  }
  
  .status-badge.portrait-optimized {
    background: #1b5e20;
    color: #c8e6c9;
    border-color: #4caf50;
  }
  
  .status-badge.check-orientation {
    background: #e65100;
    color: #ffcc02;
    border-color: #ff9800;
  }
  
  .orientation-warning {
    background: #3e2723;
    border-color: #8d6e63;
    color: #ffab40;
  }
  
  .video-loading {
    background: #2a2a2a;
    color: #bbb;
  }
  
  .video-error {
    background: #3a1b1b;
    border-color: #ef5350;
    color: #ffcdd2;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .video-wrapper {
    border: 2px solid #000;
  }
  
  @media (prefers-color-scheme: dark) {
    .video-wrapper {
      border-color: #fff;
    }
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .video-loading .spinner {
    animation: none;
  }
  
  .video-wrapper {
    transition: none;
  }
}