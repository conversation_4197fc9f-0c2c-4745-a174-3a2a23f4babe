/**
 * TabsContainer Component Styles
 * 
 * Styling for the tabs container component with view switching
 * and status indicators.
 */

.tabs-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Tab Navigation */
.tabs-nav {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  position: relative;
}

.tabs-nav::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

/* Tab Button */
.tab-button {
  flex: 1;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-button:hover:not(.disabled):not(.unavailable) {
  background: rgba(33, 150, 243, 0.05);
}

.tab-button:focus {
  outline: 2px solid #2196f3;
  outline-offset: -2px;
  z-index: 1;
}

.tab-button.active {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
}

.tab-button.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.tab-button.unavailable {
  cursor: not-allowed;
  opacity: 0.4;
  background: #f5f5f5;
}

.tab-button.processing {
  background: rgba(33, 150, 243, 0.1);
}

.tab-button.completed {
  background: rgba(76, 175, 80, 0.1);
}

.tab-button.error {
  background: rgba(244, 67, 54, 0.1);
}

/* Tab Content */
.tab-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  text-align: center;
  width: 100%;
}

.tab-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.tab-icon {
  font-size: 20px;
  line-height: 1;
}

.tab-label {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

.tab-button.active .tab-label {
  color: #2196f3;
}

.tab-description {
  font-size: 12px;
  color: #666;
  line-height: 1.3;
  margin-bottom: 4px;
}

.tab-button.active .tab-description {
  color: #555;
}

/* Tab Badges */
.tab-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 12px;
  line-height: 1;
}

.tab-badge.processing {
  background: rgba(33, 150, 243, 0.2);
  color: #1565c0;
}

.tab-badge.completed {
  background: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
}

.tab-badge.error {
  background: rgba(244, 67, 54, 0.2);
  color: #c62828;
}

.tab-badge.unavailable {
  background: rgba(158, 158, 158, 0.2);
  color: #757575;
}

.badge-icon {
  font-size: 10px;
  line-height: 1;
}

.badge-progress {
  font-size: 9px;
  font-weight: 700;
}

/* Tab Progress Bar */
.tab-progress {
  width: 100%;
  height: 2px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 1px;
  overflow: hidden;
  margin-top: 4px;
}

.tab-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #2196f3, #21cbf3);
  transition: width 0.3s ease;
  border-radius: 1px;
}

/* Tab Panel Content */
.tabs-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Allow flex item to shrink */
}

.tab-panel {
  flex: 1;
  padding: 20px;
  overflow: auto;
  outline: none;
}

.tab-panel:focus {
  outline: 2px solid #2196f3;
  outline-offset: -2px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tabs-nav {
    flex-direction: column;
  }

  .tab-button {
    min-height: 60px;
    border-bottom: 1px solid #e9ecef;
  }

  .tab-button:last-child {
    border-bottom: none;
  }

  .tab-button.active::after {
    height: 0;
    right: 0;
    top: 0;
    bottom: 0;
    left: auto;
    width: 3px;
  }

  .tab-content {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    padding: 12px 16px;
  }

  .tab-header {
    margin-bottom: 0;
  }

  .tab-description {
    margin-bottom: 0;
    margin-left: auto;
  }

  .tab-panel {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .tab-content {
    padding: 10px 12px;
  }

  .tab-icon {
    font-size: 18px;
  }

  .tab-label {
    font-size: 13px;
  }

  .tab-description {
    font-size: 11px;
  }

  .tab-panel {
    padding: 12px;
  }
}

/* Animations */
@keyframes processing-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.tab-button.processing .tab-badge.processing {
  animation: processing-pulse 2s ease-in-out infinite;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .tabs-container {
    background: #1e1e1e;
    color: #e0e0e0;
  }

  .tabs-nav {
    background: #2a2a2a;
    border-bottom-color: #444;
  }

  .tab-button:hover:not(.disabled):not(.unavailable) {
    background: rgba(100, 181, 246, 0.1);
  }

  .tab-button.active {
    background: #1e1e1e;
  }

  .tab-button.unavailable {
    background: #333;
  }

  .tab-button.processing {
    background: rgba(100, 181, 246, 0.15);
  }

  .tab-button.completed {
    background: rgba(129, 199, 132, 0.15);
  }

  .tab-button.error {
    background: rgba(239, 83, 80, 0.15);
  }

  .tab-label {
    color: #e0e0e0;
  }

  .tab-button.active .tab-label {
    color: #64b5f6;
  }

  .tab-description {
    color: #bbb;
  }

  .tab-button.active .tab-description {
    color: #ccc;
  }

  .tab-badge.processing {
    background: rgba(100, 181, 246, 0.3);
    color: #64b5f6;
  }

  .tab-badge.completed {
    background: rgba(129, 199, 132, 0.3);
    color: #81c784;
  }

  .tab-badge.error {
    background: rgba(239, 83, 80, 0.3);
    color: #ef5350;
  }

  .tab-badge.unavailable {
    background: rgba(158, 158, 158, 0.3);
    color: #9e9e9e;
  }

  .tab-progress {
    background: rgba(255, 255, 255, 0.1);
  }

  .tab-panel {
    color: #e0e0e0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .tab-button:focus {
    outline: 3px solid #000;
  }

  .tab-button.active::after {
    background: #000;
  }

  @media (prefers-color-scheme: dark) {
    .tab-button:focus {
      outline-color: #fff;
    }

    .tab-button.active::after {
      background: #fff;
    }
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .tab-button,
  .tab-progress-fill,
  .tabs-nav::after {
    transition: none;
  }

  .tab-button.processing .tab-badge.processing {
    animation: none;
  }
}