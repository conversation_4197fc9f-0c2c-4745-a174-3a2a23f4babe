/**
 * ResultsPage Component
 * 
 * Main results display component for biomechanical analysis
 * Integrates video playback, pose visualization, and metrics display
 */

import React, { useState, useEffect, useCallback } from 'react';
import { ProcessedVideoPlayerModal } from './ProcessedVideoPlayerModal';
import { 
  ResultsPageProps,
  LoadingStateProps,
  ErrorStateProps,
  PageHeaderProps
} from '../types/components';
import { 
  ViewType, 
  ProcessingStatus,
  AnalysisData 
} from '../types/analysis';
import { useAnalysisData } from '../hooks/useAnalysisData';
import { useSupabaseAnalysis } from '../hooks/useSupabaseAnalysis';

/**
 * Loading state component
 */
const LoadingState: React.FC<LoadingStateProps> = ({ 
  message = 'Loading analysis results...', 
  progress, 
  status,
  showCancel,
  onCancel 
}) => (
  <div className="flex items-center justify-center min-h-[400px] bg-gray-50 rounded-lg">
    <div className="text-center space-y-4">
      <div className="relative">
        <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
        {progress !== undefined && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-xs font-medium text-blue-600">{Math.round(progress)}%</span>
          </div>
        )}
      </div>
      <div className="space-y-2">
        <p className="text-lg font-medium text-gray-900">{message}</p>
        {status && (
          <p className="text-sm text-gray-600 capitalize">
            Status: {status.stage?.replace('_', ' ') || 'Processing'}
          </p>
        )}
        {showCancel && onCancel && (
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  </div>
);

/**
 * Error state component
 */
const ErrorState: React.FC<ErrorStateProps> = ({ 
  error, 
  title = 'Error Loading Results',
  showRetry,
  onRetry,
  showSupport
}) => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  
  return (
    <div className="flex items-center justify-center min-h-[400px] bg-red-50 rounded-lg">
      <div className="text-center space-y-4 max-w-md">
        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto">
          <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <div className="space-y-2">
          <h3 className="text-lg font-medium text-red-900">{title}</h3>
          <p className="text-red-700">{errorMessage}</p>
        </div>
        <div className="flex space-x-3 justify-center">
          {showRetry && onRetry && (
            <button
              onClick={onRetry}
              className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Try Again
            </button>
          )}
          {showSupport && (
            <button
              onClick={() => window.open('mailto:<EMAIL>', '_blank')}
              className="px-4 py-2 text-sm font-medium text-red-700 bg-white border border-red-300 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              Contact Support
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Page header component
 */
const PageHeader: React.FC<PageHeaderProps> = ({ 
  title, 
  subtitle, 
  actions = [], 
  breadcrumbs = [] 
}) => (
  <div className="bg-white border-b border-gray-200 px-6 py-4">
    <div className="flex items-center justify-between">
      <div className="min-w-0 flex-1">
        {breadcrumbs.length > 0 && (
          <nav className="flex mb-2" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <svg className="flex-shrink-0 h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                  {crumb.href ? (
                    <a href={crumb.href} className="text-sm font-medium text-gray-500 hover:text-gray-700">
                      {crumb.label}
                    </a>
                  ) : crumb.onClick ? (
                    <button onClick={crumb.onClick} className="text-sm font-medium text-gray-500 hover:text-gray-700">
                      {crumb.label}
                    </button>
                  ) : (
                    <span className="text-sm font-medium text-gray-900">{crumb.label}</span>
                  )}
                </li>
              ))}
            </ol>
          </nav>
        )}
        <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
        {subtitle && (
          <p className="mt-1 text-sm text-gray-500">{subtitle}</p>
        )}
      </div>
      {actions.length > 0 && (
        <div className="flex space-x-3">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.onClick}
              className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                action.variant === 'primary' 
                  ? 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
                  : action.variant === 'secondary'
                  ? 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-blue-500'
                  : 'text-gray-500 hover:text-gray-700 focus:ring-blue-500'
              }`}
            >
              {action.icon && <span className="mr-2">{action.icon}</span>}
              {action.label}
            </button>
          ))}
        </div>
      )}
    </div>
  </div>
);

/**
 * Processing status component
 */
const ProcessingStatus: React.FC<{ 
  analysis: AnalysisData | null;
  onBack: () => void;
}> = ({ analysis, onBack }) => (
  <div className="flex items-center justify-center min-h-[400px] bg-blue-50 rounded-lg">
    <div className="text-center space-y-6 max-w-md">
      <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
      <div className="space-y-2">
        <h2 className="text-xl font-semibold text-blue-900">Processing Your Videos</h2>
        <p className="text-blue-700">
          Your running analysis is being processed with our advanced AI models.
        </p>
        <p className="text-sm text-blue-600">
          This typically takes 1-2 minutes per video.
        </p>
      </div>
      
      {analysis && (
        <div className="bg-white rounded-lg p-4 space-y-3">
          <h3 className="font-medium text-gray-900">Processing Status</h3>
          {analysis.videos.map((video) => (
            <div key={video.id} className="flex items-center justify-between">
              <span className="text-sm text-gray-600 capitalize">
                {video.viewType} View:
              </span>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                video.poseDataUrl 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {video.poseDataUrl ? '✓ Complete' : '⏳ Processing'}
              </span>
            </div>
          ))}
        </div>
      )}
      
      <button
        onClick={onBack}
        className="px-4 py-2 text-sm font-medium text-blue-700 bg-white border border-blue-300 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        Go Back
      </button>
    </div>
  </div>
);

/**
 * View tabs component
 */
const ViewTabs: React.FC<{
  activeTab: ViewType;
  availableViews: ViewType[];
  onTabChange: (tab: ViewType) => void;
  disabled?: boolean;
}> = ({ activeTab, availableViews, onTabChange, disabled = false }) => (
  <div className="border-b border-gray-200">
    <nav className="-mb-px flex space-x-8" aria-label="Tabs">
      {availableViews.map((view) => (
        <button
          key={view}
          onClick={() => !disabled && onTabChange(view)}
          disabled={disabled}
          className={`py-2 px-1 border-b-2 font-medium text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
            activeTab === view
              ? 'border-blue-500 text-blue-600'
              : disabled
              ? 'border-transparent text-gray-400 cursor-not-allowed'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          }`}
        >
          {view === 'side' ? 'Side View' : 'Rear View'}
        </button>
      ))}
    </nav>
  </div>
);

/**
 * Main ResultsPage component
 */
export const ResultsPage: React.FC<ResultsPageProps> = ({
  analysisId,
  userEmail,
  onBack,
  onNewAnalysis,
  onExport
}) => {
  // State
  const [activeTab, setActiveTab] = useState<ViewType>('side');
  
  // Hooks
  const analysisData = useAnalysisData({
    analysisId,
    userEmail,
    autoRefresh: true,
    onError: (error) => {
      console.error('Analysis data error:', error);
    }
  });

  const supabaseAnalysis = useSupabaseAnalysis({
    onError: (error) => {
      console.error('Supabase error:', error);
    }
  });

  // Derived state
  const availableViews = analysisData.analysis?.videos.map(v => v.viewType) || [];
  const currentVideo = analysisData.analysis?.videos.find(v => v.viewType === activeTab);
  
  // Effects
  useEffect(() => {
    // Set initial tab to first available view
    if (availableViews.length > 0 && !availableViews.includes(activeTab)) {
      setActiveTab(availableViews[0]);
    }
  }, [availableViews, activeTab]);

  // Handlers
  const handleExport = useCallback((format: 'pdf' | 'csv' | 'json') => {
    if (onExport) {
      onExport(format);
    } else {
      // Default export behavior
      console.log(`Exporting analysis ${analysisId} as ${format}`);
      // Implementation would go here
    }
  }, [analysisId, onExport]);

  const handleRetry = useCallback(() => {
    analysisData.refetch();
  }, [analysisData]);

  // Page header configuration
  const pageHeaderProps: PageHeaderProps = {
    title: 'Running Analysis Results',
    subtitle: analysisData.analysis ? `Analysis ID: ${analysisId}` : undefined,
    breadcrumbs: [
      { label: 'MaxWattz', onClick: onBack },
      { label: 'Analysis', onClick: onBack },
      { label: 'Results' }
    ],
    actions: [
      {
        label: 'Back',
        onClick: onBack || (() => {}),
        variant: 'secondary',
        icon: (
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
        )
      },
      {
        label: 'Export',
        onClick: () => handleExport('pdf'),
        variant: 'secondary'
      },
      {
        label: 'New Analysis',
        onClick: onNewAnalysis || (() => {}),
        variant: 'primary'
      }
    ]
  };

  // Render loading state
  if (analysisData.isLoading && !analysisData.analysis) {
    return (
      <div className="min-h-screen bg-gray-50">
        <PageHeader {...pageHeaderProps} />
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <LoadingState 
            message="Loading analysis results..."
            showCancel
            onCancel={onBack}
          />
        </div>
      </div>
    );
  }

  // Render error state
  if (analysisData.error && !analysisData.analysis) {
    return (
      <div className="min-h-screen bg-gray-50">
        <PageHeader {...pageHeaderProps} />
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <ErrorState 
            error={analysisData.error}
            showRetry
            onRetry={handleRetry}
            showSupport
          />
        </div>
      </div>
    );
  }

  // Render processing state
  if (analysisData.analysis?.status === 'processing' || analysisData.analysis?.status === 'pending') {
    return (
      <div className="min-h-screen bg-gray-50">
        <PageHeader {...pageHeaderProps} />
        <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
          <ProcessingStatus 
            analysis={analysisData.analysis}
            onBack={onBack || (() => {})}
          />
        </div>
      </div>
    );
  }

  // Render results
  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader {...pageHeaderProps} />
      
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {availableViews.length > 0 ? (
          <div className="space-y-6">
            {/* View Tabs */}
            <div className="bg-white rounded-lg shadow">
              <ViewTabs
                activeTab={activeTab}
                availableViews={availableViews}
                onTabChange={setActiveTab}
                disabled={analysisData.isLoading}
              />
              
              {/* Content */}
              <div className="p-6">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Video Player */}
                  <div className="lg:col-span-2">
                    {currentVideo && (
                      <div className="bg-gray-900 rounded-lg overflow-hidden">
                        <ProcessedVideoPlayerModal
                          videoUrl={currentVideo.originalVideoUrl}
                          poseDataUrl={currentVideo.poseDataUrl}
                          analysisType="running"
                          viewType={activeTab}
                          overlayStyle="medical"
                          userHeight={analysisData.analysis?.userConfig.height}
                          autoPlay={false}
                          controls={true}
                          loop={true}
                        />
                      </div>
                    )}
                  </div>
                  
                  {/* Metrics Panel */}
                  <div className="space-y-4">
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">
                        Analysis Metrics
                      </h3>
                      
                      {analysisData.analysis?.metrics && (
                        <div className="space-y-3">
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Overall Score</span>
                            <span className="font-medium text-lg">
                              {Math.round((analysisData.analysis.metrics.overallScore || 0) * 100)}%
                            </span>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Performance Level</span>
                            <span className="capitalize font-medium">
                              {analysisData.analysis.metrics.performanceLevel}
                            </span>
                          </div>
                          
                          <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-600">Injury Risk</span>
                            <span className={`capitalize font-medium ${
                              analysisData.analysis.metrics.injuryRisk === 'low' 
                                ? 'text-green-600' 
                                : analysisData.analysis.metrics.injuryRisk === 'moderate'
                                ? 'text-yellow-600'
                                : 'text-red-600'
                            }`}>
                              {analysisData.analysis.metrics.injuryRisk}
                            </span>
                          </div>
                          
                          {analysisData.analysis.metrics.sideViewMetrics && (
                            <>
                              <hr className="my-3" />
                              <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-600">Cadence</span>
                                  <span className="font-medium">
                                    {analysisData.analysis.metrics.sideViewMetrics.cadence.value} 
                                    <span className="text-xs text-gray-500 ml-1">
                                      {analysisData.analysis.metrics.sideViewMetrics.cadence.unit}
                                    </span>
                                  </span>
                                </div>
                                
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-600">Stride Length</span>
                                  <span className="font-medium">
                                    {analysisData.analysis.metrics.sideViewMetrics.strideLength.value.toFixed(2)} 
                                    <span className="text-xs text-gray-500 ml-1">
                                      {analysisData.analysis.metrics.sideViewMetrics.strideLength.unit}
                                    </span>
                                  </span>
                                </div>
                                
                                <div className="flex justify-between items-center">
                                  <span className="text-sm text-gray-600">Vertical Oscillation</span>
                                  <span className="font-medium">
                                    {analysisData.analysis.metrics.sideViewMetrics.verticalOscillation.value.toFixed(1)} 
                                    <span className="text-xs text-gray-500 ml-1">
                                      {analysisData.analysis.metrics.sideViewMetrics.verticalOscillation.unit}
                                    </span>
                                  </span>
                                </div>
                              </div>
                            </>
                          )}
                        </div>
                      )}
                    </div>
                    
                    {/* Analysis Info */}
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <h4 className="font-medium text-gray-900 mb-2">Analysis Details</h4>
                      <div className="space-y-2 text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Model Version:</span>
                          <span className="font-medium">{analysisData.analysis?.modelVersion}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Processing Time:</span>
                          <span className="font-medium">{analysisData.analysis?.processingTime}s</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Created:</span>
                          <span className="font-medium">
                            {analysisData.analysis?.createdAt && new Date(analysisData.analysis.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ) : (
          // No results available
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center py-8">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No analysis results</h3>
              <p className="mt-1 text-sm text-gray-500">
                No analysis results are available for this session yet.
              </p>
              <div className="mt-6">
                <button
                  onClick={handleRetry}
                  disabled={analysisData.isLoading}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                >
                  {analysisData.isLoading ? 'Refreshing...' : 'Refresh Results'}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};