/**
 * MetricsOverlay Component
 * 
 * Renders real-time metrics directly on the canvas overlay
 * Based on dev-app-smoothnet.html metrics display system
 */

import React, { RefObject, useCallback, useEffect } from 'react';

export interface OverlayMetrics {
  // Performance indicators
  fps: number;
  processingTime: number;
  
  // Current frame data
  frameNumber: number;
  timestamp: number;
  poseScore: number;
  invalidKeypoints?: number;
  
  // Running metrics (if available)
  strideLength?: number;
  stepHeight?: number;
  cadence?: number;
  verticalOscillation?: number;
  
  // Status indicators
  modalDataStatus: 'loaded' | 'loading' | 'error';
  coordinateValidation: 'valid' | 'invalid' | 'warning';
  smoothNetActive: boolean;
}

export interface MetricsOverlayProps {
  canvasRef: RefObject<HTMLCanvasElement | null>;
  metrics: OverlayMetrics | null;
  showDebugInfo?: boolean;
  showRunningMetrics?: boolean;
  showSmoothNetBranding?: boolean;
  isVisible?: boolean;
}

export const MetricsOverlay: React.FC<MetricsOverlayProps> = ({
  canvasRef,
  metrics,
  showDebugInfo = true,
  showRunningMetrics = true,
  showSmoothNetBranding = true,
  isVisible = true
}) => {

  /**
   * Draw performance info overlay (top-left)
   */
  const drawPerformanceInfo = useCallback((
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    metrics: OverlayMetrics
  ) => {
    if (!showDebugInfo) return;

    const boxWidth = 180;
    const boxHeight = 100;
    const padding = 10;
    const lineHeight = 16;

    // Background
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(padding, padding, boxWidth, boxHeight);
    
    // Border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    ctx.strokeRect(padding, padding, boxWidth, boxHeight);

    // Text styling
    ctx.fillStyle = '#00ff00';
    ctx.font = '12px monospace';
    
    let yPos = padding + 20;
    
    // FPS with color coding
    const fpsColor = metrics.fps >= 25 ? '#00ff00' : metrics.fps >= 15 ? '#ffaa00' : '#ff0000';
    ctx.fillStyle = fpsColor;
    ctx.fillText(`FPS: ${metrics.fps.toFixed(1)}`, padding + 10, yPos);
    
    yPos += lineHeight;
    ctx.fillStyle = '#ffffff';
    ctx.fillText(`Frame: ${metrics.frameNumber}`, padding + 10, yPos);
    
    yPos += lineHeight;
    ctx.fillText(`Time: ${metrics.timestamp.toFixed(2)}s`, padding + 10, yPos);
    
    yPos += lineHeight;
    // Pose score with color coding
    const scoreColor = metrics.poseScore >= 80 ? '#00ff00' : metrics.poseScore >= 60 ? '#ffaa00' : '#ff0000';
    ctx.fillStyle = scoreColor;
    ctx.fillText(`Score: ${metrics.poseScore.toFixed(0)}%`, padding + 10, yPos);
    
    yPos += lineHeight;
    ctx.fillStyle = '#ffffff';
    ctx.fillText(`Process: ${metrics.processingTime.toFixed(1)}ms`, padding + 10, yPos);

    // Invalid keypoints warning
    if (metrics.invalidKeypoints && metrics.invalidKeypoints > 0) {
      yPos += lineHeight;
      ctx.fillStyle = '#ff6600';
      ctx.fillText(`⚠️ ${metrics.invalidKeypoints} invalid`, padding + 10, yPos);
    }
  }, [showDebugInfo]);

  /**
   * Draw running metrics overlay (bottom-left)
   */
  const drawRunningMetrics = useCallback((
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    metrics: OverlayMetrics
  ) => {
    if (!showRunningMetrics || !metrics.strideLength) return;

    const boxWidth = 220;
    const boxHeight = 110;
    const padding = 10;
    const lineHeight = 16;
    const yStart = canvas.height - boxHeight - padding;

    // Background
    ctx.fillStyle = 'rgba(0, 100, 0, 0.8)';
    ctx.fillRect(padding, yStart, boxWidth, boxHeight);
    
    // Border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    ctx.strokeRect(padding, yStart, boxWidth, boxHeight);

    // Title
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 14px Arial';
    ctx.fillText('Running Metrics', padding + 10, yStart + 20);

    // Metrics
    ctx.font = '12px monospace';
    let yPos = yStart + 40;

    // Stride Length
    ctx.fillStyle = '#00ff88';
    ctx.fillText(`Stride: ${metrics.strideLength.toFixed(2)}m`, padding + 10, yPos);
    
    if (metrics.stepHeight !== undefined) {
      yPos += lineHeight;
      ctx.fillText(`Step Height: ${metrics.stepHeight.toFixed(1)}cm`, padding + 10, yPos);
    }
    
    if (metrics.cadence !== undefined) {
      yPos += lineHeight;
      ctx.fillText(`Cadence: ${metrics.cadence.toFixed(0)} spm`, padding + 10, yPos);
    }
    
    if (metrics.verticalOscillation !== undefined) {
      yPos += lineHeight;
      ctx.fillText(`V. Osc: ${metrics.verticalOscillation.toFixed(1)}cm`, padding + 10, yPos);
    }
  }, [showRunningMetrics]);

  /**
   * Draw status indicators (top-right)
   */
  const drawStatusIndicators = useCallback((
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement,
    metrics: OverlayMetrics
  ) => {
    const boxWidth = 160;
    const boxHeight = 70;
    const padding = 10;
    const lineHeight = 16;
    const xStart = canvas.width - boxWidth - padding;

    // Background with status color
    let bgColor;
    switch (metrics.modalDataStatus) {
      case 'loaded':
        bgColor = 'rgba(0, 100, 0, 0.8)';
        break;
      case 'loading':
        bgColor = 'rgba(0, 0, 100, 0.8)';
        break;
      case 'error':
        bgColor = 'rgba(100, 0, 0, 0.8)';
        break;
      default:
        bgColor = 'rgba(100, 100, 100, 0.8)';
    }
    
    ctx.fillStyle = bgColor;
    ctx.fillRect(xStart, padding, boxWidth, boxHeight);
    
    // Border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    ctx.strokeRect(xStart, padding, boxWidth, boxHeight);

    // Status text
    ctx.fillStyle = '#ffffff';
    ctx.font = '12px Arial';
    
    let yPos = padding + 20;
    
    // Modal data status
    const statusIcon = metrics.modalDataStatus === 'loaded' ? '✅' : 
                      metrics.modalDataStatus === 'loading' ? '⏳' : '❌';
    ctx.fillText(`${statusIcon} Modal Data`, xStart + 10, yPos);
    
    yPos += lineHeight;
    
    // Coordinate validation
    const coordIcon = metrics.coordinateValidation === 'valid' ? '✅' : 
                     metrics.coordinateValidation === 'warning' ? '⚠️' : '❌';
    ctx.fillText(`${coordIcon} Coordinates`, xStart + 10, yPos);
    
    yPos += lineHeight;
    
    // SmoothNet status
    if (metrics.smoothNetActive) {
      ctx.fillText('🔄 SmoothNet ON', xStart + 10, yPos);
    }
  }, []);

  /**
   * Draw SmoothNet branding (top-right, below status)
   */
  const drawSmoothNetBranding = useCallback((
    ctx: CanvasRenderingContext2D,
    canvas: HTMLCanvasElement
  ) => {
    if (!showSmoothNetBranding) return;

    const boxWidth = 210;
    const boxHeight = 30;
    const padding = 10;
    const xStart = canvas.width - boxWidth - padding;
    const yStart = 90; // Below status indicators

    // Background
    ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
    ctx.fillRect(xStart, yStart, boxWidth, boxHeight);
    
    // Border
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.lineWidth = 1;
    ctx.strokeRect(xStart, yStart, boxWidth, boxHeight);

    // Branding text
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText('🔄 SmoothNet 3DPW-SPIN-3D', xStart + 10, yStart + 20);
  }, [showSmoothNetBranding]);

  /**
   * Draw all overlay elements
   */
  const drawOverlay = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas || !metrics || !isVisible) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Save context state
    ctx.save();
    
    try {
      // Set text rendering properties
      ctx.textBaseline = 'top';
      ctx.textAlign = 'left';
      
      // Draw all overlay elements
      drawPerformanceInfo(ctx, canvas, metrics);
      drawRunningMetrics(ctx, canvas, metrics);
      drawStatusIndicators(ctx, canvas, metrics);
      drawSmoothNetBranding(ctx, canvas);
      
    } finally {
      // Restore context state
      ctx.restore();
    }
  }, [
    canvasRef, 
    metrics, 
    isVisible, 
    drawPerformanceInfo, 
    drawRunningMetrics, 
    drawStatusIndicators, 
    drawSmoothNetBranding
  ]);

  /**
   * Trigger overlay redraw when metrics change
   */
  useEffect(() => {
    if (metrics && isVisible) {
      // Use requestAnimationFrame for smooth updates
      requestAnimationFrame(drawOverlay);
    }
  }, [metrics, isVisible, drawOverlay]);

  // This component renders directly to canvas, no JSX needed
  return null;
};

export default MetricsOverlay;