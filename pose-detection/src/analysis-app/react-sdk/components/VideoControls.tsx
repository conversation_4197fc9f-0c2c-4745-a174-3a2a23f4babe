/**
 * VideoControls Component
 * 
 * Manual frame controls for precise video navigation
 * Fixed at 0.25x playback speed with no audio
 */

import React, { RefObject, useCallback, useEffect, useState } from 'react';
import './VideoControls.css';

export interface VideoControlsProps {
  videoRef: RefObject<HTMLVideoElement | null>;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  currentFrame: number;
  totalFrames: number;
  fps: number;
  onPlay: () => void;
  onPause: () => void;
  onSeek: (time: number) => void;
  onStepForward: () => void;
  onStepBackward: () => void;
  disabled?: boolean;
  className?: string;
}

export const VideoControls: React.FC<VideoControlsProps> = ({
  videoRef,
  isPlaying,
  currentTime,
  duration,
  currentFrame,
  totalFrames,
  fps,
  onPlay,
  onPause,
  onSeek,
  onStepForward,
  onStepBackward,
  disabled = false,
  className = ''
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragTime, setDragTime] = useState(0);

  /**
   * Format time as MM:SS
   */
  const formatTime = useCallback((time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  }, []);

  /**
   * Calculate progress percentage
   */
  const progressPercent = useCallback((): number => {
    if (!duration || duration === 0) return 0;
    const time = isDragging ? dragTime : currentTime;
    return Math.min(100, (time / duration) * 100);
  }, [currentTime, duration, isDragging, dragTime]);

  /**
   * Handle scrub bar interaction
   */
  const handleScrubClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (disabled || !duration) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = Math.max(0, Math.min(duration, percentage * duration));
    
    onSeek(newTime);
  }, [disabled, duration, onSeek]);

  /**
   * Handle scrub bar mouse down
   */
  const handleScrubMouseDown = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    if (disabled || !duration) return;
    
    setIsDragging(true);
    handleScrubClick(event);
  }, [disabled, duration, handleScrubClick]);

  /**
   * Handle scrub bar mouse move
   */
  const handleScrubMouseMove = useCallback((event: MouseEvent) => {
    if (!isDragging || disabled || !duration) return;

    const scrubBar = document.querySelector('.scrub-bar') as HTMLElement;
    if (!scrubBar) return;

    const rect = scrubBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = Math.max(0, Math.min(1, clickX / rect.width));
    const newTime = percentage * duration;
    
    setDragTime(newTime);
  }, [isDragging, disabled, duration]);

  /**
   * Handle scrub bar mouse up
   */
  const handleScrubMouseUp = useCallback(() => {
    if (!isDragging) return;
    
    setIsDragging(false);
    if (dragTime !== currentTime) {
      onSeek(dragTime);
    }
  }, [isDragging, dragTime, currentTime, onSeek]);

  /**
   * Keyboard shortcuts
   */
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled) return;

    // Only handle if no input is focused
    if (document.activeElement?.tagName === 'INPUT' || 
        document.activeElement?.tagName === 'TEXTAREA') {
      return;
    }

    switch (event.key) {
      case ' ':
      case 'k':
        event.preventDefault();
        if (isPlaying) {
          onPause();
        } else {
          onPlay();
        }
        break;
      case 'ArrowLeft':
        event.preventDefault();
        onStepBackward();
        break;
      case 'ArrowRight':
        event.preventDefault();
        onStepForward();
        break;
      case 'Home':
        event.preventDefault();
        onSeek(0);
        break;
      case 'End':
        event.preventDefault();
        if (duration) onSeek(duration);
        break;
    }
  }, [disabled, isPlaying, onPlay, onPause, onStepForward, onStepBackward, onSeek, duration]);

  /**
   * Setup event listeners
   */
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleScrubMouseMove);
      document.addEventListener('mouseup', handleScrubMouseUp);
      
      return () => {
        document.removeEventListener('mousemove', handleScrubMouseMove);
        document.removeEventListener('mouseup', handleScrubMouseUp);
      };
    }
  }, [isDragging, handleScrubMouseMove, handleScrubMouseUp]);

  /**
   * Setup keyboard listeners
   */
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return (
    <div className={`video-controls ${className} ${disabled ? 'disabled' : ''}`}>
      {/* Main Control Bar */}
      <div className="control-bar">
        {/* Scrub Bar */}
        <div 
          className="scrub-container"
          onMouseDown={handleScrubMouseDown}
        >
          <div className="scrub-bar">
            <div 
              className="scrub-progress"
              style={{ width: `${progressPercent()}%` }}
            />
            <div 
              className="scrub-handle"
              style={{ left: `${progressPercent()}%` }}
            />
          </div>
        </div>

        {/* Controls Row */}
        <div className="controls-row">
          {/* Playback Controls */}
          <div className="playback-controls">
            <button
              className="control-btn step-backward"
              onClick={onStepBackward}
              disabled={disabled}
              title="Step Backward (←)"
              aria-label="Step backward one frame"
            >
              ⏮️
            </button>

            <button
              className="control-btn play-pause"
              onClick={isPlaying ? onPause : onPlay}
              disabled={disabled}
              title={isPlaying ? "Pause (Space)" : "Play (Space)"}
              aria-label={isPlaying ? "Pause video" : "Play video"}
            >
              {isPlaying ? '⏸️' : '▶️'}
            </button>

            <button
              className="control-btn step-forward"
              onClick={onStepForward}
              disabled={disabled}
              title="Step Forward (→)"
              aria-label="Step forward one frame"
            >
              ⏭️
            </button>
          </div>

          {/* Time Display */}
          <div className="time-display">
            <span className="current-time">
              {formatTime(isDragging ? dragTime : currentTime)}
            </span>
            <span className="time-separator">/</span>
            <span className="total-time">
              {formatTime(duration)}
            </span>
          </div>

          {/* Frame Info */}
          <div className="frame-info">
            <span className="frame-display">
              Frame: <span className="frame-number">{currentFrame}</span>
              <span className="frame-separator">/</span>
              <span className="total-frames">{totalFrames}</span>
            </span>
          </div>

          {/* Speed Indicator */}
          <div className="speed-info">
            <span className="speed-display">0.25x</span>
            <span className="fps-display">{fps} fps</span>
          </div>
        </div>
      </div>

      {/* Keyboard Shortcuts Help */}
      <div className="keyboard-shortcuts" title="Keyboard Shortcuts">
        <small>
          Space: Play/Pause • ← →: Step Frame • Home/End: Go to Start/End
        </small>
      </div>
    </div>
  );
};

export default VideoControls;