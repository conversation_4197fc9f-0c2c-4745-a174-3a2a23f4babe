/**
 * ProcessedVideoPlayer Component
 * 
 * Displays video with pose overlay from pre-processed Modal/SmoothNet results
 * Ported from dev-app-smoothnet.html with React patterns
 */

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { ProcessedVideoPlayerProps } from '../types/components';
import { ProcessedPoseData } from '../types/pose';
import { BLAZEPOSE_CONNECTIONS } from '../types/pose';

/**
 * Configuration for keypoint filtering
 */
const SKIP_KEYPOINTS = [
  1, 2, 3, 4, 5, 6,        // eyes
  9, 10,                   // mouth  
  17, 18, 19, 20, 21, 22   // fingers
];

/**
 * Keypoint confidence thresholds
 */
const MIN_CONFIDENCE = 0.3;
const HIGH_CONFIDENCE = 0.8;
const MEDIUM_CONFIDENCE = 0.5;

export const ProcessedVideoPlayer: React.FC<ProcessedVideoPlayerProps> = ({
  videoUrl,
  resultsUrl,
  analysisType = 'running',
  viewType,
  overlayStyle = 'medical',
  userHeight,
  onPoseData,
  onMetrics,
  autoPlay = false,
  controls = true,
  loop = false
}) => {
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const lastFrameTimeRef = useRef<number>(0);
  
  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [poseData, setPoseData] = useState<ProcessedPoseData[]>([]);
  const [videoMetadata, setVideoMetadata] = useState<{
    width: number;
    height: number;
    fps: number;
    duration: number;
  } | null>(null);
  const [currentFrame, setCurrentFrame] = useState<ProcessedPoseData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  /**
   * Load pose data from results URL
   */
  useEffect(() => {
    const loadPoseData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(resultsUrl);
        if (!response.ok) {
          throw new Error(`Failed to load pose data: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Validate data structure
        if (!data.poses || !Array.isArray(data.poses)) {
          throw new Error('Invalid pose data format');
        }
        
        setPoseData(data.poses);
        setVideoMetadata({
          width: data.videoWidth ?? 1920,
          height: data.videoHeight ?? 1080,
          fps: data.fps ?? 30,
          duration: data.duration ?? 0
        });
        
        setError(null);
      } catch (err) {
        console.error('Error loading pose data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load pose data');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPoseData();
  }, [resultsUrl]);
  
  /**
   * Synchronize canvas with video dimensions
   */
  const syncCanvasWithVideo = useCallback(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    
    if (!video || !canvas) return;
    
    // Get the actual displayed size of the video element
    const videoRect = video.getBoundingClientRect();
    
    // Set canvas to match video display size exactly
    canvas.width = videoRect.width;
    canvas.height = videoRect.height;
    
    // Set canvas CSS size to match video exactly
    canvas.style.width = `${videoRect.width}px`;
    canvas.style.height = `${videoRect.height}px`;
  }, []);
  
  /**
   * Find pose frame for current video time
   */
  const findFrameForTime = useCallback((currentTime: number): ProcessedPoseData | null => {
    if (poseData.length === 0) return null;
    
    // Binary search for closest frame
    let left = 0;
    let right = poseData.length - 1;
    
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const frameTime = poseData[mid].timestamp;
      
      if (Math.abs(frameTime - currentTime) < 0.033) { // ~30fps tolerance
        return poseData[mid];
      }
      
      if (frameTime < currentTime) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
    
    // Return closest frame
    const closestIdx = Math.max(0, Math.min(left, poseData.length - 1));
    return poseData[closestIdx];
  }, [poseData]);
  
  /**
   * Draw keypoint with style
   */
  const drawKeypoint = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    score: number,
    index: number,
    isEdgeCase?: boolean
  ) => {
    // Skip filtered keypoints
    if (SKIP_KEYPOINTS.includes(index)) return;
    
    // Color based on confidence
    let color = '#ff0000'; // red (low confidence)
    if (isEdgeCase) {
      color = '#ff8800'; // orange (edge case)
    } else if (score > HIGH_CONFIDENCE) {
      color = '#00ff00'; // green (high confidence)
    } else if (score > MEDIUM_CONFIDENCE) {
      color = '#ffaa00'; // orange (medium confidence)
    }
    
    // Draw keypoint with outline
    ctx.fillStyle = color;
    ctx.strokeStyle = isEdgeCase ? 'red' : 'white';
    ctx.lineWidth = isEdgeCase ? 3 : 2;
    ctx.beginPath();
    ctx.arc(x, y, isEdgeCase ? 8 : 6, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
    
    // Draw keypoint index for debug mode
    if (overlayStyle === 'debug') {
      ctx.fillStyle = 'white';
      ctx.font = '10px Arial';
      ctx.fillText(index.toString(), x + 6, y - 6);
    }
  }, [overlayStyle]);
  
  /**
   * Draw skeleton connections
   */
  const drawSkeleton = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    // Define connections (filtered for running analysis)
    const connections = [
      // Head (nose to ears only)
      [0, 7], [0, 8],
      // Torso
      [11, 12], [11, 23], [12, 24], [23, 24],
      // Arms (to wrists only)
      [11, 13], [13, 15], [12, 14], [14, 16],
      // Legs (full leg structure)
      [23, 25], [25, 27], [27, 29], [27, 31],
      [24, 26], [26, 28], [28, 30], [28, 32]
    ];
    
    // Set style based on overlay mode
    switch (overlayStyle) {
      case 'medical':
        ctx.strokeStyle = 'rgba(0, 255, 255, 0.9)'; // cyan
        ctx.lineWidth = 3;
        break;
      case 'athletic':
        ctx.strokeStyle = 'rgba(255, 165, 0, 0.9)'; // orange
        ctx.lineWidth = 4;
        break;
      case 'minimal':
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'; // white
        ctx.lineWidth = 2;
        break;
      case 'debug':
        ctx.strokeStyle = 'rgba(0, 255, 0, 0.9)'; // green
        ctx.lineWidth = 2;
        break;
    }
    
    // Draw connections
    connections.forEach(([i, j]) => {
      const kp1 = keypoints[i];
      const kp2 = keypoints[j];
      
      if (kp1 && kp2 && kp1.score > MIN_CONFIDENCE && kp2.score > MIN_CONFIDENCE) {
        ctx.beginPath();
        ctx.moveTo(kp1.x * scaleX, kp1.y * scaleY);
        ctx.lineTo(kp2.x * scaleX, kp2.y * scaleY);
        ctx.stroke();
      }
    });
  }, [overlayStyle]);
  
  /**
   * Draw bounding box around detected person
   */
  const drawBoundingBox = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    const validKeypoints = keypoints.filter(kp => kp.score > MIN_CONFIDENCE);
    if (validKeypoints.length === 0) return;
    
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    
    validKeypoints.forEach(kp => {
      const x = kp.x * scaleX;
      const y = kp.y * scaleY;
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    });
    
    // Add padding
    const padding = 20;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;
    
    // Draw bounding box
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
    ctx.lineWidth = 2;
    ctx.strokeRect(minX, minY, maxX - minX, maxY - minY);
  }, []);
  
  /**
   * Process and render frame
   */
  const processFrame = useCallback(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    
    if (!video || !canvas || !ctx || !videoMetadata) return;
    
    // Clear canvas and draw video frame
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Find pose data for current time
    const frameData = findFrameForTime(video.currentTime);
    
    if (frameData && frameData.keypoints) {
      setCurrentFrame(frameData);
      
      // Calculate scale factors
      const scaleX = canvas.width / videoMetadata.width;
      const scaleY = canvas.height / videoMetadata.height;
      
      // Draw skeleton first (behind keypoints)
      drawSkeleton(ctx, frameData.keypoints, scaleX, scaleY);
      
      // Draw keypoints
      frameData.keypoints.forEach((keypoint, idx) => {
        if (keypoint && keypoint.score && keypoint.score > MIN_CONFIDENCE) {
          const x = keypoint.x * scaleX;
          const y = keypoint.y * scaleY;
          drawKeypoint(ctx, x, y, keypoint.score, idx);
        }
      });
      
      // Draw bounding box
      if (overlayStyle !== 'minimal') {
        drawBoundingBox(ctx, frameData.keypoints, scaleX, scaleY);
      }
      
      // Draw overlay info
      if (overlayStyle === 'debug' || overlayStyle === 'medical') {
        const validKeypoints = frameData.keypoints.filter((kp, idx) => 
          kp && kp.score && kp.score > MIN_CONFIDENCE && !SKIP_KEYPOINTS.includes(idx)
        );
        
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(10, 10, 200, 80);
        ctx.fillStyle = 'white';
        ctx.font = '14px Arial';
        ctx.fillText(`Frame: ${frameData.frameNumber}`, 20, 30);
        ctx.fillText(`Time: ${frameData.timestamp.toFixed(2)}s`, 20, 50);
        ctx.fillText(`Keypoints: ${validKeypoints.length}`, 20, 70);
      }
      
      // Notify parent component
      if (onPoseData) {
        onPoseData(frameData);
      }
    }
  }, [videoMetadata, findFrameForTime, drawKeypoint, drawSkeleton, drawBoundingBox, overlayStyle, onPoseData]);
  
  /**
   * Animation loop
   */
  const animate = useCallback(() => {
    const now = performance.now();
    
    // Limit to ~30fps
    if (now - lastFrameTimeRef.current >= 33) {
      processFrame();
      lastFrameTimeRef.current = now;
    }
    
    if (isPlaying) {
      animationRef.current = requestAnimationFrame(animate);
    }
  }, [isPlaying, processFrame]);
  
  /**
   * Handle video play
   */
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    animate();
  }, [animate]);
  
  /**
   * Handle video pause
   */
  const handlePause = useCallback(() => {
    setIsPlaying(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  }, []);
  
  /**
   * Setup video event listeners
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;
    
    const handleLoadedMetadata = () => {
      syncCanvasWithVideo();
      // Set playback rate to 0.25x for pose synchronization
      video.playbackRate = 0.25;
    };
    
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('seeked', processFrame);
    
    // Handle window resize
    window.addEventListener('resize', syncCanvasWithVideo);
    
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('seeked', processFrame);
      window.removeEventListener('resize', syncCanvasWithVideo);
    };
  }, [handlePlay, handlePause, processFrame, syncCanvasWithVideo]);
  
  /**
   * Cleanup animation on unmount
   */
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);
  
  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pose data...</p>
        </div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg">
        <div className="text-center text-red-600">
          <p className="font-semibold">Error loading pose data</p>
          <p className="text-sm mt-2">{error}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="relative inline-block w-full">
      <video
        ref={videoRef}
        src={videoUrl}
        className="w-full h-auto block rounded-lg"
        controls={controls}
        autoPlay={autoPlay}
        loop={loop}
        playsInline
      />
      <canvas
        ref={canvasRef}
        className="absolute top-0 left-0 pointer-events-none rounded-lg"
      />
      
      {/* Overlay info */}
      {currentFrame && overlayStyle !== 'minimal' && (
        <div className="absolute top-4 right-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-sm">
          <div>View: {viewType}</div>
          <div>Model: {currentFrame.modelType}</div>
          {currentFrame.smoothed && <div>✓ SmoothNet</div>}
        </div>
      )}
    </div>
  );
};