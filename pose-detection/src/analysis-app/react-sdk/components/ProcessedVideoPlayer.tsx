/**
 * ProcessedVideoPlayer Component
 * 
 * Displays video with pose overlay from pre-processed Modal/SmoothNet results
 * Ported from dev-app-smoothnet.html with React patterns
 */

import React, { useRef, useEffect, useState, useCallback, useMemo } from 'react';
import { ProcessedVideoPlayerProps } from '../types/components';
import { ProcessedPoseData } from '../types/pose';
import { BLAZEPOSE_CONNECTIONS } from '../types/pose';
import './ProcessedVideoPlayer.css';

/**
 * Configuration for keypoint filtering
 */
const SKIP_KEYPOINTS = [
  1, 2, 3, 4, 5, 6,        // eyes
  9, 10,                   // mouth  
  17, 18, 19, 20, 21, 22   // fingers
];

/**
 * Keypoint confidence thresholds
 */
const MIN_CONFIDENCE = 0.3;
const HIGH_CONFIDENCE = 0.8;
const MEDIUM_CONFIDENCE = 0.5;

export const ProcessedVideoPlayer: React.FC<ProcessedVideoPlayerProps> = ({
  videoUrl,
  resultsUrl,
  analysisType = 'running',
  viewType,
  overlayStyle = 'medical',
  userHeight,
  onPoseData,
  onMetrics,
  autoPlay = false,
  controls = true,
  loop = false
}) => {
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const lastFrameTimeRef = useRef<number>(0);
  
  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [poseData, setPoseData] = useState<ProcessedPoseData[]>([]);
  const [videoMetadata, setVideoMetadata] = useState<{
    width: number;
    height: number;
    fps: number;
    duration: number;
  } | null>(null);
  const [currentFrame, setCurrentFrame] = useState<ProcessedPoseData | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isPortraitVideo, setIsPortraitVideo] = useState<boolean>(false);
  
  /**
   * Load pose data from results URL
   */
  useEffect(() => {
    const loadPoseData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(resultsUrl);
        if (!response.ok) {
          throw new Error(`Failed to load pose data: ${response.status}`);
        }
        
        const data = await response.json();
        
        // Validate data structure
        if (!data.poses || !Array.isArray(data.poses)) {
          throw new Error('Invalid pose data format');
        }
        
        setPoseData(data.poses);
        
        // Extract video metadata for coordinate scaling
        const metadata = {
          width: data.videoWidth ?? 1920,
          height: data.videoHeight ?? 1080,
          fps: data.fps ?? 30,
          duration: data.duration ?? 0
        };
        
        setVideoMetadata(metadata);
        
        // Detect if this is a portrait video (9:16 aspect ratio)
        const aspectRatio = metadata.width / metadata.height;
        const isPortrait = aspectRatio < 1 && Math.abs(aspectRatio - (9/16)) < 0.1;
        setIsPortraitVideo(isPortrait);
        
        console.log('Video metadata loaded:', {
          ...metadata,
          aspectRatio: aspectRatio.toFixed(3),
          isPortrait,
          expectedPortrait: '9:16 = 0.5625'
        });
        
        setError(null);
      } catch (err) {
        console.error('Error loading pose data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load pose data');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPoseData();
  }, [resultsUrl]);
  
  /**
   * Synchronize canvas with video dimensions
   * Critical: Must match dev-app-smoothnet.html implementation exactly
   */
  const syncCanvasWithVideo = useCallback(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    
    if (!video || !canvas) return;
    
    // Get the actual displayed size of the video element (not the video's native resolution)
    const videoRect = video.getBoundingClientRect();
    
    // Set canvas internal resolution to match video display size exactly
    canvas.width = videoRect.width;
    canvas.height = videoRect.height;
    
    // Set canvas CSS size to match video display size exactly
    canvas.style.width = `${videoRect.width}px`;
    canvas.style.height = `${videoRect.height}px`;
    
    // Position canvas to exactly overlay the video
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.pointerEvents = 'none';
    
    // Debug log for dimension tracking
    if (video.videoWidth > 0 && videoMetadata) {
      console.log('Video/Canvas sync:', {
        displaySize: { width: videoRect.width, height: videoRect.height },
        videoResolution: { width: video.videoWidth, height: video.videoHeight },
        modalResolution: { width: videoMetadata.width, height: videoMetadata.height },
        scaleX: videoRect.width / videoMetadata.width,
        scaleY: videoRect.height / videoMetadata.height
      });
    }
  }, [videoMetadata]);
  
  /**
   * Filter out invalid keypoints and handle edge cases
   * Matches dev-app-smoothnet.html lines 774-809
   */
  const getValidKeypoints = useCallback((keypoints: any[]) => {
    return keypoints.map((kp, idx) => {
      // If both X and Y are 0, this is likely invalid data
      if (kp.x === 0.0 && kp.y === 0.0) {
        return {
          ...kp,
          x: 0.0,
          y: 0.0,
          score: 0.0, // Mark as invalid by setting score to 0
          isInvalid: true
        };
      }
      
      // If only X is 0 (but Y is valid), this might be edge detection issue
      if (kp.x === 0.0 && kp.y > 0.0) {
        return {
          ...kp,
          score: Math.max(0.1, kp.score * 0.5), // Reduce confidence
          isEdgeCase: true
        };
      }
      
      // If only Y is 0 (but X is valid), this might be edge detection issue  
      if (kp.y === 0.0 && kp.x > 0.0) {
        return {
          ...kp,
          score: Math.max(0.1, kp.score * 0.5), // Reduce confidence
          isEdgeCase: true
        };
      }
      
      // Valid coordinate
      return kp;
    });
  }, []);

  /**
   * Find pose frame for current video time
   */
  const findFrameForTime = useCallback((currentTime: number): ProcessedPoseData | null => {
    if (poseData.length === 0) return null;
    
    // Binary search for closest frame
    let left = 0;
    let right = poseData.length - 1;
    
    while (left <= right) {
      const mid = Math.floor((left + right) / 2);
      const frameTime = poseData[mid].timestamp;
      
      if (Math.abs(frameTime - currentTime) < 0.033) { // ~30fps tolerance
        return poseData[mid];
      }
      
      if (frameTime < currentTime) {
        left = mid + 1;
      } else {
        right = mid - 1;
      }
    }
    
    // Return closest frame
    const closestIdx = Math.max(0, Math.min(left, poseData.length - 1));
    return poseData[closestIdx];
  }, [poseData]);
  
  /**
   * Draw keypoint with style
   */
  const drawKeypoint = useCallback((
    ctx: CanvasRenderingContext2D,
    x: number,
    y: number,
    score: number,
    index: number,
    isEdgeCase?: boolean
  ) => {
    // Skip filtered keypoints
    if (SKIP_KEYPOINTS.includes(index)) return;
    
    // Color based on confidence
    let color = '#ff0000'; // red (low confidence)
    if (isEdgeCase) {
      color = '#ff8800'; // orange (edge case)
    } else if (score > HIGH_CONFIDENCE) {
      color = '#00ff00'; // green (high confidence)
    } else if (score > MEDIUM_CONFIDENCE) {
      color = '#ffaa00'; // orange (medium confidence)
    }
    
    // Draw keypoint with outline
    ctx.fillStyle = color;
    ctx.strokeStyle = isEdgeCase ? 'red' : 'white';
    ctx.lineWidth = isEdgeCase ? 3 : 2;
    ctx.beginPath();
    ctx.arc(x, y, isEdgeCase ? 8 : 6, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
    
    // Draw keypoint index for debug mode
    if (overlayStyle === 'debug') {
      ctx.fillStyle = 'white';
      ctx.font = '10px Arial';
      ctx.fillText(index.toString(), x + 6, y - 6);
    }
  }, [overlayStyle]);
  
  /**
   * Draw skeleton connections
   */
  const drawSkeleton = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    // Define connections (filtered for running analysis)
    const connections = [
      // Head (nose to ears only)
      [0, 7], [0, 8],
      // Torso
      [11, 12], [11, 23], [12, 24], [23, 24],
      // Arms (to wrists only)
      [11, 13], [13, 15], [12, 14], [14, 16],
      // Legs (full leg structure)
      [23, 25], [25, 27], [27, 29], [27, 31],
      [24, 26], [26, 28], [28, 30], [28, 32]
    ];
    
    // Set style based on overlay mode
    switch (overlayStyle) {
      case 'medical':
        ctx.strokeStyle = 'rgba(0, 255, 255, 0.9)'; // cyan
        ctx.lineWidth = 3;
        break;
      case 'athletic':
        ctx.strokeStyle = 'rgba(255, 165, 0, 0.9)'; // orange
        ctx.lineWidth = 4;
        break;
      case 'minimal':
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.7)'; // white
        ctx.lineWidth = 2;
        break;
      case 'debug':
        ctx.strokeStyle = 'rgba(0, 255, 0, 0.9)'; // green
        ctx.lineWidth = 2;
        break;
    }
    
    // Draw connections
    connections.forEach(([i, j]) => {
      const kp1 = keypoints[i];
      const kp2 = keypoints[j];
      
      if (kp1 && kp2 && kp1.score > MIN_CONFIDENCE && kp2.score > MIN_CONFIDENCE) {
        ctx.beginPath();
        ctx.moveTo(kp1.x * scaleX, kp1.y * scaleY);
        ctx.lineTo(kp2.x * scaleX, kp2.y * scaleY);
        ctx.stroke();
      }
    });
  }, [overlayStyle]);
  
  /**
   * Draw bounding box around detected person
   */
  const drawBoundingBox = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    const validKeypoints = keypoints.filter(kp => kp.score > MIN_CONFIDENCE);
    if (validKeypoints.length === 0) return;
    
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    
    validKeypoints.forEach(kp => {
      const x = kp.x * scaleX;
      const y = kp.y * scaleY;
      minX = Math.min(minX, x);
      minY = Math.min(minY, y);
      maxX = Math.max(maxX, x);
      maxY = Math.max(maxY, y);
    });
    
    // Add padding
    const padding = 20;
    minX -= padding;
    minY -= padding;
    maxX += padding;
    maxY += padding;
    
    // Draw bounding box
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
    ctx.lineWidth = 2;
    ctx.strokeRect(minX, minY, maxX - minX, maxY - minY);
  }, []);
  
  /**
   * Process and render frame
   */
  const processFrame = useCallback(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    
    if (!video || !canvas || !ctx || !videoMetadata) return;
    
    // Clear canvas and draw video frame
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    // Find pose data for current time
    const frameData = findFrameForTime(video.currentTime);
    
    if (frameData && frameData.keypoints) {
      setCurrentFrame(frameData);
      
      // Critical: Process keypoints to handle invalid coordinates and edge cases
      const validatedKeypoints = getValidKeypoints(frameData.keypoints);
      
      // Critical: Scale coordinates from original video resolution to displayed canvas size
      // This matches the dev-app-smoothnet.html implementation lines 1213-1215
      const scaleX = canvas.width / videoMetadata.width;
      const scaleY = canvas.height / videoMetadata.height;
      
      // Validate dimensions match to avoid coordinate errors
      if (video.videoWidth !== videoMetadata.width || video.videoHeight !== videoMetadata.height) {
        // Draw dimension mismatch warning
        ctx.fillStyle = 'rgba(255, 0, 0, 0.9)';
        ctx.fillRect(0, 0, canvas.width, 100);
        ctx.fillStyle = 'white';
        ctx.font = 'bold 16px Arial';
        ctx.fillText('❌ DIMENSION MISMATCH WARNING', 10, 25);
        ctx.font = '14px Arial';
        ctx.fillText(`Video: ${video.videoWidth}×${video.videoHeight} vs Modal: ${videoMetadata.width}×${videoMetadata.height}`, 10, 45);
        ctx.fillText('Coordinates will be scaled incorrectly', 10, 65);
        ctx.fillText('Use matching video file for accurate results', 10, 85);
      }
      
      // Draw skeleton first (behind keypoints) using validated keypoints
      drawSkeleton(ctx, validatedKeypoints, scaleX, scaleY);
      
      // Draw keypoints with proper coordinate scaling and validation
      validatedKeypoints.forEach((keypoint, idx) => {
        if (keypoint && keypoint.score && keypoint.score > MIN_CONFIDENCE && !keypoint.isInvalid) {
          const x = keypoint.x * scaleX;
          const y = keypoint.y * scaleY;
          drawKeypoint(ctx, x, y, keypoint.score, idx, keypoint.isEdgeCase);
        }
      });
      
      // Draw bounding box using validated keypoints
      if (overlayStyle !== 'minimal') {
        drawBoundingBox(ctx, validatedKeypoints, scaleX, scaleY);
      }
      
      // Show invalid coordinate warning if any found
      const invalidCount = validatedKeypoints.filter(kp => kp.isInvalid).length;
      const edgeCaseCount = validatedKeypoints.filter(kp => kp.isEdgeCase).length;
      
      if (invalidCount > 0 || edgeCaseCount > 0) {
        ctx.fillStyle = 'rgba(255, 165, 0, 0.8)';
        ctx.fillRect(10, canvas.height - 50, 300, 40);
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.fillText(`⚠️ Invalid coords: ${invalidCount}, Edge cases: ${edgeCaseCount}`, 15, canvas.height - 30);
      }
      
      // Draw overlay info
      if (overlayStyle === 'debug' || overlayStyle === 'medical') {
        const validKeypoints = validatedKeypoints.filter((kp, idx) => 
          kp && kp.score && kp.score > MIN_CONFIDENCE && !kp.isInvalid && !SKIP_KEYPOINTS.includes(idx)
        );
        
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(10, 10, 200, 100);
        ctx.fillStyle = 'white';
        ctx.font = '14px Arial';
        ctx.fillText(`Frame: ${frameData.frameNumber}`, 20, 30);
        ctx.fillText(`Time: ${frameData.timestamp.toFixed(2)}s`, 20, 50);
        ctx.fillText(`Valid KPs: ${validKeypoints.length}`, 20, 70);
        ctx.fillText(`Edge cases: ${edgeCaseCount}`, 20, 90);
      }
      
      // Notify parent component
      if (onPoseData) {
        onPoseData(frameData);
      }
    }
  }, [videoMetadata, findFrameForTime, getValidKeypoints, drawKeypoint, drawSkeleton, drawBoundingBox, overlayStyle, onPoseData]);
  
  /**
   * Animation loop
   */
  const animate = useCallback(() => {
    const now = performance.now();
    
    // Limit to ~30fps
    if (now - lastFrameTimeRef.current >= 33) {
      processFrame();
      lastFrameTimeRef.current = now;
    }
    
    if (isPlaying) {
      animationRef.current = requestAnimationFrame(animate);
    }
  }, [isPlaying, processFrame]);
  
  /**
   * Handle video play
   */
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    animate();
  }, [animate]);
  
  /**
   * Handle video pause
   */
  const handlePause = useCallback(() => {
    setIsPlaying(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  }, []);
  
  /**
   * Setup video event listeners
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;
    
    const handleLoadedMetadata = () => {
      syncCanvasWithVideo();
      // Critical: Set playback rate to 0.25x for pose synchronization (matches dev-app-smoothnet.html line 936)
      video.playbackRate = 0.25;
      
      // Validate video dimensions against Modal data to prevent coordinate errors
      if (videoMetadata) {
        const videoDimensions = `${video.videoWidth}×${video.videoHeight}`;
        const modalDimensions = `${videoMetadata.width}×${videoMetadata.height}`;
        
        if (video.videoWidth !== videoMetadata.width || video.videoHeight !== videoMetadata.height) {
          console.error('Video dimensions mismatch', {
            video: videoDimensions,
            modal: modalDimensions,
            issue: 'Coordinate scaling will be incorrect'
          });
        } else {
          console.log('✅ Video dimensions match Modal data', { dimensions: videoDimensions });
        }
      }
    };
    
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);
    video.addEventListener('seeked', processFrame);
    
    // Handle window resize and video container changes
    const handleResize = () => {
      // Debounce resize events to avoid excessive calls
      setTimeout(() => {
        syncCanvasWithVideo();
      }, 10);
    };
    
    window.addEventListener('resize', handleResize);
    
    // Use ResizeObserver to watch for container size changes (more accurate than window resize)
    let resizeObserver: ResizeObserver | null = null;
    if (window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        handleResize();
      });
      resizeObserver.observe(video);
    }
    
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
      video.removeEventListener('seeked', processFrame);
      window.removeEventListener('resize', handleResize);
      
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [handlePlay, handlePause, processFrame, syncCanvasWithVideo]);
  
  /**
   * Cleanup animation on unmount
   */
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);
  
  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading pose data...</p>
        </div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64 bg-red-50 rounded-lg">
        <div className="text-center text-red-600">
          <p className="font-semibold">Error loading pose data</p>
          <p className="text-sm mt-2">{error}</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="video-player-container w-full max-w-full">
      {/* Video wrapper optimized for 9:16 portrait videos (1080x1920, 2160x3840) */}
      <div 
        className="video-wrapper relative w-full mx-auto" 
        style={{ 
          // Responsive sizing for portrait videos
          // Mobile: use 90vw to prevent overflow
          // Tablet: use reasonable width for portrait video
          // Desktop: limit to ~400px for side-by-side layout with metrics
          width: 'clamp(280px, 90vw, 400px)',
          maxWidth: '100%',
          aspectRatio: '9/16', // Enforce 9:16 portrait aspect ratio
          // Add subtle shadow and border for video container
          borderRadius: '12px',
          overflow: 'hidden',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
        }}
      >
        <video
          ref={videoRef}
          src={videoUrl}
          className="video-element w-full h-full block rounded-lg object-contain"
          style={{ 
            // Critical: Video must fill container while maintaining aspect ratio
            // object-contain ensures video fits within container without distortion
            width: '100%',
            height: '100%',
            aspectRatio: '9/16' // Enforce 9:16 for portrait videos
          }}
          controls={controls}
          autoPlay={autoPlay}
          loop={loop}
          playsInline
        />
        <canvas
          ref={canvasRef}
          className="pose-overlay absolute top-0 left-0 pointer-events-none rounded-lg"
          style={{
            // Canvas positioning and sizing is handled programmatically in syncCanvasWithVideo
            // This ensures perfect overlay alignment regardless of video display size
          }}
        />
        
        {/* Overlay info */}
        {currentFrame && overlayStyle !== 'minimal' && (
          <div className="absolute top-4 right-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-sm z-10">
            <div>View: {viewType}</div>
            <div>Model: {currentFrame.modelType}</div>
            {currentFrame.smoothed && <div>✓ SmoothNet</div>}
            <div className="text-xs opacity-75 mt-1">
              {videoMetadata && `${videoMetadata.width}×${videoMetadata.height}`} @ 0.25x
            </div>
          </div>
        )}
      </div>
      
      
      {/* Video dimension validation info */}
      {videoMetadata && (
        <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm">
          <div className="flex justify-between items-center mb-2">
            <span className="font-medium">Video: {videoMetadata.width}×{videoMetadata.height}</span>
            <span className={`font-semibold px-2 py-1 rounded text-xs ${
              isPortraitVideo
                ? 'bg-green-100 text-green-700' 
                : 'bg-amber-100 text-amber-700'
            }`}>
              {isPortraitVideo
                ? '✅ Portrait Optimized' 
                : '⚠️ Check Orientation'}
            </span>
          </div>
          
          <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
            <div>
              <span className="text-gray-500">Aspect Ratio:</span><br/>
              <span className="font-mono">
                {(videoMetadata.width / videoMetadata.height).toFixed(3)}
                {Math.abs((videoMetadata.width / videoMetadata.height) - (9/16)) < 0.1 
                  ? ' (9:16 ✓)' 
                  : ' (Expected 9:16)'}
              </span>
            </div>
            <div>
              <span className="text-gray-500">FPS:</span><br/>
              <span className="font-mono">{videoMetadata.fps.toFixed(1)}</span>
            </div>
          </div>
          
          {!isPortraitVideo && (
            <div className="mt-2 p-2 bg-amber-50 border border-amber-200 rounded text-xs text-amber-800">
              <strong>⚠️ Recommendation:</strong> Use portrait videos (9:16 ratio) like 1080x1920 or 2160x3840 for optimal running analysis.
            </div>
          )}
        </div>
      )}
    </div>
  );
};