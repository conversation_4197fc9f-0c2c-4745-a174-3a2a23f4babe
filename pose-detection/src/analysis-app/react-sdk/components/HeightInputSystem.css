/**
 * HeightInputSystem Component Styles
 * 
 * Styling for the height input component matching the reference implementation
 * with responsive design and visual feedback states.
 */

.height-input-system {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.height-input-system.calibrating {
  border-color: #2196f3;
  background: #e3f2fd;
}

.height-input-system.calibrated {
  border-color: #4caf50;
  background: #e8f5e9;
}

.height-input-system.failed {
  border-color: #f44336;
  background: #ffebee;
}

.height-input-system h3 {
  margin: 0 0 15px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Input Wrapper */
.height-input-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.height-input-wrapper label {
  font-weight: 500;
  color: #555;
  margin-right: 10px;
}

/* Input Groups */
.imperial-inputs,
.metric-inputs {
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Height Inputs */
.height-input {
  width: 60px;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  text-align: center;
  transition: all 0.2s ease;
}

.height-input:focus {
  outline: none;
  border-color: #2196f3;
  box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.height-input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.6;
}

.height-input.error {
  border-color: #f44336;
}

/* Specific input widths */
.feet-input {
  width: 50px;
}

.inches-input {
  width: 50px;
}

.cm-input {
  width: 70px;
}

/* Unit Labels */
.unit-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

/* Unit Toggle Button */
.unit-toggle {
  background: #fff;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-left: 10px;
}

.unit-toggle:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #2196f3;
  color: #2196f3;
}

.unit-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Height Info Section */
.height-info {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #e0e0e0;
}

.height-display {
  display: block;
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.height-input-system.set .height-display {
  color: #4caf50;
  font-weight: 500;
}

/* Validation Error */
.validation-error {
  color: #f44336;
  font-size: 13px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 5px;
}

/* Calibration Status */
.calibration-status {
  margin-top: 8px;
  font-size: 13px;
}

.status-calibrating {
  color: #2196f3;
  animation: pulse 1.5s ease-in-out infinite;
}

.status-success {
  color: #4caf50;
  font-weight: 500;
}

.status-failed {
  color: #f44336;
}

/* Instructions */
.height-instructions {
  margin-top: 8px;
  font-size: 13px;
  color: #888;
  font-style: italic;
}

/* Animations */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .height-input-system {
    padding: 15px;
  }

  .height-input-wrapper {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .height-input-wrapper label {
    margin-bottom: 5px;
  }

  .unit-toggle {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }

  .imperial-inputs,
  .metric-inputs {
    width: 100%;
    justify-content: flex-start;
  }

  .height-input {
    flex: 1;
    max-width: 80px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .height-input-system {
    background: #1e1e1e;
    border-color: #333;
  }

  .height-input-system h3 {
    color: #e0e0e0;
  }

  .height-input-wrapper label {
    color: #bbb;
  }

  .height-input {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .height-input:focus {
    border-color: #64b5f6;
    box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.3);
  }

  .unit-toggle {
    background: #2a2a2a;
    border-color: #444;
    color: #e0e0e0;
  }

  .unit-toggle:hover:not(:disabled) {
    background: #333;
    border-color: #64b5f6;
    color: #64b5f6;
  }

  .height-info {
    border-top-color: #444;
  }

  .height-display {
    color: #bbb;
  }

  .height-input-system.set .height-display {
    color: #81c784;
  }

  .height-input-system.calibrating {
    background: #1e3a5f;
    border-color: #2196f3;
  }

  .height-input-system.calibrated {
    background: #1b3a1b;
    border-color: #4caf50;
  }

  .height-input-system.failed {
    background: #3a1b1b;
    border-color: #f44336;
  }
}