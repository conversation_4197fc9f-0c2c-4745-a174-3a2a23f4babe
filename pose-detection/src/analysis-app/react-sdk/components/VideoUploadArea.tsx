/**
 * VideoUploadArea Component
 * 
 * Drag & drop video file upload interface for raw video processing pipeline
 * Based on dev-app-smoothnet.html upload functionality (lines 869-890)
 */

import React, { useRef, useState, useCallback, DragEvent, ChangeEvent } from 'react';
import './VideoUploadArea.css';

export interface VideoFile {
  file: File;
  url: string; // Object URL for preview
  name: string;
  size: number;
  type: string;
  duration?: number;
  dimensions?: { width: number; height: number };
}

export interface FileValidationResult {
  isValid: boolean;
  error?: string;
  warnings?: string[];
}

interface VideoUploadAreaProps {
  onFileSelect: (videoFile: VideoFile) => void;
  onValidationError: (error: string) => void;
  acceptedTypes?: string[];
  maxSizeGB?: number;
  disabled?: boolean;
  className?: string;
}

/**
 * Supported video formats based on reference implementation
 */
const DEFAULT_ACCEPTED_TYPES = [
  'video/mp4',
  'video/mov', 
  'video/quicktime',
  'video/avi',
  'video/webm',
  'video/x-msvideo'
];

const DEFAULT_MAX_SIZE_GB = 2; // 2GB limit as per PRD

export const VideoUploadArea: React.FC<VideoUploadAreaProps> = ({
  onFileSelect,
  onValidationError,
  acceptedTypes = DEFAULT_ACCEPTED_TYPES,
  maxSizeGB = DEFAULT_MAX_SIZE_GB,
  disabled = false,
  className = ''
}) => {
  // Refs
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // State
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  /**
   * Validate uploaded video file
   */
  const validateFile = useCallback((file: File): FileValidationResult => {
    // Check file type
    if (!acceptedTypes.includes(file.type)) {
      const supportedFormats = acceptedTypes.map(type => type.split('/')[1]).join(', ');
      return {
        isValid: false,
        error: `Unsupported file format. Supported formats: ${supportedFormats}`
      };
    }
    
    // Check file size (convert GB to bytes)
    const maxSizeBytes = maxSizeGB * 1024 * 1024 * 1024;
    if (file.size > maxSizeBytes) {
      return {
        isValid: false,
        error: `File too large. Maximum size: ${maxSizeGB}GB (${(file.size / (1024 * 1024 * 1024)).toFixed(2)}GB)`
      };
    }
    
    // Size warnings
    const warnings: string[] = [];
    const sizeMB = file.size / (1024 * 1024);
    
    if (sizeMB > 500) {
      warnings.push('Large file - processing may take several minutes');
    }
    
    if (sizeMB < 1) {
      warnings.push('Very small file - ensure it contains sufficient video content');
    }
    
    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }, [acceptedTypes, maxSizeGB]);
  
  /**
   * Extract video metadata using HTML5 Video API
   */
  const extractVideoMetadata = useCallback((file: File): Promise<VideoFile> => {
    return new Promise((resolve, reject) => {
      const url = URL.createObjectURL(file);
      const video = document.createElement('video');
      
      video.onloadedmetadata = () => {
        const videoFile: VideoFile = {
          file,
          url,
          name: file.name,
          size: file.size,
          type: file.type,
          duration: video.duration,
          dimensions: {
            width: video.videoWidth,
            height: video.videoHeight
          }
        };
        
        console.log('Video metadata extracted:', {
          name: file.name,
          size: `${(file.size / (1024 * 1024)).toFixed(1)}MB`,
          duration: `${video.duration.toFixed(1)}s`,
          dimensions: `${video.videoWidth}×${video.videoHeight}`,
          type: file.type
        });
        
        resolve(videoFile);
      };
      
      video.onerror = () => {
        URL.revokeObjectURL(url);
        reject(new Error('Failed to load video metadata. File may be corrupted.'));
      };
      
      video.src = url;
    });
  }, []);
  
  /**
   * Process selected file
   */
  const handleFile = useCallback(async (file: File) => {
    if (!file || isProcessing || disabled) return;
    
    try {
      setIsProcessing(true);
      
      // Validate file
      const validation = validateFile(file);
      if (!validation.isValid) {
        onValidationError(validation.error!);
        return;
      }
      
      // Extract metadata
      const videoFile = await extractVideoMetadata(file);
      
      // Show warnings if any
      if (validation.warnings) {
        console.warn('File validation warnings:', validation.warnings);
      }
      
      // Pass to parent
      onFileSelect(videoFile);
      
    } catch (error) {
      console.error('File processing error:', error);
      onValidationError(error instanceof Error ? error.message : 'Failed to process video file');
    } finally {
      setIsProcessing(false);
    }
  }, [isProcessing, disabled, validateFile, extractVideoMetadata, onFileSelect, onValidationError]);
  
  /**
   * Handle click to open file dialog
   */
  const handleClick = useCallback(() => {
    if (disabled || isProcessing) return;
    fileInputRef.current?.click();
  }, [disabled, isProcessing]);
  
  /**
   * Handle file input change
   */
  const handleFileInputChange = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFile(file);
    }
    // Reset input so same file can be selected again
    e.target.value = '';
  }, [handleFile]);
  
  /**
   * Handle drag over
   */
  const handleDragOver = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    if (!disabled && !isProcessing) {
      setIsDragOver(true);
    }
  }, [disabled, isProcessing]);
  
  /**
   * Handle drag leave
   */
  const handleDragLeave = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    // Only remove dragover if we're actually leaving the upload area
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setIsDragOver(false);
    }
  }, []);
  
  /**
   * Handle drop
   */
  const handleDrop = useCallback((e: DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
    
    if (disabled || isProcessing) return;
    
    const files = Array.from(e.dataTransfer.files);
    const videoFile = files.find(file => file.type.startsWith('video/'));
    
    if (!videoFile) {
      onValidationError('Please drop a video file');
      return;
    }
    
    if (files.length > 1) {
      console.warn('Multiple files dropped, using first video file:', videoFile.name);
    }
    
    handleFile(videoFile);
  }, [disabled, isProcessing, handleFile, onValidationError]);
  
  // Dynamic class names based on state
  const uploadAreaClasses = [
    'video-upload-area',
    isDragOver && 'dragover',
    disabled && 'disabled',
    isProcessing && 'processing',
    className
  ].filter(Boolean).join(' ');
  
  // Supported formats display
  const formatsList = acceptedTypes
    .map(type => type.split('/')[1].toUpperCase())
    .join(', ');
  
  return (
    <div
      className={uploadAreaClasses}
      onClick={handleClick}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-label="Upload video file"
      aria-disabled={disabled}
    >
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={handleFileInputChange}
        style={{ display: 'none' }}
        disabled={disabled}
      />
      
      <div className="upload-content">
        {isProcessing ? (
          <>
            <div className="upload-spinner"></div>
            <p className="upload-text">Processing video file...</p>
            <p className="upload-subtext">Extracting metadata...</p>
          </>
        ) : (
          <>
            <div className="upload-icon">📹</div>
            <p className="upload-text">
              {isDragOver ? 'Drop video file here' : 'Drop video file here or click to select'}
            </p>
            <p className="upload-subtext">
              Supports {formatsList} • Max size: {maxSizeGB}GB
            </p>
            {disabled && (
              <p className="upload-disabled">Upload currently disabled</p>
            )}
          </>
        )}
      </div>
    </div>
  );
};