/**
 * StatusDisplay Component
 * 
 * Critical component for displaying processing status and user feedback.
 * Based on reference implementation from dev-app-smoothnet.html (lines 105-190).
 * 
 * Features:
 * - Real-time processing status updates
 * - Error state display with retry actions
 * - User instruction messaging
 * - Progress feedback with percentages
 * - Connection status indicators
 * - Dismissible notifications
 */

import React, { useCallback } from 'react';
import './StatusDisplay.css';

export type ProcessingStage = 
  | 'idle' 
  | 'loading' 
  | 'processing' 
  | 'complete' 
  | 'error'
  | 'ready'
  | 'calibrating'
  | 'warning';

export type ErrorType = 
  | 'network' 
  | 'validation' 
  | 'processing' 
  | 'modal' 
  | 'calibration'
  | 'video'
  | 'unknown';

export interface ProcessingError {
  type: ErrorType;
  message: string;
  actionable: boolean;
  details?: string;
}

export interface ProcessingStatus {
  stage: ProcessingStage;
  message: string;
  progress?: number;
  error?: ProcessingError;
  timestamp?: Date;
  isDismissible?: boolean;
}

interface StatusDisplayProps {
  status: ProcessingStatus;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

export const StatusDisplay: React.FC<StatusDisplayProps> = ({
  status,
  onRetry,
  onDismiss,
  showDetails = false,
  className = ''
}) => {
  const { stage, message, progress, error, isDismissible } = status;

  // Get appropriate icon for status
  const getStatusIcon = useCallback(() => {
    switch (stage) {
      case 'loading':
      case 'processing':
        return '⏳';
      case 'calibrating':
        return '🔄';
      case 'complete':
      case 'ready':
        return '✅';
      case 'error':
        return '❌';
      case 'warning':
        return '⚠️';
      case 'idle':
      default:
        return '📋';
    }
  }, [stage]);

  // Get CSS class for status type
  const getStatusClass = useCallback(() => {
    return `status-display ${stage} ${className}`;
  }, [stage, className]);

  // Handle retry button click
  const handleRetry = useCallback(() => {
    if (onRetry) {
      onRetry();
    }
  }, [onRetry]);

  // Handle dismiss button click
  const handleDismiss = useCallback(() => {
    if (onDismiss) {
      onDismiss();
    }
  }, [onDismiss]);

  // Format error details for display
  const getErrorDetails = useCallback(() => {
    if (!error) return null;

    const errorTypeLabels: Record<ErrorType, string> = {
      network: 'Network Error',
      validation: 'Validation Error',
      processing: 'Processing Error',
      modal: 'Modal Processing Error',
      calibration: 'Calibration Error',
      video: 'Video Error',
      unknown: 'Unknown Error'
    };

    return {
      typeLabel: errorTypeLabels[error.type],
      canRetry: error.actionable
    };
  }, [error]);

  const errorDetails = getErrorDetails();

  return (
    <div className={getStatusClass()} role="status" aria-live="polite">
      <div className="status-content">
        <div className="status-main">
          <span className="status-icon" aria-hidden="true">
            {getStatusIcon()}
          </span>
          <span className="status-message">{message}</span>
          
          {/* Progress bar */}
          {progress !== undefined && (
            <div className="status-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${Math.max(0, Math.min(100, progress))}%` }}
                  aria-valuenow={progress}
                  aria-valuemin={0}
                  aria-valuemax={100}
                  role="progressbar"
                />
              </div>
              <span className="progress-text">{Math.round(progress)}%</span>
            </div>
          )}
        </div>

        {/* Action buttons */}
        <div className="status-actions">
          {/* Retry button for actionable errors */}
          {stage === 'error' && errorDetails?.canRetry && onRetry && (
            <button 
              onClick={handleRetry}
              className="status-action-btn retry-btn"
              type="button"
              aria-label="Retry operation"
            >
              🔄 Retry
            </button>
          )}

          {/* Dismiss button for dismissible statuses */}
          {isDismissible && onDismiss && (
            <button 
              onClick={handleDismiss}
              className="status-action-btn dismiss-btn"
              type="button"
              aria-label="Dismiss notification"
            >
              ✕
            </button>
          )}
        </div>
      </div>

      {/* Error details section */}
      {stage === 'error' && error && showDetails && (
        <div className="status-details">
          <div className="error-details">
            <div className="error-type">
              <strong>{errorDetails?.typeLabel}</strong>
            </div>
            {error.details && (
              <div className="error-description">
                {error.details}
              </div>
            )}
            <div className="error-message">
              {error.message}
            </div>
          </div>
        </div>
      )}

      {/* Spinning animation for loading states */}
      {(stage === 'loading' || stage === 'processing' || stage === 'calibrating') && (
        <div className="status-spinner" aria-hidden="true" />
      )}
    </div>
  );
};

export default StatusDisplay;