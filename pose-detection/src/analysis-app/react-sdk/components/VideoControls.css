/**
 * VideoControls Styles
 * Professional video player controls with frame-accurate navigation
 */

.video-controls {
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 30%, rgba(0, 0, 0, 0.8) 100%);
  color: white;
  padding: 12px 16px 8px;
  border-radius: 0 0 8px 8px;
  user-select: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.video-controls.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* Control Bar */
.control-bar {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Scrub Bar */
.scrub-container {
  cursor: pointer;
  padding: 8px 0;
}

.scrub-bar {
  position: relative;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  transition: height 0.2s ease;
}

.scrub-container:hover .scrub-bar {
  height: 6px;
}

.scrub-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: #3b82f6;
  border-radius: 2px;
  transition: background 0.2s ease;
}

.scrub-container:hover .scrub-progress {
  background: #60a5fa;
}

.scrub-handle {
  position: absolute;
  top: 50%;
  width: 12px;
  height: 12px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.scrub-container:hover .scrub-handle {
  opacity: 1;
}

/* Controls Row */
.controls-row {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 32px;
}

/* Playback Controls */
.playback-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  height: 32px;
}

.control-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.control-btn:active:not(:disabled) {
  transform: translateY(0);
  background: rgba(255, 255, 255, 0.3);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.play-pause {
  background: #3b82f6;
  border-color: #2563eb;
}

.control-btn.play-pause:hover:not(:disabled) {
  background: #60a5fa;
  border-color: #3b82f6;
}

/* Time Display */
.time-display {
  display: flex;
  align-items: center;
  gap: 4px;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  min-width: 80px;
}

.time-separator {
  color: rgba(255, 255, 255, 0.6);
}

/* Frame Info */
.frame-info {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  min-width: 100px;
}

.frame-number {
  font-weight: 600;
  color: #60a5fa;
}

.frame-separator {
  margin: 0 4px;
  color: rgba(255, 255, 255, 0.5);
}

/* Speed Info */
.speed-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-left: auto;
}

.speed-display {
  font-weight: 600;
  color: #fbbf24;
}

.fps-display {
  color: rgba(255, 255, 255, 0.6);
}

/* Keyboard Shortcuts */
.keyboard-shortcuts {
  margin-top: 4px;
  padding-top: 6px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  text-align: center;
}

.keyboard-shortcuts small {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-controls {
    padding: 10px 12px 6px;
  }
  
  .controls-row {
    gap: 12px;
  }
  
  .control-btn {
    min-width: 32px;
    height: 28px;
    font-size: 14px;
    padding: 4px 8px;
  }
  
  .time-display,
  .frame-info {
    font-size: 11px;
  }
  
  .speed-info {
    font-size: 10px;
  }
  
  .keyboard-shortcuts {
    display: none;
  }
}

@media (max-width: 480px) {
  .video-controls {
    padding: 8px 10px 4px;
  }
  
  .controls-row {
    gap: 8px;
  }
  
  .frame-info,
  .time-display {
    min-width: auto;
  }
  
  /* Stack on very small screens */
  .controls-row {
    flex-wrap: wrap;
  }
  
  .playback-controls {
    order: 1;
    flex: 1;
    justify-content: center;
  }
  
  .time-display,
  .frame-info,
  .speed-info {
    order: 2;
    font-size: 10px;
  }
}