/**
 * HeightInputSystem Component
 * 
 * Critical component for user height input with imperial/metric unit support.
 * Based on reference implementation from dev-app-smoothnet.html (lines 320-339).
 * 
 * Features:
 * - Imperial (feet/inches) and metric (cm) input modes
 * - Real-time validation and conversion
 * - Visual feedback for valid/invalid ranges
 * - Integration with CoordinateScaler for calibration
 * - Persistent state management
 */

import React, { useState, useEffect, useCallback } from 'react';
import { UserProfile } from '../utils/CoordinateScaler';
import { cmToInches, inchesToCm, formatHeight } from '../utils';
import './HeightInputSystem.css';

interface HeightInputSystemProps {
  userProfile: UserProfile;
  onProfileUpdate: (updates: Partial<UserProfile>) => void;
  isCalibrating: boolean;
  calibrationStatus: 'pending' | 'success' | 'failed';
  disabled?: boolean;
  className?: string;
}

export const HeightInputSystem: React.FC<HeightInputSystemProps> = ({
  userProfile,
  onProfileUpdate,
  isCalibrating,
  calibrationStatus,
  disabled = false,
  className = ''
}) => {
  // Local state for input values
  const [feet, setFeet] = useState<string>('');
  const [inches, setInches] = useState<string>('');
  const [cm, setCm] = useState<string>('');
  const [displayUnit, setDisplayUnit] = useState<'imperial' | 'metric'>('imperial');
  const [validationError, setValidationError] = useState<string>('');

  // Initialize inputs from userProfile on mount
  useEffect(() => {
    const heightValue = userProfile.height.unit === 'cm' ? userProfile.height.value : inchesToCm(userProfile.height.value);
    
    if (heightValue > 0) {
      // Convert to display values
      if (displayUnit === 'imperial') {
        const totalInches = cmToInches(heightValue);
        const feetValue = Math.floor(totalInches / 12);
        const inchesValue = Math.round(totalInches % 12);
        setFeet(feetValue.toString());
        setInches(inchesValue.toString());
      } else {
        setCm(Math.round(heightValue).toString());
      }
    }
  }, [userProfile.height, displayUnit]);

  // Validation and update handler
  const validateAndUpdateHeight = useCallback(() => {
    let heightInCm: number;
    let isValid = true;
    let error = '';

    if (displayUnit === 'imperial') {
      const feetVal = parseInt(feet) || 0;
      const inchesVal = parseInt(inches) || 0;

      // Validate imperial range (3'0" to 8'0")
      if (feetVal < 3 || feetVal > 8) {
        isValid = false;
        error = 'Height must be between 3 and 8 feet';
      } else if (inchesVal < 0 || inchesVal > 11) {
        isValid = false;
        error = 'Inches must be between 0 and 11';
      } else if (feetVal === 3 && inchesVal === 0) {
        // Minimum height check (3'0" = 36 inches)
        isValid = true;
      } else if (feetVal === 8 && inchesVal > 0) {
        isValid = false;
        error = 'Maximum height is 8 feet';
      }

      // Convert to cm
      const totalInches = feetVal * 12 + inchesVal;
      heightInCm = inchesToCm(totalInches);
    } else {
      // Metric validation (90-250 cm)
      const cmVal = parseInt(cm) || 0;
      
      if (cmVal < 90 || cmVal > 250) {
        isValid = false;
        error = 'Height must be between 90 and 250 cm';
      }
      
      heightInCm = cmVal;
    }

    setValidationError(isValid ? '' : error);

    if (isValid && heightInCm > 0) {
      onProfileUpdate({ 
        height: {
          value: heightInCm,
          unit: 'cm'
        }
      });
    }

    return isValid;
  }, [feet, inches, cm, displayUnit, onProfileUpdate]);

  // Handle unit toggle
  const toggleUnits = useCallback(() => {
    const newUnit = displayUnit === 'imperial' ? 'metric' : 'imperial';
    setDisplayUnit(newUnit);
    
    // Convert existing values
    const heightValue = userProfile.height.unit === 'cm' ? userProfile.height.value : inchesToCm(userProfile.height.value);
    
    if (heightValue > 0) {
      if (newUnit === 'metric') {
        setCm(Math.round(heightValue).toString());
      } else {
        const totalInches = cmToInches(heightValue);
        const feetValue = Math.floor(totalInches / 12);
        const inchesValue = Math.round(totalInches % 12);
        setFeet(feetValue.toString());
        setInches(inchesValue.toString());
      }
    }
  }, [displayUnit, userProfile.height, onProfileUpdate]);

  // Handle Enter key
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      validateAndUpdateHeight();
    }
  };

  // Format display text
  const getHeightDisplayText = () => {
    if (userProfile.height.value === 0) {
      return 'Height: Not set';
    }

    const heightInCm = userProfile.height.unit === 'cm' 
      ? userProfile.height.value 
      : inchesToCm(userProfile.height.value);
      
    const primary = formatHeight(heightInCm, displayUnit === 'imperial' ? 'inches' : 'cm');
    const secondary = formatHeight(heightInCm, displayUnit === 'imperial' ? 'cm' : 'inches');
    
    return `Height: ${primary} (${secondary})`;
  };

  // Get status class for visual feedback
  const getStatusClass = () => {
    if (validationError) return 'error';
    if (calibrationStatus === 'success') return 'calibrated';
    if (calibrationStatus === 'failed') return 'failed';
    if (isCalibrating) return 'calibrating';
    if (userProfile.height.value > 0) return 'set';
    return '';
  };

  return (
    <div className={`height-input-system ${className} ${getStatusClass()}`}>
      <h3>Runner Information</h3>
      
      <div className="height-input-wrapper">
        <label>Height:</label>
        
        {/* Imperial Inputs */}
        {displayUnit === 'imperial' && (
          <div className="imperial-inputs">
            <input
              type="number"
              value={feet}
              onChange={(e) => setFeet(e.target.value)}
              onBlur={validateAndUpdateHeight}
              onKeyPress={handleKeyPress}
              min="3"
              max="8"
              placeholder="5"
              disabled={disabled}
              className="height-input feet-input"
              aria-label="Height in feet"
            />
            <span className="unit-label">ft</span>
            <input
              type="number"
              value={inches}
              onChange={(e) => setInches(e.target.value)}
              onBlur={validateAndUpdateHeight}
              onKeyPress={handleKeyPress}
              min="0"
              max="11"
              placeholder="10"
              disabled={disabled}
              className="height-input inches-input"
              aria-label="Height in inches"
            />
            <span className="unit-label">in</span>
          </div>
        )}
        
        {/* Metric Input */}
        {displayUnit === 'metric' && (
          <div className="metric-inputs">
            <input
              type="number"
              value={cm}
              onChange={(e) => setCm(e.target.value)}
              onBlur={validateAndUpdateHeight}
              onKeyPress={handleKeyPress}
              min="90"
              max="250"
              placeholder="178"
              disabled={disabled}
              className="height-input cm-input"
              aria-label="Height in centimeters"
            />
            <span className="unit-label">cm</span>
          </div>
        )}
        
        <button
          onClick={toggleUnits}
          disabled={disabled}
          className="unit-toggle"
          aria-label={`Switch to ${displayUnit === 'imperial' ? 'metric' : 'imperial'} units`}
        >
          Switch to {displayUnit === 'imperial' ? 'Metric' : 'Imperial'}
        </button>
      </div>
      
      {/* Height Display and Status */}
      <div className="height-info">
        <small className="height-display">{getHeightDisplayText()}</small>
        
        {validationError && (
          <div className="validation-error" role="alert">
            ⚠️ {validationError}
          </div>
        )}
        
        {/* Calibration Status */}
        {userProfile.height.value > 0 && (
          <div className="calibration-status">
            {isCalibrating && <span className="status-calibrating">🔄 Calibrating...</span>}
            {calibrationStatus === 'success' && (
              <span className="status-success">✅ Calibrated</span>
            )}
            {calibrationStatus === 'failed' && (
              <span className="status-failed">❌ Calibration failed - ensure person is standing upright</span>
            )}
          </div>
        )}
        
        {/* Instructions */}
        {userProfile.height.value === 0 && !validationError && (
          <div className="height-instructions">
            <small>Enter your height to enable distance measurements</small>
          </div>
        )}
      </div>
    </div>
  );
};

export default HeightInputSystem;