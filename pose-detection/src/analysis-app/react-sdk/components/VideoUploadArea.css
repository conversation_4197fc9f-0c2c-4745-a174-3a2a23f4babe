/**
 * VideoUploadArea Styles
 * Based on dev-app-smoothnet.html upload area styling
 */

.video-upload-area {
  /* Base styling matching reference */
  border: 2px dashed #007bff;
  border-radius: 10px;
  padding: 40px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-upload-area:hover {
  background: #f0f8ff;
  border-color: #0056b3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.video-upload-area.dragover {
  background: #e7f3ff;
  border-color: #0056b3;
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.2);
}

.video-upload-area.disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: #f8f9fa;
  border-color: #dee2e6;
}

.video-upload-area.disabled:hover {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: none;
  box-shadow: none;
}

.video-upload-area.processing {
  cursor: default;
  border-color: #28a745;
  background: #f8fff9;
}

/* Upload content container */
.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  width: 100%;
}

/* Upload icon */
.upload-icon {
  font-size: 48px;
  margin-bottom: 8px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* Upload text styling */
.upload-text {
  font-size: 18px;
  font-weight: 500;
  color: #333333;
  margin: 0;
  line-height: 1.4;
}

.upload-subtext {
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.3;
}

.upload-disabled {
  font-size: 12px;
  color: #dc3545;
  margin: 4px 0 0 0;
  font-weight: 500;
}

/* Processing spinner */
.upload-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #28a745;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 768px) {
  .video-upload-area {
    padding: 30px 20px;
    margin: 0 10px;
  }
  
  .upload-icon {
    font-size: 40px;
  }
  
  .upload-text {
    font-size: 16px;
  }
  
  .upload-subtext {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .video-upload-area {
    padding: 25px 15px;
    min-height: 100px;
  }
  
  .upload-icon {
    font-size: 36px;
  }
  
  .upload-text {
    font-size: 15px;
  }
  
  .upload-subtext {
    font-size: 12px;
  }
}

/* Focus styles for accessibility */
.video-upload-area:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

.video-upload-area:focus:not(:focus-visible) {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .video-upload-area {
    border-width: 3px;
  }
  
  .upload-text {
    font-weight: 600;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .video-upload-area {
    transition: none;
  }
  
  .video-upload-area:hover,
  .video-upload-area.dragover {
    transform: none;
  }
  
  .upload-spinner {
    animation: none;
    border: 3px solid #28a745;
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  .video-upload-area {
    background: #1a1a1a;
    border-color: #4a90e2;
    color: #ffffff;
  }
  
  .video-upload-area:hover {
    background: #2a2a2a;
  }
  
  .video-upload-area.dragover {
    background: #2a3a4a;
  }
  
  .upload-text {
    color: #ffffff;
  }
  
  .upload-subtext {
    color: #cccccc;
  }
}