/**
 * StatusDisplay Component Styles
 * 
 * Styling for the status display component matching the reference implementation
 * with visual feedback states and responsive design.
 */

.status-display {
  padding: 12px 16px;
  margin: 10px 0;
  border-radius: 8px;
  text-align: left;
  border: 1px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  min-height: 48px;
  display: flex;
  align-items: center;
}

/* Status Content Layout */
.status-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  gap: 16px;
}

.status-main {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.status-icon {
  font-size: 18px;
  line-height: 1;
}

.status-message {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  flex: 1;
}

/* Progress Bar */
.status-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.progress-bar {
  width: 80px;
  height: 6px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: currentColor;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.progress-text {
  font-size: 12px;
  font-weight: 600;
  min-width: 32px;
  text-align: right;
}

/* Action Buttons */
.status-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.status-action-btn {
  background: transparent;
  border: 1px solid currentColor;
  color: inherit;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.status-action-btn:hover {
  background: currentColor;
  color: white;
}

.retry-btn {
  min-width: 70px;
}

.dismiss-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

/* Status Type Styles */

/* Loading State */
.status-display.loading {
  background: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

/* Processing State */
.status-display.processing {
  background: #e3f2fd;
  color: #1565c0;
  border-color: #90caf9;
}

/* Calibrating State */
.status-display.calibrating {
  background: #e8f5e9;
  color: #2e7d32;
  border-color: #a5d6a7;
}

/* Ready State */
.status-display.ready {
  background: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

/* Complete State */
.status-display.complete {
  background: #d4edda;
  color: #155724;
  border-color: #c3e6cb;
}

/* Error State */
.status-display.error {
  background: #f8d7da;
  color: #721c24;
  border-color: #f1aeb5;
}

/* Warning State */
.status-display.warning {
  background: #fff3cd;
  color: #856404;
  border-color: #ffeaa7;
}

/* Idle State */
.status-display.idle {
  background: #f8f9fa;
  color: #495057;
  border-color: #dee2e6;
}

/* Error Details */
.status-details {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  width: 100%;
}

.error-details {
  font-size: 13px;
  line-height: 1.4;
}

.error-type {
  margin-bottom: 4px;
}

.error-description {
  margin-bottom: 6px;
  color: rgba(114, 28, 36, 0.8);
  font-style: italic;
}

.error-message {
  font-family: monospace;
  background: rgba(0, 0, 0, 0.05);
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* Loading Spinner */
.status-spinner {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: translateY(-50%) rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .status-display {
    padding: 10px 12px;
    min-height: 40px;
  }

  .status-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .status-main {
    width: 100%;
  }

  .status-actions {
    align-self: flex-end;
  }

  .status-progress {
    min-width: 100px;
  }

  .progress-bar {
    width: 60px;
  }

  .status-icon {
    font-size: 16px;
  }

  .status-message {
    font-size: 13px;
  }

  .status-spinner {
    right: 12px;
    width: 14px;
    height: 14px;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .status-display,
  .progress-fill,
  .status-action-btn {
    transition: none;
  }

  .status-spinner {
    animation: none;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .status-display.loading {
    background: #3e2723;
    color: #ffcc02;
    border-color: #5d4037;
  }

  .status-display.processing {
    background: #0d47a1;
    color: #90caf9;
    border-color: #1976d2;
  }

  .status-display.calibrating {
    background: #1b5e20;
    color: #a5d6a7;
    border-color: #388e3c;
  }

  .status-display.ready,
  .status-display.complete {
    background: #1b5e20;
    color: #c8e6c9;
    border-color: #4caf50;
  }

  .status-display.error {
    background: #b71c1c;
    color: #ffcdd2;
    border-color: #f44336;
  }

  .status-display.warning {
    background: #e65100;
    color: #ffcc02;
    border-color: #ff9800;
  }

  .status-display.idle {
    background: #212121;
    color: #e0e0e0;
    border-color: #424242;
  }

  .error-message {
    background: rgba(255, 255, 255, 0.1);
    color: #ffcdd2;
  }

  .status-details {
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}