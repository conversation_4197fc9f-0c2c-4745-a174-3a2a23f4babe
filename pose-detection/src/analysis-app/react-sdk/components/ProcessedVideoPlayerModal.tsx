/**
 * ProcessedVideoPlayerModal Component
 * 
 * Updated video player that uses pre-processed Modal/SmoothNet data
 * Based on dev-app-smoothnet.html implementation
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { ProcessedVideoPlayerProps } from '../types/components';
import { useModalData } from '../hooks/useModalData';
import { VideoControls } from './VideoControls';
import { MetricsOverlay, OverlayMetrics } from './MetricsOverlay';
import './ProcessedVideoPlayer.css';

/**
 * Configuration for keypoint filtering per SPEC
 */
const SKIP_KEYPOINTS = [
  1, 2, 3, 4, 5, 6,        // eyes (left_eye_inner, left_eye, left_eye_outer, right_eye_inner, right_eye, right_eye_outer)
  9, 10,                   // mouth (mouth_left, mouth_right)
  17, 18, 19, 20, 21, 22   // fingers (pinky, index, thumb for both hands)
];

/**
 * Keypoint names for debugging
 */
const BLAZEPOSE_KEYPOINT_NAMES = [
  'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
  'right_eye_inner', 'right_eye', 'right_eye_outer',
  'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
  'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
  'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
  'left_index', 'right_index', 'left_thumb', 'right_thumb',
  'left_hip', 'right_hip', 'left_knee', 'right_knee',
  'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
  'left_foot_index', 'right_foot_index'
];

/**
 * Skeleton connections for drawing pose structure
 */
const SKELETON_CONNECTIONS = [
  // Head (nose to ears only - skip eyes and mouth)
  [0, 7], [0, 8], // nose to ears for head angle
  // Torso
  [11, 12], [11, 23], [12, 24], [23, 24], // shoulders and hips
  // Arms (to wrists only, skip fingers)
  [11, 13], [13, 15], [12, 14], [14, 16], // shoulder to elbow to wrist
  // Legs (full leg structure for running analysis)
  [23, 25], [25, 27], [27, 29], [27, 31], // left leg including foot
  [24, 26], [26, 28], [28, 30], [28, 32]  // right leg including foot
];

interface ProcessedVideoPlayerModalProps extends Omit<ProcessedVideoPlayerProps, 'resultsUrl'> {
  poseDataUrl: string; // Modal JSON data URL
  showControls?: boolean;
  showMetricsOverlay?: boolean;
  showFrameInfo?: boolean;
  onFrameChange?: (frameData: any) => void;
}

export const ProcessedVideoPlayerModal: React.FC<ProcessedVideoPlayerModalProps> = ({
  videoUrl,
  poseDataUrl,
  analysisType = 'running',
  viewType,
  overlayStyle = 'medical',
  userHeight,
  onPoseData,
  onMetrics,
  autoPlay = false,
  controls = true,
  loop = false,
  showControls = true,
  showMetricsOverlay = true,
  showFrameInfo = true,
  onFrameChange
}) => {
  // Refs
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | null>(null);
  const lastFrameTimeRef = useRef<number>(0);
  const frameCountRef = useRef<number>(0);
  
  // State
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentFrame, setCurrentFrame] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [videoDimensionMismatch, setVideoDimensionMismatch] = useState<boolean>(false);
  const [currentTime, setCurrentTime] = useState<number>(0);
  const [duration, setDuration] = useState<number>(0);
  const [fps, setFps] = useState<number>(30);
  const [processingTime, setProcessingTime] = useState<number>(0);
  
  // Use Modal data hook
  const {
    modalData,
    isLoading: modalLoading,
    error: modalError,
    findFrameForTime,
    getVideoDimensions
  } = useModalData({
    poseDataUrl,
    onStatusChange: ({ isLoading, message }) => {
      console.log(`Modal data status: ${message} (loading: ${isLoading})`);
    },
    onError: (err) => {
      setError(`Modal data error: ${err.message}`);
    }
  });

  /**
   * Synchronize canvas with video dimensions
   * Critical: Must match dev-app-smoothnet.html syncCanvasWithVideo()
   */
  const syncCanvasWithVideo = useCallback(() => {
    const video = videoRef.current;
    const canvas = canvasRef.current;
    
    if (!video || !canvas) return;
    
    // Get the actual displayed size of the video element
    const videoRect = video.getBoundingClientRect();
    
    // Set canvas to match video display size exactly
    canvas.width = videoRect.width;
    canvas.height = videoRect.height;
    
    // Set canvas CSS size to match video exactly
    canvas.style.width = `${videoRect.width}px`;
    canvas.style.height = `${videoRect.height}px`;
    
    // Position canvas to exactly overlay the video
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.pointerEvents = 'none';
    
    console.log('Video/Canvas synced:', {
      displaySize: { width: videoRect.width, height: videoRect.height },
      videoRes: { width: video.videoWidth, height: video.videoHeight }
    });
  }, []);

  /**
   * Check for video dimension mismatch with Modal data
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video || !modalData) return;

    const handleLoadedMetadata = () => {
      const modalDimensions = getVideoDimensions();
      const mismatch = video.videoWidth !== modalDimensions.width || 
                      video.videoHeight !== modalDimensions.height;
      
      setVideoDimensionMismatch(mismatch);
      
      if (mismatch) {
        console.warn('Video dimension mismatch:', {
          video: `${video.videoWidth}×${video.videoHeight}`,
          modal: `${modalDimensions.width}×${modalDimensions.height}`
        });
      }
      
      // Set video to 0.25x playback rate and mute audio
      video.playbackRate = 0.25;
      video.muted = true; // Remove audio as requested
      console.log('Video playback rate set to 0.25x for pose synchronization (audio muted)');
      
      // Update duration and FPS
      setDuration(video.duration);
      setFps(modalData?.fps || 30);
      
      syncCanvasWithVideo();
    };

    const handleTimeUpdate = () => {
      if (video) setCurrentTime(video.currentTime);
    };

    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('timeupdate', handleTimeUpdate);
    
    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('timeupdate', handleTimeUpdate);
    };
  }, [modalData, getVideoDimensions, syncCanvasWithVideo]);

  /**
   * Draw Modal pose overlay
   * Based on dev-app-smoothnet.html drawModalPose() function
   */
  const drawModalPose = useCallback((frameData: any) => {
    const canvas = canvasRef.current;
    const video = videoRef.current;
    if (!canvas || !video || !modalData || !frameData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const modalDimensions = getVideoDimensions();
    
    // Scale coordinates from Modal data to canvas display size
    const scaleX = canvas.width / modalDimensions.width;
    const scaleY = canvas.height / modalDimensions.height;
    
    const minConfidence = 0.3;
    let drawnKeypoints = 0;
    let invalidKeypoints = 0;

    // Clear canvas and draw video frame
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Draw keypoints (excluding the ones we want to skip)
    frameData.keypoints.forEach((keypoint: any, idx: number) => {
      if (SKIP_KEYPOINTS.includes(idx)) return; // Skip filtered keypoints
      
      // Check for invalid coordinates
      if ((keypoint as any).isInvalid) {
        invalidKeypoints++;
        return;
      }
      
      if (keypoint.score > minConfidence) {
        // Scale coordinates
        const x = keypoint.x * scaleX;
        const y = keypoint.y * scaleY;
        
        // Color based on confidence and validity
        let color;
        if ((keypoint as any).isEdgeCase) {
          color = '#ff8800'; // Orange for edge cases
        } else if (keypoint.score > 0.8) {
          color = '#00ff00'; // Green for high confidence
        } else if (keypoint.score > 0.5) {
          color = '#ffaa00'; // Orange for medium confidence
        } else {
          color = '#ff0000'; // Red for low confidence
        }
        
        // Draw keypoint with outline
        ctx.fillStyle = color;
        ctx.strokeStyle = (keypoint as any).isEdgeCase ? 'red' : 'white';
        ctx.lineWidth = (keypoint as any).isEdgeCase ? 3 : 2;
        ctx.beginPath();
        ctx.arc(x, y, (keypoint as any).isEdgeCase ? 8 : 6, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
        
        // Draw keypoint index for debugging
        ctx.fillStyle = (keypoint as any).isEdgeCase ? 'red' : 'white';
        ctx.font = '10px Arial';
        ctx.fillText((keypoint as any).isEdgeCase ? `${idx}!` : idx.toString(), x + 6, y - 6);
        
        drawnKeypoints++;
      }
    });

    // Draw skeleton connections
    drawSkeleton(ctx, frameData.keypoints, scaleX, scaleY);
    
    // Draw data quality indicator if there are invalid keypoints
    if (invalidKeypoints > 0) {
      ctx.fillStyle = 'rgba(255, 0, 0, 0.9)';
      ctx.fillRect(10, canvas.height - 80, 300, 30);
      ctx.fillStyle = 'white';
      ctx.font = '14px Arial';
      ctx.fillText(`⚠️ ${invalidKeypoints} invalid coordinates (0,0)`, 20, canvas.height - 60);
    }
    
    // Draw SmoothNet indicator
    ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
    ctx.fillRect(canvas.width - 220, 10, 210, 30);
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText('🔄 SmoothNet 3DPW-SPIN-3D', canvas.width - 210, 30);
    
    // Draw dimension mismatch warning if needed
    if (videoDimensionMismatch) {
      ctx.fillStyle = 'rgba(255, 0, 0, 0.9)';
      ctx.fillRect(0, 0, canvas.width, 100);
      ctx.fillStyle = 'white';
      ctx.font = 'bold 16px Arial';
      ctx.fillText('❌ DIMENSION MISMATCH WARNING', 10, 25);
      ctx.font = '14px Arial';
      ctx.fillText(`Video: ${video.videoWidth}×${video.videoHeight} vs Modal: ${modalDimensions.width}×${modalDimensions.height}`, 10, 45);
      ctx.fillText('Coordinates will be scaled incorrectly', 10, 65);
    }
  }, [modalData, getVideoDimensions, videoDimensionMismatch]);

  /**
   * Frame stepping logic
   */
  const getFrameDuration = useCallback((): number => {
    return 1 / (fps || 30);
  }, [fps]);

  const stepForward = useCallback(() => {
    const video = videoRef.current;
    if (!video || !duration) return;
    
    const frameDuration = getFrameDuration();
    const newTime = Math.min(duration, video.currentTime + frameDuration);
    video.currentTime = newTime;
  }, [duration, getFrameDuration]);

  const stepBackward = useCallback(() => {
    const video = videoRef.current;
    if (!video) return;
    
    const frameDuration = getFrameDuration();
    const newTime = Math.max(0, video.currentTime - frameDuration);
    video.currentTime = newTime;
  }, [getFrameDuration]);

  const handleSeek = useCallback((time: number) => {
    const video = videoRef.current;
    if (!video) return;
    
    video.currentTime = Math.max(0, Math.min(duration, time));
  }, [duration]);

  const handlePlay = useCallback(() => {
    const video = videoRef.current;
    if (video) {
      video.play();
    }
  }, []);

  const handlePause = useCallback(() => {
    const video = videoRef.current;
    if (video) {
      video.pause();
    }
  }, []);

  /**
   * Draw skeleton connections
   */
  const drawSkeleton = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    // Use brighter, more visible colors for skeleton
    ctx.strokeStyle = 'rgba(0, 255, 255, 0.9)'; // Bright cyan
    ctx.lineWidth = 3; // Thicker lines for better visibility
    
    SKELETON_CONNECTIONS.forEach(([i, j]) => {
      const kp1 = keypoints[i];
      const kp2 = keypoints[j];
      
      if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
        ctx.beginPath();
        ctx.moveTo(kp1.x * scaleX, kp1.y * scaleY);
        ctx.lineTo(kp2.x * scaleX, kp2.y * scaleY);
        ctx.stroke();
      }
    });
  }, []);

  /**
   * Process frame - main rendering loop
   * Based on dev-app-smoothnet.html processFrame() function
   */
  const processFrame = useCallback(async () => {
    const startTime = performance.now();
    const video = videoRef.current;
    if (!video || !modalData) return;

    // Find frame data for current video time
    const frameData = findFrameForTime(video.currentTime);
    
    if (frameData && frameData.keypoints) {
      setCurrentFrame(frameData);
      drawModalPose(frameData);
      
      // Call callbacks - convert Modal data to ProcessedPoseData format
      const processedPoseData = {
        keypoints: frameData.keypoints,
        score: frameData.score || 0,
        timestamp: frameData.timestamp,
        frameNumber: frameData.frameNumber,
        modelType: 'full' as const,
        smoothed: true
      };
      
      onPoseData?.(processedPoseData);
      onFrameChange?.(frameData);
      
      // Log occasionally
      if (frameCountRef.current % 30 === 0) {
        console.log(`Modal frame: ${frameData.frameNumber} @ ${frameData.timestamp.toFixed(2)}s`);
      }
    }
    
    frameCountRef.current++;
    
    // Update processing time for metrics overlay
    const endTime = performance.now();
    const startTime = endTime - 16; // Approximate start time
    setProcessingTime(endTime - startTime);
  }, [modalData, findFrameForTime, drawModalPose, onPoseData]);

  /**
   * Animation loop
   */
  const processLoop = useCallback(async () => {
    const video = videoRef.current;
    if (!isPlaying || !video || video.paused || video.ended) {
      return;
    }
    
    // Limit frame rate to ~30fps for smooth rendering
    const now = performance.now();
    if (now - lastFrameTimeRef.current < 33) { // ~30fps = 33ms between frames
      animationRef.current = requestAnimationFrame(processLoop);
      return;
    }
    
    await processFrame();
    lastFrameTimeRef.current = now;
    
    if (isPlaying) {
      animationRef.current = requestAnimationFrame(processLoop);
    }
  }, [isPlaying, processFrame]);

  /**
   * Calculate metrics for overlay
   */
  const calculateOverlayMetrics = useCallback((): OverlayMetrics | null => {
    if (!currentFrame || !modalData) return null;
    
    const frameRate = frameCountRef.current > 0 ? 
      frameCountRef.current / (performance.now() / 1000) : 0;
    
    // Count invalid keypoints (coordinates at 0,0)
    const invalidKeypoints = currentFrame.keypoints?.filter(
      (kp: any) => kp.x === 0 && kp.y === 0
    ).length || 0;
    
    // Calculate average pose score
    const avgScore = currentFrame.keypoints?.reduce(
      (sum: number, kp: any) => sum + (kp.score || 0), 0
    ) / (currentFrame.keypoints?.length || 1) * 100;
    
    return {
      // Performance indicators
      fps: frameRate,
      processingTime,
      
      // Current frame data
      frameNumber: currentFrame.frameNumber || 0,
      timestamp: currentFrame.timestamp || currentTime,
      poseScore: avgScore || 0,
      invalidKeypoints,
      
      // Running metrics (placeholder - would come from useRunningMetrics)
      strideLength: undefined, // Would be calculated from running metrics
      stepHeight: undefined,
      cadence: undefined,
      verticalOscillation: undefined,
      
      // Status indicators
      modalDataStatus: modalData ? 'loaded' : 'loading',
      coordinateValidation: invalidKeypoints > 5 ? 'invalid' : 
                           invalidKeypoints > 0 ? 'warning' : 'valid',
      smoothNetActive: true
    };
  }, [currentFrame, modalData, processingTime, currentTime]);

  /**
   * Start processing
   */
  const startProcessing = useCallback(() => {
    setIsPlaying(true);
    processLoop();
  }, [processLoop]);

  /**
   * Stop processing
   */
  const stopProcessing = useCallback(() => {
    setIsPlaying(false);
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
  }, []);

  /**
   * Handle video play/pause events
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const handlePlay = () => startProcessing();
    const handlePause = () => stopProcessing();

    video.addEventListener('play', handlePlay);
    video.addEventListener('pause', handlePause);

    return () => {
      video.removeEventListener('play', handlePlay);
      video.removeEventListener('pause', handlePause);
    };
  }, [startProcessing, stopProcessing]);

  /**
   * Setup resize observer for canvas sync
   */
  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const resizeObserver = new ResizeObserver(() => {
      syncCanvasWithVideo();
    });

    resizeObserver.observe(video);
    window.addEventListener('resize', syncCanvasWithVideo);

    return () => {
      resizeObserver.disconnect();
      window.removeEventListener('resize', syncCanvasWithVideo);
    };
  }, [syncCanvasWithVideo]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  /**
   * Calculate current frame number for display
   */
  const getCurrentFrameNumber = useCallback((): number => {
    if (!fps || !currentTime) return 0;
    return Math.floor(currentTime * fps);
  }, [currentTime, fps]);

  /**
   * Calculate total frame count
   */
  const getTotalFrames = useCallback((): number => {
    if (!fps || !duration) return 0;
    return Math.floor(duration * fps);
  }, [duration, fps]);

  // Calculate overlay metrics
  const overlayMetrics = calculateOverlayMetrics();

  // Handle errors
  const displayError = error || modalError?.message;

  if (modalLoading) {
    return (
      <div className="processed-video-player loading">
        <div className="loading-spinner">Loading Modal SmoothNet data...</div>
      </div>
    );
  }

  if (displayError) {
    return (
      <div className="processed-video-player error">
        <div className="error-message">
          <h4>Error Loading Video Analysis</h4>
          <p>{displayError}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="processed-video-player">
      <div className="video-container">
        <div className="video-wrapper">
          <video
            ref={videoRef}
            src={videoUrl}
            controls={false} // Always hide native controls
            autoPlay={autoPlay}
            loop={loop}
            playsInline
            muted={true} // Always muted
            className="analysis-video"
          />
          <canvas
            ref={canvasRef}
            className="pose-overlay"
          />
          
          {/* Metrics Overlay */}
          {showMetricsOverlay && (
            <MetricsOverlay
              canvasRef={canvasRef}
              metrics={overlayMetrics}
              showDebugInfo={true}
              showRunningMetrics={true}
              showSmoothNetBranding={true}
              isVisible={true}
            />
          )}
        </div>
        
        {/* Manual Video Controls */}
        {showControls && (
          <VideoControls
            videoRef={videoRef}
            isPlaying={isPlaying}
            currentTime={currentTime}
            duration={duration}
            currentFrame={getCurrentFrameNumber()}
            totalFrames={getTotalFrames()}
            fps={fps}
            onPlay={handlePlay}
            onPause={handlePause}
            onSeek={handleSeek}
            onStepForward={stepForward}
            onStepBackward={stepBackward}
          />
        )}
        
        {videoDimensionMismatch && (
          <div className="dimension-warning">
            ⚠️ Video dimensions don't match Modal data. Coordinates may be inaccurate.
          </div>
        )}
        
        {showFrameInfo && currentFrame && (
          <div className="frame-info">
            Frame: {getCurrentFrameNumber()} | Time: {currentTime.toFixed(2)}s | Score: {((currentFrame.keypoints?.reduce((sum: number, kp: any) => sum + (kp.score || 0), 0) / (currentFrame.keypoints?.length || 1)) * 100).toFixed(0)}%
          </div>
        )}
      </div>
    </div>
  );
};