#!/usr/bin/env python3
"""
Simple HTTP server to view the visual demo
"""

import http.server
import socketserver
import os
import webbrowser
from threading import Timer

PORT = 8005
DIRECTORY = os.path.dirname(os.path.abspath(__file__))

class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)

    def end_headers(self):
        # Add CORS headers for local development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()

def open_browser():
    webbrowser.open(f'http://localhost:{PORT}/full-app-demo.html')

if __name__ == "__main__":
    os.chdir(DIRECTORY)

    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print(f"🚀 Server started at http://localhost:{PORT}")
        print(f"📁 Serving directory: {DIRECTORY}")
        print(f"🌐 Open http://localhost:{PORT}/full-app-demo.html in your browser")
        print("Press Ctrl+C to stop the server")

        # Open browser after 1 second
        timer = Timer(1, open_browser)
        timer.start()

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n✋ Server stopped")
            timer.cancel()
