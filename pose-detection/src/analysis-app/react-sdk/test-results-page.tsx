/**
 * ResultsPage Test Component
 * 
 * Test component to verify ResultsPage functionality
 */

import React from 'react';
import { ResultsPage } from './components/ResultsPage';

/**
 * Test component for ResultsPage
 */
export const ResultsPageTest: React.FC = () => {
  const handleBack = () => {
    console.log('🔄 Back button clicked');
    // In real app, this would navigate back
  };

  const handleNewAnalysis = () => {
    console.log('🆕 New Analysis button clicked');
    // In real app, this would navigate to upload page
  };

  const handleExport = (format: 'pdf' | 'csv' | 'json') => {
    console.log(`📄 Export clicked: ${format}`);
    // In real app, this would trigger export
  };

  return (
    <div className="w-full h-screen bg-gray-100">
      <ResultsPage
        analysisId="test-analysis-123"
        userEmail="<EMAIL>"
        onBack={handleBack}
        onNewAnalysis={handleNewAnalysis}
        onExport={handleExport}
      />
    </div>
  );
};

/**
 * Test component with error state
 */
export const ResultsPageErrorTest: React.FC = () => {
  return (
    <div className="w-full h-screen bg-gray-100">
      <ResultsPage
        analysisId="invalid-analysis-id"
        userEmail="<EMAIL>"
        onBack={() => console.log('Back clicked')}
        onNewAnalysis={() => console.log('New analysis clicked')}
        onExport={(format) => console.log('Export:', format)}
      />
    </div>
  );
};

/**
 * Test component with processing state
 */
export const ResultsPageProcessingTest: React.FC = () => {
  return (
    <div className="w-full h-screen bg-gray-100">
      <ResultsPage
        analysisId="processing-analysis-456"
        userEmail="<EMAIL>"
        onBack={() => console.log('Back clicked')}
        onNewAnalysis={() => console.log('New analysis clicked')}
        onExport={(format) => console.log('Export:', format)}
      />
    </div>
  );
};

/**
 * Combined test suite component
 */
export const ResultsPageTestSuite: React.FC = () => {
  const [testMode, setTestMode] = React.useState<'normal' | 'error' | 'processing'>('normal');

  const renderTest = () => {
    switch (testMode) {
      case 'error':
        return <ResultsPageErrorTest />;
      case 'processing':
        return <ResultsPageProcessingTest />;
      default:
        return <ResultsPageTest />;
    }
  };

  return (
    <div className="w-full h-screen">
      {/* Test Mode Selector */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-lg font-semibold text-gray-900">
            ResultsPage Test Suite
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setTestMode('normal')}
              className={`px-3 py-1 text-sm rounded ${
                testMode === 'normal'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Normal
            </button>
            <button
              onClick={() => setTestMode('processing')}
              className={`px-3 py-1 text-sm rounded ${
                testMode === 'processing'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Processing
            </button>
            <button
              onClick={() => setTestMode('error')}
              className={`px-3 py-1 text-sm rounded ${
                testMode === 'error'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Error
            </button>
          </div>
        </div>
        <p className="mt-2 text-sm text-gray-600">
          Testing ResultsPage component with different states and data scenarios
        </p>
      </div>

      {/* Test Component */}
      <div className="flex-1">
        {renderTest()}
      </div>

      {/* Test Instructions */}
      <div className="bg-green-50 border-t border-green-200 p-4">
        <h3 className="font-semibold text-green-900 mb-2">Test Instructions:</h3>
        <ul className="text-sm text-green-800 space-y-1">
          <li>1. <strong>Normal:</strong> Tests completed analysis with video and metrics</li>
          <li>2. <strong>Processing:</strong> Tests processing state with progress indicators</li>
          <li>3. <strong>Error:</strong> Tests error handling and retry functionality</li>
          <li>4. Check browser console for event handlers and hook interactions</li>
          <li>5. Verify responsive design and component composition</li>
        </ul>
      </div>
    </div>
  );
};