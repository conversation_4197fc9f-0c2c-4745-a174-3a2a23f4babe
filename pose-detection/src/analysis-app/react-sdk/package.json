{"name": "@peak-insight/pose-analysis-sdk", "version": "1.0.0", "description": "React SDK for BlazePose biomechanical analysis integration", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "private": true, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "tslint -p . -t verbose"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@tensorflow/tfjs-core": "^4.10.0", "@tensorflow/tfjs-backend-webgl": "^4.10.0", "@supabase/supabase-js": "^2.39.7"}, "dependencies": {"@types/react": "^18.2.22", "@types/react-dom": "^18.2.7", "clsx": "^2.1.1", "tailwind-merge": "^2.5.2", "lucide-react": "^0.446.0", "typescript": "^5.2.2"}, "devDependencies": {"tslint": "^6.1.3", "tslint-no-circular-imports": "~0.7.0"}, "keywords": ["tensorflow", "blazepose", "pose-detection", "biomechanics", "running-analysis", "react", "typescript"], "author": "Peak Insight", "license": "MIT"}