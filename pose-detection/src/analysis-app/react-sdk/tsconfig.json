{"compilerOptions": {"target": "ES2018", "lib": ["ES2018", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/hooks/*": ["./hooks/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"], "@/config/*": ["./config/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"]}