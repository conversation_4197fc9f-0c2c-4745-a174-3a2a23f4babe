<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Actual Modal Data Processing - React SDK</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .test-controls button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-controls button:hover {
            background: #0056b3;
        }
        
        .test-controls button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .status {
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
            font-family: monospace;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        .test-results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
            margin-top: 15px;
        }
        
        .test-results pre {
            margin: 0;
            font-size: 12px;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        
        .video-section {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .video-container {
            flex: 1;
            max-width: 400px;
        }
        
        .video-container video {
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }
        
        .data-panel {
            flex: 1;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        
        .metric-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Modal Data Processing Pipeline Test</h1>
            <p><strong>Purpose:</strong> Validate Modal data loading with actual project files (NOT mock data)</p>
            <p><strong>Files:</strong> <code>../Michael_test_side.mp4</code> + <code>../d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json</code></p>
        </div>
        
        <div class="test-section">
            <h2>🔧 Test Controls</h2>
            <div class="test-controls">
                <button onclick="testDataLoading()" id="loadBtn">1. Load Modal Data</button>
                <button onclick="testDataValidation()" id="validateBtn" disabled>2. Validate Structure</button>
                <button onclick="testFrameFinding()" id="frameBtn" disabled>3. Test Frame Finding</button>
                <button onclick="testKeypointValidation()" id="keypointBtn" disabled>4. Validate Keypoints</button>
                <button onclick="testVideoSync()" id="syncBtn" disabled>5. Test Video Sync</button>
                <button onclick="runFullPipeline()" id="fullBtn">🚀 Run Full Pipeline</button>
            </div>
            
            <div id="testStatus" class="status info">Ready to test Modal data processing pipeline...</div>
        </div>
        
        <div class="test-section">
            <h2>📊 Pipeline Results</h2>
            <div class="video-section">
                <div class="video-container">
                    <video id="testVideo" controls muted>
                        <source src="../Michael_test_side.mp4" type="video/mp4">
                        Your browser does not support the video tag.
                    </video>
                    <div class="video-info">
                        <p><strong>Video:</strong> <span id="videoInfo">Not loaded</span></p>
                        <p><strong>Current Frame:</strong> <span id="currentFrame">0</span></p>
                        <p><strong>Modal Frame:</strong> <span id="modalFrame">Not synced</span></p>
                    </div>
                </div>
                
                <div class="data-panel">
                    <h3>Modal Data Analysis</h3>
                    <div id="modalAnalysis">
                        <p>Modal data not loaded yet...</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📈 Validation Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="totalFrames">--</div>
                    <div class="metric-label">Total Modal Frames</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="validKeypoints">--</div>
                    <div class="metric-label">Valid Keypoints</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="avgPoseScore">--</div>
                    <div class="metric-label">Avg Pose Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="videoDimensions">--</div>
                    <div class="metric-label">Video Dimensions</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="modalDimensions">--</div>
                    <div class="metric-label">Modal Dimensions</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="dimensionMatch">--</div>
                    <div class="metric-label">Dimension Match</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔍 Detailed Results</h2>
            <div class="test-results">
                <pre id="detailedResults">No tests run yet...</pre>
            </div>
        </div>
    </div>

    <script>
        // Global state
        let modalData = null;
        let video = null;
        let testResults = [];
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            video = document.getElementById('testVideo');
            video.addEventListener('loadedmetadata', updateVideoInfo);
            video.addEventListener('timeupdate', updateCurrentFrame);
            
            logResult('System initialized. Ready to test Modal data pipeline.');
        });
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('testStatus');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function logResult(message, data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${message}`;
            testResults.push(entry);
            
            if (data) {
                testResults.push(JSON.stringify(data, null, 2));
            }
            
            const resultsElement = document.getElementById('detailedResults');
            resultsElement.textContent = testResults.join('\n');
            resultsElement.scrollTop = resultsElement.scrollHeight;
        }
        
        function enableButton(buttonId) {
            document.getElementById(buttonId).disabled = false;
        }
        
        // Test 1: Load Modal Data
        async function testDataLoading() {
            updateStatus('Loading Modal data from d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json...', 'info');
            
            try {
                const response = await fetch('../d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                modalData = await response.json();
                
                logResult('✅ Modal data loaded successfully', {
                    fileSize: JSON.stringify(modalData).length,
                    hasFrames: !!modalData.frames,
                    frameCount: modalData.frames?.length || 0,
                    hasVideo: !!modalData.video,
                    modelType: modalData.modelType
                });
                
                updateStatus('✅ Modal data loaded successfully!', 'success');
                enableButton('validateBtn');
                updateModalAnalysis();
                
            } catch (error) {
                logResult('❌ Failed to load Modal data', { error: error.message });
                updateStatus(`❌ Failed to load Modal data: ${error.message}`, 'error');
            }
        }
        
        // Test 2: Validate Data Structure
        function testDataValidation() {
            if (!modalData) {
                updateStatus('❌ No Modal data to validate', 'error');
                return;
            }
            
            updateStatus('Validating Modal data structure...', 'info');
            
            const validationResults = {
                hasVideo: !!modalData.video,
                hasFrames: !!modalData.frames && Array.isArray(modalData.frames),
                frameCount: modalData.frames?.length || 0,
                hasVideoWidth: typeof modalData.videoWidth === 'number',
                hasVideoHeight: typeof modalData.videoHeight === 'number',
                hasFps: typeof modalData.fps === 'number',
                hasModelType: !!modalData.modelType,
                firstFrameStructure: modalData.frames?.[0] ? Object.keys(modalData.frames[0]) : []
            };
            
            const isValid = validationResults.hasVideo && 
                           validationResults.hasFrames && 
                           validationResults.frameCount > 0 &&
                           validationResults.hasVideoWidth &&
                           validationResults.hasVideoHeight;
            
            if (isValid) {
                logResult('✅ Modal data structure validation passed', validationResults);
                updateStatus('✅ Data structure validation passed!', 'success');
                enableButton('frameBtn');
                updateMetrics();
            } else {
                logResult('❌ Modal data structure validation failed', validationResults);
                updateStatus('❌ Data structure validation failed', 'error');
            }
        }
        
        // Test 3: Test Frame Finding
        function testFrameFinding() {
            if (!modalData?.frames) {
                updateStatus('❌ No frames to test', 'error');
                return;
            }
            
            updateStatus('Testing frame finding by timestamp...', 'info');
            
            const testTimestamps = [0, 1.5, 3.0, 5.0];
            const frameResults = testTimestamps.map(timestamp => {
                const frame = findFrameByTimestamp(timestamp);
                return {
                    searchTime: timestamp,
                    foundFrame: frame ? frame.frameNumber : null,
                    frameTime: frame ? frame.timestamp : null,
                    hasKeypoints: frame ? (frame.keypoints?.length || 0) : 0
                };
            });
            
            const successfulFinds = frameResults.filter(r => r.foundFrame !== null).length;
            
            if (successfulFinds > 0) {
                logResult('✅ Frame finding test passed', {
                    testedTimestamps: testTimestamps.length,
                    successfulFinds: successfulFinds,
                    results: frameResults
                });
                updateStatus(`✅ Frame finding: ${successfulFinds}/${testTimestamps.length} successful`, 'success');
                enableButton('keypointBtn');
            } else {
                logResult('❌ Frame finding test failed', { results: frameResults });
                updateStatus('❌ Frame finding test failed', 'error');
            }
        }
        
        // Test 4: Validate Keypoints
        function testKeypointValidation() {
            if (!modalData?.frames) {
                updateStatus('❌ No frames to validate keypoints', 'error');
                return;
            }
            
            updateStatus('Validating keypoint data...', 'info');
            
            const sampleFrames = modalData.frames.slice(0, 10); // Test first 10 frames
            const keypointResults = sampleFrames.map((frame, index) => {
                const keypoints = frame.keypoints || [];
                
                // Modal data uses absolute pixel coordinates, not normalized 0-1
                // Valid keypoints should have reasonable pixel values within video dimensions
                const validKeypoints = keypoints.filter(kp => 
                    kp && 
                    typeof kp.x === 'number' && 
                    typeof kp.y === 'number' && 
                    typeof kp.score === 'number' &&
                    kp.x >= 0 && kp.x <= modalData.videoWidth &&
                    kp.y >= 0 && kp.y <= modalData.videoHeight &&
                    kp.score >= 0.1 // Minimum confidence threshold
                );
                
                return {
                    frameIndex: index,
                    frameNumber: frame.frameNumber,
                    totalKeypoints: keypoints.length,
                    validKeypoints: validKeypoints.length,
                    expectedKeypoints: 33, // BlazePose Full has 33 main keypoints
                    validityPercent: validKeypoints.length / keypoints.length * 100,
                    avgScore: keypoints.reduce((sum, kp) => sum + (kp.score || 0), 0) / keypoints.length
                };
            });
            
            const avgValidity = keypointResults.reduce((sum, r) => sum + r.validityPercent, 0) / keypointResults.length;
            const avgScore = keypointResults.reduce((sum, r) => sum + r.avgScore, 0) / keypointResults.length;
            
            if (avgValidity >= 70 && avgScore >= 0.5) { // At least 70% valid keypoints with decent confidence
                logResult('✅ Keypoint validation passed', {
                    sampledFrames: sampleFrames.length,
                    avgValidityPercent: avgValidity.toFixed(1),
                    avgConfidence: avgScore.toFixed(3),
                    results: keypointResults
                });
                updateStatus(`✅ Keypoints valid: ${avgValidity.toFixed(1)}% (${avgScore.toFixed(3)} confidence)`, 'success');
                enableButton('syncBtn');
            } else {
                logResult('❌ Keypoint validation failed', {
                    avgValidityPercent: avgValidity.toFixed(1),
                    avgConfidence: avgScore.toFixed(3),
                    results: keypointResults
                });
                updateStatus(`❌ Keypoints invalid: ${avgValidity.toFixed(1)}% (${avgScore.toFixed(3)} confidence)`, 'error');
            }
        }
        
        // Test 5: Test Video Sync
        function testVideoSync() {
            if (!modalData || !video) {
                updateStatus('❌ Missing Modal data or video', 'error');
                return;
            }
            
            updateStatus('Testing video synchronization...', 'info');
            
            const videoDimensions = {
                width: video.videoWidth,
                height: video.videoHeight,
                duration: video.duration
            };
            
            const modalDimensions = {
                width: modalData.videoWidth,
                height: modalData.videoHeight,
                fps: modalData.fps
            };
            
            const dimensionsMatch = videoDimensions.width === modalDimensions.width &&
                                  videoDimensions.height === modalDimensions.height;
            
            const syncResults = {
                videoDimensions,
                modalDimensions,
                dimensionsMatch,
                isPortrait: videoDimensions.height > videoDimensions.width,
                aspectRatio: videoDimensions.width / videoDimensions.height
            };
            
            if (dimensionsMatch) {
                logResult('✅ Video synchronization test passed', syncResults);
                updateStatus('✅ Video and Modal data synchronized!', 'success');
            } else {
                logResult('⚠️ Video dimensions mismatch', syncResults);
                updateStatus('⚠️ Video dimensions don\'t match Modal data', 'warning');
            }
        }
        
        // Helper Functions
        function findFrameByTimestamp(targetTime) {
            if (!modalData?.frames) return null;
            
            // Find closest frame by timestamp
            let closestFrame = null;
            let closestDiff = Infinity;
            
            for (const frame of modalData.frames) {
                const diff = Math.abs(frame.timestamp - targetTime);
                if (diff < closestDiff) {
                    closestDiff = diff;
                    closestFrame = frame;
                }
            }
            
            return closestFrame;
        }
        
        function updateVideoInfo() {
            const info = `${video.videoWidth}x${video.videoHeight}, ${video.duration.toFixed(1)}s`;
            document.getElementById('videoInfo').textContent = info;
            document.getElementById('videoDimensions').textContent = `${video.videoWidth}x${video.videoHeight}`;
        }
        
        function updateCurrentFrame() {
            const currentTime = video.currentTime;
            const frameNumber = Math.floor(currentTime * (modalData?.fps || 30));
            document.getElementById('currentFrame').textContent = frameNumber;
            
            if (modalData) {
                const modalFrame = findFrameByTimestamp(currentTime);
                document.getElementById('modalFrame').textContent = 
                    modalFrame ? `Frame ${modalFrame.frameNumber}` : 'No match';
            }
        }
        
        function updateModalAnalysis() {
            if (!modalData) return;
            
            const analysis = `
                <p><strong>Video File:</strong> ${modalData.video}</p>
                <p><strong>Dimensions:</strong> ${modalData.videoWidth}x${modalData.videoHeight}</p>
                <p><strong>FPS:</strong> ${modalData.fps}</p>
                <p><strong>Model:</strong> ${modalData.modelType}</p>
                <p><strong>Total Frames:</strong> ${modalData.frames?.length || 0}</p>
                <p><strong>Processing Time:</strong> ${modalData.processingTime || 'Unknown'}</p>
            `;
            
            document.getElementById('modalAnalysis').innerHTML = analysis;
        }
        
        function updateMetrics() {
            if (!modalData) return;
            
            document.getElementById('totalFrames').textContent = modalData.frames?.length || 0;
            document.getElementById('modalDimensions').textContent = `${modalData.videoWidth}x${modalData.videoHeight}`;
            
            if (modalData.frames?.length > 0) {
                const sampleFrames = modalData.frames.slice(0, 50);
                const totalKeypoints = sampleFrames.reduce((sum, frame) => 
                    sum + (frame.keypoints?.length || 0), 0);
                const avgKeypoints = totalKeypoints / sampleFrames.length;
                
                document.getElementById('validKeypoints').textContent = avgKeypoints.toFixed(1);
                
                // Calculate average pose score if available
                const scoresSum = sampleFrames.reduce((sum, frame) => 
                    sum + (frame.poseScore || 0), 0);
                const avgScore = scoresSum / sampleFrames.length;
                document.getElementById('avgPoseScore').textContent = avgScore.toFixed(1) + '%';
            }
            
            // Check dimension match when video is loaded
            if (video && video.videoWidth) {
                const match = video.videoWidth === modalData.videoWidth && 
                             video.videoHeight === modalData.videoHeight;
                document.getElementById('dimensionMatch').textContent = match ? '✅ Yes' : '❌ No';
            }
        }
        
        // Run Full Pipeline
        async function runFullPipeline() {
            updateStatus('Running complete Modal data processing pipeline...', 'info');
            logResult('🚀 Starting full pipeline test...');
            
            await testDataLoading();
            if (modalData) {
                testDataValidation();
                testFrameFinding();
                testKeypointValidation();
                testVideoSync();
                
                logResult('🎉 Full pipeline test completed!');
                updateStatus('🎉 Full pipeline test completed! Check results above.', 'success');
            }
        }
        
        // Instructions
        console.log(`
=== Modal Data Processing Pipeline Test ===

This test validates that our React SDK can properly process actual Modal data files.

Key validations:
1. ✅ Load Modal JSON data from server
2. ✅ Validate expected data structure (frames, dimensions, etc.)
3. ✅ Find frames by timestamp for video synchronization
4. ✅ Validate BlazePose Full keypoints (39 expected)
5. ✅ Check video/modal dimension compatibility

Files being tested:
- Video: ../Michael_test_side.mp4
- Modal Data: ../d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json

This addresses the concern: "we haven't tested the modal data processing pipeline yet"
and ensures we're not using mock data that could show "false success".
        `);
    </script>
</body>
</html>