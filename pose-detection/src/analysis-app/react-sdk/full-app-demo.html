<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxWattz Biometrics - Full App Preview</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { margin: 0; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
        .pose-overlay { 
            position: absolute; 
            top: 0; 
            left: 0; 
            width: 100%; 
            height: 100%; 
            pointer-events: none;
        }
        .video-container {
            position: relative;
            background: #000;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .skeleton-line {
            stroke: #3B82F6;
            stroke-width: 2;
            opacity: 0.8;
        }
        .keypoint {
            fill: #10B981;
            stroke: #fff;
            stroke-width: 2;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // Simulate the actual ResultsPage as it will appear in the main app
        const BiometricsPage = () => {
            const [activeTab, setActiveTab] = useState('side');
            const [isPlaying, setIsPlaying] = useState(false);
            const videoRef = useRef(null);
            const canvasRef = useRef(null);

            // Simulate pose keypoints for visualization
            const mockKeypoints = [
                { x: 250, y: 120, name: 'nose' },
                { x: 240, y: 140, name: 'leftEye' },
                { x: 260, y: 140, name: 'rightEye' },
                { x: 220, y: 200, name: 'leftShoulder' },
                { x: 280, y: 200, name: 'rightShoulder' },
                { x: 200, y: 280, name: 'leftElbow' },
                { x: 300, y: 280, name: 'rightElbow' },
                { x: 250, y: 300, name: 'leftHip' },
                { x: 250, y: 300, name: 'rightHip' },
                { x: 230, y: 400, name: 'leftKnee' },
                { x: 270, y: 400, name: 'rightKnee' },
                { x: 220, y: 480, name: 'leftAnkle' },
                { x: 280, y: 480, name: 'rightAnkle' }
            ];

            const connections = [
                ['leftShoulder', 'rightShoulder'],
                ['leftShoulder', 'leftElbow'],
                ['rightShoulder', 'rightElbow'],
                ['leftShoulder', 'leftHip'],
                ['rightShoulder', 'rightHip'],
                ['leftHip', 'rightHip'],
                ['leftHip', 'leftKnee'],
                ['rightHip', 'rightKnee'],
                ['leftKnee', 'leftAnkle'],
                ['rightKnee', 'rightAnkle']
            ];

            const drawPose = () => {
                const canvas = canvasRef.current;
                if (!canvas) return;
                
                const ctx = canvas.getContext('2d');
                ctx.clearRect(0, 0, canvas.width, canvas.height);

                // Draw skeleton connections
                ctx.strokeStyle = '#3B82F6';
                ctx.lineWidth = 2;
                connections.forEach(([start, end]) => {
                    const startPoint = mockKeypoints.find(kp => kp.name === start);
                    const endPoint = mockKeypoints.find(kp => kp.name === end);
                    if (startPoint && endPoint) {
                        ctx.beginPath();
                        ctx.moveTo(startPoint.x, startPoint.y);
                        ctx.lineTo(endPoint.x, endPoint.y);
                        ctx.stroke();
                    }
                });

                // Draw keypoints
                mockKeypoints.forEach(point => {
                    ctx.fillStyle = '#10B981';
                    ctx.strokeStyle = '#fff';
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(point.x, point.y, 6, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();
                });
            };

            useEffect(() => {
                drawPose();
            }, []);

            return (
                <div className="min-h-screen bg-gray-50">
                    {/* Navigation Bar - Your main app navigation */}
                    <nav className="bg-white shadow-sm border-b">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex justify-between h-16">
                                <div className="flex items-center">
                                    <span className="text-xl font-bold text-orange-600">MaxWattz</span>
                                    <div className="ml-10 flex items-baseline space-x-4">
                                        <a href="#" className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">Dashboard</a>
                                        <a href="#" className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">Upload</a>
                                        <a href="#" className="bg-orange-100 text-orange-700 px-3 py-2 rounded-md text-sm font-medium">Biometrics</a>
                                        <a href="#" className="text-gray-500 hover:text-gray-700 px-3 py-2 rounded-md text-sm font-medium">History</a>
                                    </div>
                                </div>
                                <div className="flex items-center">
                                    <span className="text-sm text-gray-700"><EMAIL></span>
                                </div>
                            </div>
                        </div>
                    </nav>

                    {/* This is our ResultsPage component integrated into the main app */}
                    <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                        {/* Page Header */}
                        <div className="bg-white rounded-lg shadow mb-6 p-6">
                            <div className="flex justify-between items-center">
                                <div>
                                    <h1 className="text-2xl font-bold text-gray-900">Running Analysis Results</h1>
                                    <p className="mt-1 text-sm text-gray-500">Analysis completed on {new Date().toLocaleDateString()}</p>
                                </div>
                                <div className="flex space-x-3">
                                    <button className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                        ← Back to Dashboard
                                    </button>
                                    <button className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                        📄 Export PDF
                                    </button>
                                    <button className="px-4 py-2 text-white bg-orange-600 rounded-md hover:bg-orange-700">
                                        + New Analysis
                                    </button>
                                </div>
                            </div>
                        </div>

                        {/* Main Content Area */}
                        <div className="bg-white rounded-lg shadow">
                            {/* View Tabs */}
                            <div className="border-b px-6">
                                <nav className="-mb-px flex space-x-8">
                                    <button
                                        onClick={() => setActiveTab('side')}
                                        className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                                            activeTab === 'side' 
                                                ? 'border-orange-500 text-orange-600' 
                                                : 'border-transparent text-gray-500 hover:text-gray-700'
                                        }`}
                                    >
                                        Side View Analysis
                                    </button>
                                    <button
                                        onClick={() => setActiveTab('rear')}
                                        className={`py-3 px-1 border-b-2 font-medium text-sm transition-colors ${
                                            activeTab === 'rear' 
                                                ? 'border-orange-500 text-orange-600' 
                                                : 'border-transparent text-gray-500 hover:text-gray-700'
                                        }`}
                                    >
                                        Rear View Analysis
                                    </button>
                                </nav>
                            </div>

                            {/* Content Grid */}
                            <div className="p-6">
                                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                                    {/* Video Player with Pose Overlay */}
                                    <div className="lg:col-span-2">
                                        <div className="video-container" style={{ paddingBottom: '56.25%' }}>
                                            <video 
                                                ref={videoRef}
                                                className="absolute inset-0 w-full h-full object-cover"
                                                poster="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1920' height='1080' viewBox='0 0 1920 1080'%3E%3Crect fill='%23374151' width='1920' height='1080'/%3E%3Ctext x='50%25' y='50%25' text-anchor='middle' dy='.3em' fill='%239CA3AF' font-family='Arial' font-size='24'%3EVideo Preview%3C/text%3E%3C/svg%3E"
                                            >
                                                <source src="./Michael_test_side.mp4" type="video/mp4" />
                                            </video>
                                            <canvas 
                                                ref={canvasRef}
                                                className="pose-overlay"
                                                width="500"
                                                height="500"
                                            />
                                            {/* Video Controls */}
                                            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                                                <div className="flex items-center space-x-4 text-white">
                                                    <button 
                                                        onClick={() => setIsPlaying(!isPlaying)}
                                                        className="p-2 hover:bg-white/20 rounded"
                                                    >
                                                        {isPlaying ? '⏸' : '▶️'}
                                                    </button>
                                                    <div className="flex-1 bg-white/30 rounded-full h-1">
                                                        <div className="bg-orange-500 h-1 rounded-full transition-all" style={{ width: '35%' }}></div>
                                                    </div>
                                                    <span className="text-sm">0:52 / 2:30</span>
                                                    <button className="text-sm bg-white/20 px-2 py-1 rounded">
                                                        0.25x
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Metrics Panel */}
                                    <div className="space-y-4">
                                        {/* Performance Score */}
                                        <div className="bg-gradient-to-br from-orange-50 to-orange-100 rounded-lg p-6 text-center">
                                            <h3 className="text-sm font-medium text-orange-900 mb-2">Overall Performance</h3>
                                            <div className="text-4xl font-bold text-orange-600 mb-2">85%</div>
                                            <div className="text-sm text-orange-700">Excellent Form</div>
                                        </div>

                                        {/* Key Metrics */}
                                        <div className="bg-white border border-gray-200 rounded-lg p-4">
                                            <h3 className="font-semibold text-gray-900 mb-4">Key Metrics</h3>
                                            <div className="space-y-3">
                                                <div className="flex justify-between items-center">
                                                    <div>
                                                        <span className="text-sm text-gray-600">Cadence</span>
                                                        <div className="text-xs text-gray-500">Target: 170-180</div>
                                                    </div>
                                                    <div className="text-right">
                                                        <span className="font-semibold text-green-600">176</span>
                                                        <span className="text-xs text-gray-500 ml-1">spm</span>
                                                    </div>
                                                </div>
                                                
                                                <div className="flex justify-between items-center">
                                                    <div>
                                                        <span className="text-sm text-gray-600">Stride Length</span>
                                                        <div className="text-xs text-gray-500">Target: 1.1-1.3m</div>
                                                    </div>
                                                    <div className="text-right">
                                                        <span className="font-semibold text-green-600">1.24</span>
                                                        <span className="text-xs text-gray-500 ml-1">m</span>
                                                    </div>
                                                </div>
                                                
                                                <div className="flex justify-between items-center">
                                                    <div>
                                                        <span className="text-sm text-gray-600">Vertical Oscillation</span>
                                                        <div className="text-xs text-gray-500">Target: <10cm</div>
                                                    </div>
                                                    <div className="text-right">
                                                        <span className="font-semibold text-green-600">8.2</span>
                                                        <span className="text-xs text-gray-500 ml-1">cm</span>
                                                    </div>
                                                </div>
                                                
                                                <div className="flex justify-between items-center">
                                                    <div>
                                                        <span className="text-sm text-gray-600">Ground Contact</span>
                                                        <div className="text-xs text-gray-500">Target: <250ms</div>
                                                    </div>
                                                    <div className="text-right">
                                                        <span className="font-semibold text-yellow-600">248</span>
                                                        <span className="text-xs text-gray-500 ml-1">ms</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Insights */}
                                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                            <h4 className="font-medium text-blue-900 mb-2">💡 Key Insight</h4>
                                            <p className="text-sm text-blue-700">
                                                Your running form shows excellent efficiency with optimal cadence and stride length. 
                                                Consider focusing on reducing ground contact time for improved speed.
                                            </p>
                                        </div>

                                        {/* Analysis Info */}
                                        <div className="bg-gray-50 rounded-lg p-4 text-xs text-gray-600">
                                            <div className="flex justify-between mb-1">
                                                <span>Analysis ID:</span>
                                                <span className="font-mono">d9539cf3-e5ef</span>
                                            </div>
                                            <div className="flex justify-between mb-1">
                                                <span>Model:</span>
                                                <span>BlazePose Full (39 points)</span>
                                            </div>
                                            <div className="flex justify-between">
                                                <span>Processing Time:</span>
                                                <span>47 seconds</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // Render the app
        ReactDOM.render(<BiometricsPage />, document.getElementById('root'));
    </script>
</body>
</html>