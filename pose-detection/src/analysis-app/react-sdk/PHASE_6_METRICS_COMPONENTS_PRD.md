# Phase 6: Metrics Components - Product Requirements Document

**Version:** 1.0  
**Date:** 2025-07-30  
**Status:** Ready for Implementation  
**Dependencies:** Phase 7 (Utilities) Complete  

## 🎯 Overview

Phase 6 focuses on creating the metrics display and tab container components that will present biomechanical analysis results to users. Based on the cross-examination findings, these components must match the sophisticated display system in the reference implementation while integrating with our React architecture.

## 📋 Components to Implement

### 1. MetricsDisplay Component
**File:** `components/MetricsDisplay.tsx`  
**Purpose:** Comprehensive metrics visualization and analysis presentation  

### 2. TabsContainer Component  
**File:** `components/TabsContainer.tsx`  
**Purpose:** Side/Rear view switching with visual indicators  

### 3. HeightInputSystem Component  
**File:** `components/HeightInputSystem.tsx`  
**Purpose:** User height input with imperial/metric toggle (CRITICAL MISSING)  

### 4. StatusDisplay Component  
**File:** `components/StatusDisplay.tsx`  
**Purpose:** Real-time processing status and user feedback (CRITICAL MISSING)  

### 5. DebugPanel Component  
**File:** `components/DebugPanel.tsx`  
**Purpose:** Development and troubleshooting information display  

## 🔍 Reference Analysis

Based on `dev-app-smoothnet.html` analysis:

### Height Input System (Lines 320-370)
```javascript
// Reference implementation features:
// - Imperial/metric toggle
// - Validation ranges (3-8 feet, 90-250 cm)
// - Real-time conversion display
// - Integration with CoordinateScaler
// - Visual feedback for valid/invalid input
```

### Metrics Display (Lines 1288-1336)
```javascript
// Reference shows:
// - Real-time stride length calculations
// - Step height measurements
// - Running metrics overlay on canvas
// - Imperial/metric unit display
// - Color-coded status indicators
```

### Status System (Lines 105-190)
```javascript
// Comprehensive status management:
// - Processing stages indicator
// - Error state display
// - User instruction messaging
// - Progress feedback
// - Connection status
```

## 📐 Detailed Component Specifications

## 1. MetricsDisplay Component

### Features Required:
- **Real-time metrics display** from useRunningMetrics hook
- **Side/Rear view specific calculations**
- **Color-coded status indicators** (excellent/good/needs-work)
- **Unit conversion support** (metric/imperial)
- **Historical comparison** (if data available)
- **Export functionality** integration

### Props Interface:
```typescript
interface MetricsDisplayProps {
  metrics: RunningMetrics | null;
  viewType: 'side' | 'rear';
  userProfile: UserProfile;
  isCalibrating: boolean;
  calibrationProgress: number;
  onExport?: (format: 'pdf' | 'json' | 'csv') => void;
  showDebugInfo?: boolean;
}
```

### Layout Structure:
```typescript
// Main container with responsive grid
// - Overall performance score (prominent)
// - Core metrics cards (cadence, stride, etc.)
// - Detailed analysis section
// - Recommendations panel
// - Export controls
```

### Styling Requirements:
- **Responsive design** (mobile-first)
- **Color-coded metrics** based on score ranges
- **Smooth animations** for value changes
- **Accessible contrast ratios**
- **Print-friendly layouts** for export

## 2. TabsContainer Component

### Features Required:
- **Side/Rear view switching**
- **Visual indicators** for active tab
- **Progress indicators** per view
- **Disabled state handling** (if view not available)
- **Keyboard navigation** support

### Props Interface:
```typescript
interface TabsContainerProps {
  activeTab: 'side' | 'rear';
  onTabChange: (tab: 'side' | 'rear') => void;
  sideViewStatus?: 'available' | 'processing' | 'unavailable';
  rearViewStatus?: 'available' | 'processing' | 'unavailable';
  sideViewProgress?: number;
  rearViewProgress?: number;
  children: React.ReactNode;
}
```

### Tab Features:
- **Visual status indicators**
- **Progress bars** for processing views
- **Tooltips** explaining view types
- **Badge indicators** for completion status

## 3. HeightInputSystem Component

### Features Required (CRITICAL - Missing from React SDK):
- **Imperial/Metric toggle** (feet+inches / cm)
- **Input validation** (3-8 feet, 90-250 cm)
- **Real-time conversion** display
- **Visual feedback** for valid/invalid ranges
- **Integration** with CoordinateScaler
- **Accessibility** compliance

### Props Interface:
```typescript
interface HeightInputSystemProps {
  userProfile: UserProfile;
  onProfileUpdate: (updates: Partial<UserProfile>) => void;
  isCalibrating: boolean;
  calibrationStatus: 'pending' | 'success' | 'failed';
  disabled?: boolean;
}
```

### Validation Rules:
- **Imperial:** 3'0" to 8'0" (36-96 inches)
- **Metric:** 90cm to 250cm
- **Real-time feedback** with color coding
- **Error messaging** for invalid inputs

## 4. StatusDisplay Component

### Features Required (CRITICAL - Missing from React SDK):
- **Processing stage indicators**
- **Error state display** with actions
- **User instruction messaging**
- **Progress feedback** with percentages
- **Connection status** indicators
- **Dismissible notifications**

### Props Interface:
```typescript
interface StatusDisplayProps {
  status: ProcessingStatus;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
}

interface ProcessingStatus {
  stage: 'idle' | 'loading' | 'processing' | 'complete' | 'error';
  message: string;
  progress?: number;
  error?: {
    type: 'network' | 'validation' | 'processing' | 'unknown';
    message: string;
    actionable: boolean;
  };
}
```

### Status Types:
- **Loading states** with spinners
- **Progress indicators** with time estimates
- **Success states** with checkmarks
- **Error states** with retry actions
- **Warning states** with user guidance

## 5. DebugPanel Component

### Features Required:
- **Pose data inspection**
- **Coordinate validation display**
- **Frame processing metrics**
- **Performance monitoring**
- **Console log integration**
- **Collapsible interface**

### Props Interface:
```typescript
interface DebugPanelProps {
  isVisible: boolean;
  onToggle: () => void;
  poseData?: ProcessedPoseData;
  metrics?: RunningMetrics;
  calibrationStatus?: CalibrationResult;
  frameRate?: number;
  memoryUsage?: number;
}
```

## 🎨 Design System Integration

### Color Palette:
```css
/* Status Colors */
--excellent: #10B981 (green)
--good: #F59E0B (amber)
--needs-work: #EF4444 (red)
--processing: #3B82F6 (blue)
--disabled: #9CA3AF (gray)

/* Background Colors */
--primary-bg: #FFFFFF
--secondary-bg: #F9FAFB
--accent-bg: #FEF3C7 (amber light)
--error-bg: #FEF2F2 (red light)
```

### Typography Scale:
```css
/* Metric Values */
--metric-primary: 2.5rem font-bold
--metric-secondary: 1.5rem font-semibold
--metric-label: 0.85rem font-medium

/* Status Text */
--status-primary: 1rem font-medium
--status-secondary: 0.875rem font-normal
```

## 📱 Responsive Behavior

### Desktop (>1024px):
- **3-column layout** (tabs, metrics, details)
- **Side-by-side comparisons** for side/rear views
- **Expanded metrics cards** with detailed breakdowns
- **Debug panel overlay** available

### Tablet (768px-1024px):
- **2-column layout** (metrics + details)
- **Stacked tab navigation**
- **Condensed metrics cards**
- **Collapsible sections**

### Mobile (<768px):
- **Single column layout**
- **Horizontal tab scrolling**
- **Simplified metrics display**
- **Bottom sheet modals** for details

## 🔗 Integration Requirements

### Hook Integration:
```typescript
// MetricsDisplay usage
const {
  metrics,
  isCalibrating,
  calibrationProgress,
  facingDirection
} = useRunningMetrics({
  userProfile,
  videoWidth: 1080,
  videoHeight: 1920,
  autoDetectFacing: true
});
```

### Data Flow:
```
ResultsPage → TabsContainer → MetricsDisplay
     ↓              ↓              ↓
StatusDisplay → useRunningMetrics → CoordinateScaler
```

## ⚡ Performance Requirements

### Rendering Performance:
- **60fps animations** for metric value changes
- **Debounced updates** (max 10fps for live metrics)
- **Memoized components** to prevent unnecessary re-renders
- **Lazy loading** for non-visible content

### Memory Management:
- **Component cleanup** on unmount
- **Event listener cleanup**
- **Animation frame cancellation**
- **Large data structure disposal**

## 🧪 Testing Strategy

### Unit Tests:
- **Component rendering** with various prop combinations
- **User interaction handling** (clicks, keyboard)
- **Data transformation** accuracy
- **Responsive behavior** validation

### Integration Tests:
- **Hook integration** with real data
- **Tab switching** functionality
- **Status display** updates
- **Export functionality**

### Visual Regression Tests:
- **Screenshot comparisons** across screen sizes
- **Theme consistency** validation
- **Accessibility compliance** checking

## 📚 Implementation Priority

### Phase 6.1 - Critical Components (Week 1)
1. **HeightInputSystem** - Essential for calibration
2. **StatusDisplay** - User feedback is critical
3. **TabsContainer** - Core navigation

### Phase 6.2 - Core Metrics (Week 2)
1. **MetricsDisplay** - Main feature component
2. **Integration testing** with existing hooks
3. **Responsive layout** implementation

### Phase 6.3 - Enhancement (Week 3)
1. **DebugPanel** - Development tools
2. **Export functionality** - User value
3. **Performance optimization**

## 📊 Success Criteria

### Functional Requirements:
- [ ] All 5 components render without errors
- [ ] Height input system fully functional
- [ ] Status display shows all processing states
- [ ] Metrics display updates in real-time
- [ ] Tab switching works smoothly
- [ ] Debug panel provides useful information

### Quality Requirements:
- [ ] Responsive across all screen sizes
- [ ] Accessible (WCAG 2.1 AA compliance)
- [ ] Performance (60fps animations)
- [ ] Type safety (100% TypeScript coverage)
- [ ] Testing (>90% code coverage)

### User Experience:
- [ ] Intuitive navigation
- [ ] Clear visual feedback
- [ ] Helpful error messages
- [ ] Smooth transitions
- [ ] Professional appearance

## 🚀 Implementation Notes

### Critical Dependencies:
- **Phase 7 utilities** must be complete
- **CROSSCHECK_REACT_SDK.md** gaps must be addressed
- **Portrait video fixes** from cross-examination
- **Modal data integration** for proper architecture

### Risk Mitigation:
- **Progressive enhancement** - start with basic features
- **Fallback components** for missing data
- **Error boundaries** for component failures
- **Performance monitoring** during development

---

**Next Steps:** Begin implementation with Phase 6.1 critical components, addressing cross-examination findings simultaneously.