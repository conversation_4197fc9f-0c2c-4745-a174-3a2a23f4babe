/**
 * useRunningMetrics Hook
 * 
 * React hook for calculating running metrics from pose data
 * Integrates CoordinateScaler and RunningMetricsCalculator
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { ProcessedPoseData } from '../types/pose';
import { RunningMetrics } from '../types/metrics';
import { RunningMetricsCalculator, UserProfile } from '../utils';

export interface UseRunningMetricsOptions {
  userProfile: UserProfile;
  videoWidth: number;
  videoHeight: number;
  fps?: number;
  autoDetectFacing?: boolean;
}

export interface UseRunningMetricsResult {
  metrics: RunningMetrics | null;
  isCalibrating: boolean;
  calibrationProgress: number;
  facingDirection: 'left' | 'right' | 'unknown';
  addFrame: (poseData: ProcessedPoseData) => void;
  updateUserProfile: (updates: Partial<UserProfile>) => void;
  reset: () => void;
}

export function useRunningMetrics(options: UseRunningMetricsOptions): UseRunningMetricsResult {
  const [metrics, setMetrics] = useState<RunningMetrics | null>(null);
  const [isCalibrating, setIsCalibrating] = useState(true);
  const [calibrationProgress, setCalibrationProgress] = useState(0);
  const [facingDirection, setFacingDirection] = useState<'left' | 'right' | 'unknown'>('unknown');
  
  const calculatorRef = useRef<RunningMetricsCalculator | null>(null);
  const frameCountRef = useRef(0);
  
  // Initialize calculator
  useEffect(() => {
    calculatorRef.current = new RunningMetricsCalculator({
      userProfile: options.userProfile,
      fps: options.fps || 30
    });
    
    return () => {
      calculatorRef.current = null;
    };
  }, []); // Only initialize once
  
  // Update user profile when it changes
  useEffect(() => {
    if (calculatorRef.current) {
      calculatorRef.current.updateUserProfile(options.userProfile);
    }
  }, [options.userProfile]);
  
  /**
   * Detect facing direction from pose keypoints
   */
  const detectFacingDirection = useCallback((keypoints: any[]): 'left' | 'right' | 'unknown' => {
    const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');
    const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');
    const nose = keypoints.find(kp => kp.name === 'nose');
    
    if (!leftShoulder || !rightShoulder || !nose) {
      return 'unknown';
    }
    
    // Calculate shoulder midpoint
    const shoulderMidX = (leftShoulder.x + rightShoulder.x) / 2;
    
    // If nose is to the left of shoulder midpoint, person is facing left
    if (nose.x < shoulderMidX - 10) {
      return 'left';
    } else if (nose.x > shoulderMidX + 10) {
      return 'right';
    }
    
    // Check shoulder visibility as secondary indicator
    if (leftShoulder.score > rightShoulder.score + 0.1) {
      return 'right'; // Left shoulder more visible = facing right
    } else if (rightShoulder.score > leftShoulder.score + 0.1) {
      return 'left'; // Right shoulder more visible = facing left
    }
    
    return 'unknown';
  }, []);
  
  /**
   * Add a new pose frame for analysis
   */
  const addFrame = useCallback((poseData: ProcessedPoseData) => {
    if (!calculatorRef.current) return;
    
    frameCountRef.current++;
    
    // Auto-detect facing direction if enabled and not yet detected
    if (options.autoDetectFacing && facingDirection === 'unknown' && poseData.keypoints) {
      const detected = detectFacingDirection(poseData.keypoints);
      if (detected !== 'unknown') {
        setFacingDirection(detected);
        // Update calculator with detected direction
        calculatorRef.current.updateUserProfile({
          ...options.userProfile,
          facingDirection: detected
        });
      }
    }
    
    // Add frame to calculator
    const newMetrics = calculatorRef.current.addFrame(poseData);
    
    // Update calibration status
    const calibrationStatus = calculatorRef.current['scaler'].getCalibrationStatus();
    setIsCalibrating(!calibrationStatus.calibrated);
    setCalibrationProgress(Math.min(100, (calibrationStatus.attempts / 150) * 100));
    
    // Update metrics if available
    if (newMetrics) {
      setMetrics(newMetrics);
    }
  }, [options.autoDetectFacing, options.userProfile, facingDirection, detectFacingDirection]);
  
  /**
   * Update user profile
   */
  const updateUserProfile = useCallback((updates: Partial<UserProfile>) => {
    if (calculatorRef.current) {
      calculatorRef.current.updateUserProfile(updates);
      
      // Update facing direction if provided
      if (updates.facingDirection) {
        setFacingDirection(updates.facingDirection);
      }
    }
  }, []);
  
  /**
   * Reset the calculator
   */
  const reset = useCallback(() => {
    if (calculatorRef.current) {
      calculatorRef.current.reset();
      setMetrics(null);
      setIsCalibrating(true);
      setCalibrationProgress(0);
      setFacingDirection('unknown');
      frameCountRef.current = 0;
    }
  }, []);
  
  return {
    metrics,
    isCalibrating,
    calibrationProgress,
    facingDirection,
    addFrame,
    updateUserProfile,
    reset
  };
}