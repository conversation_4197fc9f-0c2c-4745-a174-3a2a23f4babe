/**
 * useModalData Hook
 * 
 * React hook for loading and managing pre-processed Modal/SmoothNet data
 * This replaces real-time BlazePose detection with pre-processed results
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { ProcessedPoseData, BlazePoseKeypoint } from '../types/pose';

interface ModalFrameData {
  frameNumber: number;
  timestamp: number;
  keypoints: BlazePoseKeypoint[];
  score?: number;
}

interface ModalVideoData {
  video: string;
  videoWidth: number;
  videoHeight: number;
  fps: number;
  frames: ModalFrameData[];
  modelType: string;
  processingTime: string;
  totalFrames?: number;
  duration?: number;
}

interface UseModalDataOptions {
  poseDataUrl?: string;
  onDataLoaded?: (data: ModalVideoData) => void;
  onError?: (error: Error) => void;
  onStatusChange?: (status: { isLoading: boolean; message: string }) => void;
}

interface UseModalDataReturn {
  modalData: ModalVideoData | null;
  isLoading: boolean;
  error: Error | null;
  
  // Frame finding functions
  findFrameForTime: (currentTime: number) => ModalFrameData | null;
  findFrameByIndex: (index: number) => ModalFrameData | null;
  getValidFrames: () => ModalFrameData[];
  
  // Utility functions
  getTotalFrames: () => number;
  getFrameRate: () => number;
  getVideoDimensions: () => { width: number; height: number };
  
  // Data management
  refetch: () => Promise<void>;
  reset: () => void;
}

/**
 * Filter out invalid keypoints (with coordinates at 0,0)
 */
function getValidKeypoints(keypoints: BlazePoseKeypoint[]): BlazePoseKeypoint[] {
  return keypoints.map((kp, idx) => {
    // If both X and Y are 0, this is likely invalid data
    if (kp.x === 0.0 && kp.y === 0.0) {
      return {
        ...kp,
        x: 0.0,
        y: 0.0,
        score: 0.0, // Mark as invalid by setting score to 0
        isInvalid: true
      } as BlazePoseKeypoint & { isInvalid: boolean };
    }
    
    // If only X is 0 (but Y is valid), this might be edge detection issue
    if (kp.x === 0.0 && kp.y > 0.0) {
      return {
        ...kp,
        score: Math.max(0.1, kp.score * 0.5), // Reduce confidence
        isEdgeCase: true
      } as BlazePoseKeypoint & { isEdgeCase: boolean };
    }
    
    // If only Y is 0 (but X is valid), this might be edge detection issue  
    if (kp.y === 0.0 && kp.x > 0.0) {
      return {
        ...kp,
        score: Math.max(0.1, kp.score * 0.5), // Reduce confidence
        isEdgeCase: true
      } as BlazePoseKeypoint & { isEdgeCase: boolean };
    }
    
    // Valid coordinate
    return kp;
  });
}

/**
 * React hook for Modal data management
 */
export const useModalData = (options: UseModalDataOptions = {}): UseModalDataReturn => {
  const {
    poseDataUrl,
    onDataLoaded,
    onError,
    onStatusChange
  } = options;

  // State
  const [modalData, setModalData] = useState<ModalVideoData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Refs
  const isLoadingRef = useRef(false);

  /**
   * Load Modal processed data
   */
  const loadModalData = useCallback(async (dataUrl: string): Promise<ModalVideoData | null> => {
    if (isLoadingRef.current) {
      console.log('🔄 Modal data loading already in progress');
      return null;
    }

    try {
      isLoadingRef.current = true;
      setIsLoading(true);
      setError(null);
      
      onStatusChange?.({ isLoading: true, message: 'Loading Modal SmoothNet data...' });
      
      console.log('📡 Fetching Modal pre-processed results from:', dataUrl);
      
      const response = await fetch(dataUrl);
      if (!response.ok) {
        throw new Error(`Failed to load Modal data: ${response.status} ${response.statusText}`);
      }
      
      const data: ModalVideoData = await response.json();
      
      // Validate data structure
      if (!data.frames || !Array.isArray(data.frames)) {
        throw new Error('Invalid Modal data: missing frames array');
      }
      
      if (!data.videoWidth || !data.videoHeight) {
        throw new Error('Invalid Modal data: missing video dimensions');
      }
      
      // Process and validate keypoints for each frame
      const processedFrames = data.frames.map(frame => ({
        ...frame,
        keypoints: getValidKeypoints(frame.keypoints)
      }));
      
      const processedData: ModalVideoData = {
        ...data,
        frames: processedFrames,
        totalFrames: data.frames.length,
        duration: data.frames.length > 0 ? data.frames[data.frames.length - 1].timestamp : 0
      };
      
      setModalData(processedData);
      
      console.log('✅ Modal data loaded successfully', {
        video: data.video,
        dimensions: `${data.videoWidth}×${data.videoHeight}`,
        fps: data.fps,
        frames: data.frames.length,
        modelType: data.modelType,
        processingTime: data.processingTime
      });
      
      onDataLoaded?.(processedData);
      onStatusChange?.({ isLoading: false, message: 'Modal SmoothNet data loaded' });
      
      return processedData;
      
    } catch (err) {
      console.error('❌ Error loading Modal data:', err);
      const error = err instanceof Error ? err : new Error('Failed to load Modal data');
      setError(error);
      onError?.(error);
      onStatusChange?.({ isLoading: false, message: `Error: ${error.message}` });
      return null;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [onDataLoaded, onError, onStatusChange]);

  /**
   * Get valid frames (skip invalid frames like frame 0 with all zeros)
   */
  const getValidFrames = useCallback((): ModalFrameData[] => {
    if (!modalData || !modalData.frames) return [];
    
    // Skip invalid frames (like frame 0 with all zeros)
    return modalData.frames.filter(frame => {
      // Check if frame has at least some valid coordinates (not all zeros)
      const validKeypoints = frame.keypoints.filter(kp => 
        !(kp as any).isInvalid && (kp.x !== 0.0 || kp.y !== 0.0)
      );
      return validKeypoints.length > 5; // Need at least 5 non-zero keypoints
    });
  }, [modalData]);

  /**
   * Find frame data for current video time
   */
  const findFrameForTime = useCallback((currentTime: number): ModalFrameData | null => {
    const validFrames = getValidFrames();
    if (validFrames.length === 0) {
      return null;
    }
    
    // Find frame closest to current video time from valid frames only
    let closestFrame = null;
    let minDiff = Infinity;
    
    for (const frame of validFrames) {
      const diff = Math.abs(frame.timestamp - currentTime);
      if (diff < minDiff) {
        minDiff = diff;
        closestFrame = frame;
      }
    }
    
    return closestFrame;
  }, [getValidFrames]);

  /**
   * Find frame by index
   */
  const findFrameByIndex = useCallback((index: number): ModalFrameData | null => {
    const validFrames = getValidFrames();
    if (validFrames.length === 0) return null;
    
    // Clamp index to valid range
    const clampedIndex = Math.max(0, Math.min(index, validFrames.length - 1));
    return validFrames[clampedIndex];
  }, [getValidFrames]);
  
  /**
   * Get total number of frames
   */
  const getTotalFrames = useCallback((): number => {
    return modalData?.totalFrames || modalData?.frames?.length || 0;
  }, [modalData]);
  
  /**
   * Get frame rate
   */
  const getFrameRate = useCallback((): number => {
    return modalData?.fps || 30;
  }, [modalData]);
  
  /**
   * Get video dimensions
   */
  const getVideoDimensions = useCallback((): { width: number; height: number } => {
    return {
      width: modalData?.videoWidth || 0,
      height: modalData?.videoHeight || 0
    };
  }, [modalData]);

  /**
   * Refetch data
   */
  const refetch = useCallback(async (): Promise<void> => {
    if (poseDataUrl) {
      await loadModalData(poseDataUrl);
    }
  }, [poseDataUrl, loadModalData]);

  /**
   * Reset state
   */
  const reset = useCallback(() => {
    setModalData(null);
    setError(null);
    isLoadingRef.current = false;
    console.log('🔄 Modal data reset');
  }, []);

  /**
   * Load data when URL changes
   */
  useEffect(() => {
    if (poseDataUrl) {
      loadModalData(poseDataUrl);
    }
  }, [poseDataUrl, loadModalData]);

  return {
    modalData,
    isLoading,
    error,
    
    // Frame finding functions
    findFrameForTime,
    findFrameByIndex,
    getValidFrames,
    
    // Utility functions
    getTotalFrames,
    getFrameRate,
    getVideoDimensions,
    
    // Data management
    refetch,
    reset
  };
};