/**
 * useAnalysisData Hook
 * 
 * React hook for managing analysis data state and operations
 * Integrates with Supabase and provides data transformation utilities
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  UseAnalysisDataReturn
} from '../types/components';
import { 
  AnalysisData,
  ProcessingStatus,
  ViewType 
} from '../types/analysis';
import { BioRunAnalysis, BioRunVideo } from '../types/supabase';

interface UseAnalysisDataOptions {
  analysisId?: string;
  userEmail?: string;
  autoRefresh?: boolean;
  refreshInterval?: number;
  onStatusChange?: (status: ProcessingStatus) => void;
  onError?: (error: Error) => void;
}

/**
 * Transform Supabase analysis data to SDK format
 */
function transformAnalysisData(
  analysis: BioRunAnalysis,
  sideVideo?: BioRunVideo,
  rearVideo?: BioRunVideo
): AnalysisData {
  return {
    id: analysis.id,
    userId: analysis.user_email, // Using email as user ID temporarily
    userEmail: analysis.user_email,
    createdAt: analysis.created_at,
    updatedAt: analysis.updated_at,
    
    // User configuration
    userConfig: {
      height: {
        feet: Math.floor(analysis.height_inches / 12),
        inches: Math.round(analysis.height_inches % 12),
        totalInches: analysis.height_inches,
        meters: analysis.height_inches * 0.0254
      },
      gender: analysis.gender,
      weight: analysis.weight_lbs,
      weightUnit: 'lbs' as const
    },
    
    // Analysis configuration - inferred from data
    analysisConfig: {
      analysisType: 'running' as const,
      analysisMode: '3D' as const,
      activityType: 'Running',
      videoSetup: 'treadmill' as const,
      overlayStyle: 'medical' as const,
      analysisQuality: analysis.model_version === 'blazepose_heavy' ? 'full' as const : 'standard' as const,
      viewType: sideVideo && rearVideo ? 'side' as const : sideVideo ? 'side' as const : 'rear' as const
    },
    
    // Videos
    videos: [
      ...(sideVideo ? [{
        id: sideVideo.id,
        analysisId: analysis.id,
        viewType: 'side' as ViewType,
        originalVideoUrl: sideVideo.s3_url,
        processedVideoUrl: sideVideo.s3_url, // Same for now
        poseDataUrl: analysis.side_metrics_s3_url || '',
        thumbnailUrl: undefined,
        duration: sideVideo.duration_seconds,
        fps: sideVideo.fps,
        width: sideVideo.width,
        height: sideVideo.height,
        frameCount: Math.floor(sideVideo.duration_seconds * sideVideo.fps)
      }] : []),
      ...(rearVideo ? [{
        id: rearVideo.id,
        analysisId: analysis.id,
        viewType: 'rear' as ViewType,
        originalVideoUrl: rearVideo.s3_url,
        processedVideoUrl: rearVideo.s3_url, // Same for now
        poseDataUrl: analysis.rear_metrics_s3_url || '',
        thumbnailUrl: undefined,
        duration: rearVideo.duration_seconds,
        fps: rearVideo.fps,
        width: rearVideo.width,
        height: rearVideo.height,
        frameCount: Math.floor(rearVideo.duration_seconds * rearVideo.fps)
      }] : [])
    ],
    
    // Metrics - simplified for now
    metrics: {
      sideViewMetrics: analysis.side_analysis_json ? {
        strideLength: {
          value: analysis.stride_length_avg || 0,
          unit: 'm',
          score: 0.8,
          status: 'good' as const,
          description: 'Average stride length during analysis'
        },
        cadence: {
          value: analysis.cadence_avg || 0,
          unit: 'steps/min',
          score: 0.8,
          status: 'good' as const,
          description: 'Running cadence (steps per minute)'
        },
        verticalOscillation: {
          value: analysis.vertical_oscillation_avg || 0,
          unit: 'cm',
          score: 0.8,
          status: 'good' as const,
          description: 'Vertical bouncing motion while running'
        },
        forwardLean: {
          value: analysis.lean_angle_avg || 0,
          unit: 'degrees',
          score: 0.8,
          status: 'good' as const,
          description: 'Forward torso lean angle'
        },
        groundContactTime: {
          value: analysis.ground_contact_time_avg || 0,
          unit: 'ms',
          score: 0.8,
          status: 'good' as const,
          description: 'Time foot spends in contact with ground'
        },
        kneeFlexion: {
          value: 0,
          unit: 'degrees',
          score: 0.8,
          status: 'good' as const,
          description: 'Maximum knee bend during swing phase'
        },
        footStrike: {
          type: 'midfoot' as const,
          angle: 0,
          consistency: 0.8,
          score: 0.8,
          status: 'good' as const,
          description: 'Midfoot strike pattern with good consistency'
        }
      } : undefined,
      overallScore: 0.8,
      performanceLevel: 'intermediate' as const,
      injuryRisk: 'low' as const,
      efficiency: 0.8
    },
    
    // Insights and recommendations - placeholder
    insights: [],
    recommendations: [],
    
    // Processing info
    processingTime: analysis.processing_duration_seconds || 0,
    modelVersion: analysis.model_version || 'blazepose_full',
    status: analysis.status === 'completed' ? 'completed' as const :
            analysis.status === 'processing_side' || analysis.status === 'processing_rear' ? 'processing' as const :
            analysis.status === 'error' ? 'failed' as const : 'pending' as const,
    error: analysis.error_message
  };
}

/**
 * React hook for analysis data management
 */
export const useAnalysisData = (options: UseAnalysisDataOptions = {}): UseAnalysisDataReturn => {
  const {
    analysisId,
    userEmail,
    autoRefresh = true,
    refreshInterval = 5000, // 5 seconds
    onError
  } = options;

  // State
  const [analysis, setAnalysis] = useState<AnalysisData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  // Refs
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isLoadingRef = useRef(false);

  /**
   * Fetch analysis data
   */
  const fetchAnalysis = useCallback(async (id: string): Promise<AnalysisData | null> => {
    if (isLoadingRef.current) {
      console.log('🔄 Analysis fetch already in progress');
      return null;
    }

    try {
      isLoadingRef.current = true;
      setIsLoading(true);
      setError(null);

      console.log('📡 Fetching analysis data:', id);

      // This would normally be a Supabase call
      // For now, return mock data or throw error if no analysis found
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock analysis data - in real implementation, this would come from Supabase
      const mockAnalysis: AnalysisData = {
        id,
        userId: userEmail || 'test-user',
        userEmail: userEmail || '<EMAIL>',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        
        userConfig: {
          height: { feet: 5, inches: 10, totalInches: 70, meters: 1.78 },
          gender: 'male',
          weight: 150,
          weightUnit: 'lbs'
        },
        
        analysisConfig: {
          analysisType: 'running',
          analysisMode: '3D',
          activityType: 'Running',
          videoSetup: 'treadmill',
          overlayStyle: 'medical',
          analysisQuality: 'full',
          viewType: 'side'
        },
        
        videos: [{
          id: 'video-1',
          analysisId: id,
          viewType: 'side',
          originalVideoUrl: './Michael_test_side.mp4',
          processedVideoUrl: './Michael_test_side.mp4',
          poseDataUrl: './d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json',
          duration: 10,
          fps: 30,
          width: 1080,
          height: 1920,
          frameCount: 300
        }],
        
        metrics: {
          overallScore: 0.85,
          performanceLevel: 'intermediate',
          injuryRisk: 'low',
          efficiency: 0.82
        },
        
        insights: [],
        recommendations: [],
        
        processingTime: 45,
        modelVersion: 'blazepose_full',
        status: 'completed'
      };

      setAnalysis(mockAnalysis);
      console.log('✅ Analysis data loaded successfully');
      return mockAnalysis;

    } catch (err) {
      console.error('❌ Error fetching analysis:', err);
      const error = err instanceof Error ? err : new Error('Failed to fetch analysis');
      setError(error);
      onError?.(error);
      return null;
    } finally {
      setIsLoading(false);
      isLoadingRef.current = false;
    }
  }, [userEmail, onError]);

  /**
   * Refetch current analysis
   */
  const refetch = useCallback(() => {
    if (analysisId) {
      fetchAnalysis(analysisId);
    }
  }, [analysisId, fetchAnalysis]);

  /**
   * Update analysis with partial data
   */
  const updateAnalysis = useCallback(async (updates: Partial<AnalysisData>): Promise<void> => {
    if (!analysis) {
      throw new Error('No analysis loaded to update');
    }

    try {
      console.log('💾 Updating analysis:', updates);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Update local state
      setAnalysis((prev: AnalysisData | null) => prev ? { ...prev, ...updates } : null);
      
      console.log('✅ Analysis updated successfully');
    } catch (err) {
      console.error('❌ Error updating analysis:', err);
      const error = err instanceof Error ? err : new Error('Failed to update analysis');
      setError(error);
      onError?.(error);
      throw error;
    }
  }, [analysis, onError]);

  /**
   * Setup auto-refresh for processing analyses
   */
  useEffect(() => {
    if (!autoRefresh || !analysisId || !analysis) return;
    
    // Only auto-refresh if analysis is still processing
    if (analysis.status === 'processing' || analysis.status === 'pending') {
      refreshTimeoutRef.current = setTimeout(() => {
        refetch();
      }, refreshInterval);
    }

    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [autoRefresh, analysisId, analysis, refreshInterval, refetch]);

  /**
   * Initial load
   */
  useEffect(() => {
    if (analysisId) {
      fetchAnalysis(analysisId);
    }
  }, [analysisId, fetchAnalysis]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, []);

  return {
    analysis,
    isLoading,
    error,
    refetch,
    updateAnalysis
  };
};