/**
 * useBlazePose Hook
 * 
 * React hook for integrating BlazePose pose detection
 * with comprehensive tensor memory management and error handling
 */

import { useState, useEffect, useRef, useCallback } from 'react';
import { 
  UseBlazePoseReturn
} from '../types/components';
import {
  BlazePoseModelType,
  ProcessedPoseData
} from '../types/pose';

// Import the actual BlazePose implementation
// We'll need to access the existing implementation from the parent directory
declare global {
  interface Window {
    tf: any;
  }
}

// Re-create the essential interfaces to avoid circular dependencies
interface BlazePoseModelOptions {
  runtime: 'tfjs';
  modelType: BlazePoseModelType;
  enableSmoothing?: boolean;
  enableSegmentation?: boolean;
  detectorModelUrl?: string;
  landmarkModelUrl?: string;
}

interface BlazePoseEstimateOptions {
  maxPoses?: number;
  flipHorizontal?: boolean;
}

interface BlazePoseDetector {
  estimatePoses(
    input: HTMLVideoElement | HTMLImageElement | HTMLCanvasElement,
    config?: BlazePoseEstimateOptions,
    timestamp?: number
  ): Promise<any[]>;
  dispose(): void;
  reset?(): void;
}

interface UseBlazePoseOptions {
  modelType?: BlazePoseModelType;
  enableSmoothing?: boolean;
  enableSegmentation?: boolean;
  maxPoses?: number;
  flipHorizontal?: boolean;
  onPoseData?: (data: ProcessedPoseData) => void;
  onError?: (error: Error) => void;
  onStatusChange?: (status: { isLoading: boolean; message: string }) => void;
}

/**
 * Default BlazePose model configuration
 */
const DEFAULT_MODEL_CONFIG: BlazePoseModelOptions = {
  runtime: 'tfjs',
  modelType: 'full', // Use Full model for 39 keypoints
  enableSmoothing: true,
  enableSegmentation: false
};

/**
 * Default estimation configuration
 */
const DEFAULT_ESTIMATION_CONFIG: BlazePoseEstimateOptions = {
  maxPoses: 1,
  flipHorizontal: false
};

/**
 * BlazePose keypoint names for reference
 */
const BLAZEPOSE_KEYPOINT_NAMES = [
  'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
  'right_eye_inner', 'right_eye', 'right_eye_outer',
  'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
  'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
  'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
  'left_index', 'right_index', 'left_thumb', 'right_thumb',
  'left_hip', 'right_hip', 'left_knee', 'right_knee',
  'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
  'left_foot_index', 'right_foot_index',
  // Additional 6 keypoints for Full model
  'left_wrist_pinky', 'left_wrist_index', 'left_wrist_thumb',
  'right_wrist_pinky', 'right_wrist_index', 'right_wrist_thumb'
];

/**
 * React hook for BlazePose pose detection
 */
export const useBlazePose = (options: UseBlazePoseOptions = {}): UseBlazePoseReturn => {
  const {
    modelType = 'full',
    enableSmoothing = true,
    enableSegmentation = false,
    maxPoses = 1,
    flipHorizontal = false,
    onPoseData,
    onError,
    onStatusChange
  } = options;

  // State
  const [isLoading, setIsLoading] = useState(false);
  const [isReady, setIsReady] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Refs
  const detectorRef = useRef<BlazePoseDetector | null>(null);
  const frameCountRef = useRef(0);
  const isInitializingRef = useRef(false);

  /**
   * Initialize BlazePose detector
   */
  const initializeDetector = useCallback(async () => {
    if (isInitializingRef.current) {
      console.log('🔄 BlazePose initialization already in progress');
      return;
    }

    try {
      isInitializingRef.current = true;
      setIsLoading(true);
      setError(null);
      
      onStatusChange?.({ isLoading: true, message: 'Loading BlazePose model...' });

      console.log('🚀 Initializing BlazePose detector...', {
        modelType,
        enableSmoothing,
        enableSegmentation
      });

      // Dynamic import to avoid circular dependencies
      const poseDetection = await import('../../../index');
      
      const modelConfig: BlazePoseModelOptions = {
        ...DEFAULT_MODEL_CONFIG,
        modelType,
        enableSmoothing,
        enableSegmentation
      };

      const detector = await poseDetection.createDetector(
        poseDetection.SupportedModels.BlazePose,
        modelConfig
      ) as BlazePoseDetector;

      detectorRef.current = detector;
      setIsReady(true);
      setIsLoading(false);
      isInitializingRef.current = false;

      console.log('✅ BlazePose detector initialized successfully');
      onStatusChange?.({ isLoading: false, message: 'BlazePose ready' });

    } catch (err) {
      console.error('❌ Failed to initialize BlazePose detector:', err);
      
      const error = err instanceof Error ? err : new Error('Failed to initialize BlazePose');
      setError(error);
      setIsLoading(false);
      setIsReady(false);
      isInitializingRef.current = false;

      onError?.(error);
      onStatusChange?.({ isLoading: false, message: `Error: ${error.message}` });
    }
  }, [modelType, enableSmoothing, enableSegmentation, onStatusChange, onError]);

  /**
   * Detect pose from video element
   */
  const detectPose = useCallback(async (
    video: HTMLVideoElement
  ): Promise<ProcessedPoseData | null> => {
    if (!detectorRef.current || !isReady) {
      console.warn('⚠️ BlazePose detector not ready');
      return null;
    }

    if (!video || video.readyState < 2) {
      console.warn('⚠️ Video element not ready');
      return null;
    }

    try {
      frameCountRef.current += 1;
      const timestamp = video.currentTime * 1000; // Convert to milliseconds
      
      const estimationConfig: BlazePoseEstimateOptions = {
        maxPoses,
        flipHorizontal
      };

      const poses = await detectorRef.current.estimatePoses(
        video,
        estimationConfig,
        timestamp
      );

      if (poses.length === 0) {
        // Log occasionally, not for every frame
        if (frameCountRef.current % 30 === 0) {
          console.log(`⚠️ No poses detected in frame ${frameCountRef.current}`);
        }
        return null;
      }

      const pose = poses[0];
      
      // Validate keypoint count for BlazePose Full (39 keypoints expected)
      const expectedKeypoints = modelType === 'full' ? 39 : 33;
      if (pose.keypoints.length !== expectedKeypoints) {
        console.warn(`⚠️ Expected ${expectedKeypoints} keypoints, got ${pose.keypoints.length}`);
      }

      // Create processed pose data
      const processedPose: ProcessedPoseData = {
        keypoints: pose.keypoints.map((kp: any, index: number) => ({
          x: kp.x,
          y: kp.y,
          z: kp.z,
          score: kp.score || 0,
          name: BLAZEPOSE_KEYPOINT_NAMES[index] || `keypoint_${index}`,
          visibility: kp.visibility,
          presence: kp.presence
        })),
        keypoints3D: pose.keypoints3D?.map((kp: any, index: number) => ({
          x: kp.x,
          y: kp.y,
          z: kp.z,
          score: kp.score || 0,
          name: BLAZEPOSE_KEYPOINT_NAMES[index] || `keypoint_${index}`
        })),
        score: pose.score || 0,
        timestamp,
        frameNumber: frameCountRef.current,
        modelType: modelType,
        smoothed: enableSmoothing
      };

      // Log progress every 30 frames
      if (frameCountRef.current % 30 === 0) {
        console.log(`📊 Processed ${frameCountRef.current} frames, score: ${pose.score?.toFixed(3)}`);
      }

      onPoseData?.(processedPose);
      return processedPose;

    } catch (err) {
      console.error('❌ Error detecting pose:', err);
      const error = err instanceof Error ? err : new Error('Pose detection failed');
      setError(error);
      onError?.(error);
      return null;
    }
  }, [isReady, maxPoses, flipHorizontal, modelType, enableSmoothing, onPoseData, onError]);

  /**
   * Reset detector state
   */
  const reset = useCallback(() => {
    if (detectorRef.current && detectorRef.current.reset) {
      detectorRef.current.reset();
    }
    frameCountRef.current = 0;
    setError(null);
    console.log('🔄 BlazePose detector reset');
  }, []);

  /**
   * Dispose detector and cleanup
   */
  const dispose = useCallback(() => {
    if (detectorRef.current) {
      try {
        detectorRef.current.dispose();
        console.log('🗑️ BlazePose detector disposed');
      } catch (err) {
        console.warn('⚠️ Error disposing detector:', err);
      }
      detectorRef.current = null;
    }
    setIsReady(false);
    setError(null);
    frameCountRef.current = 0;
    isInitializingRef.current = false;
  }, []);

  /**
   * Initialize detector on mount
   */
  useEffect(() => {
    initializeDetector();
    
    return () => {
      dispose();
    };
  }, [initializeDetector, dispose]);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      dispose();
    };
  }, [dispose]);

  return {
    isLoading,
    isReady,
    error,
    detectPose,
    detector: detectorRef.current,
    reset,
    dispose,
    
    // Additional state info
    frameCount: frameCountRef.current,
    modelInfo: {
      type: modelType,
      smoothing: enableSmoothing,
      segmentation: enableSegmentation,
      expectedKeypoints: modelType === 'full' ? 39 : 33
    }
  };
};