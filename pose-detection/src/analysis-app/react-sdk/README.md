# Peak Insight Pose Analysis SDK

A React SDK for integrating BlazePose biomechanical analysis into Next.js applications.

## Overview

This SDK provides React components and hooks for displaying biomechanical analysis results from BlazePose pose detection. It's designed to integrate seamlessly with the Peak Insight main application while maintaining clean separation of concerns.

## Installation & Usage

### In your Next.js app:

```typescript
import { 
  ResultsPage, 
  useBlazePose, 
  RunningMetricsCalculator 
} from '@/path/to/react-sdk';

// Use in your component
function AnalysisPage() {
  return (
    <ResultsPage
      analysisId="analysis-123"
      userEmail="<EMAIL>"
      onBack={() => router.back()}
      onNewAnalysis={() => router.push('/upload')}
    />
  );
}
```

## Components

### `ResultsPage`
Main results display component with tabs, metrics, and video integration.

### `ProcessedVideoPlayer`
Video player with pose overlay rendering and synchronization.

### `TabsContainer`
Side/Rear view switching with visual indicators.

### `MetricsDisplay`
Running metrics display with calculated values and status indicators.

## Hooks

### `useBlazePose`
React hook wrapping BlazePose detection logic with tensor memory management.

### `useAnalysisData`
Hook for fetching and managing analysis data from Supabase.

## Utilities

### `RunningMetricsCalculator`
Calculates biomechanical metrics from pose keypoints.

### Coordinate Transformations
Utilities for converting between coordinate systems.

## Directory Structure

```
react-sdk/
├── components/          # React components
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
├── styles/             # CSS and styling utilities
├── config/             # Configuration files
├── package.json        # SDK dependencies
├── tsconfig.json       # TypeScript configuration
└── index.ts           # Main export file
```

## Development

The SDK is built with:
- React 18.2.0
- TypeScript 5.2.2
- TensorFlow.js 4.10.0
- Tailwind CSS compatibility
- Supabase integration

## Integration Notes

- Designed for Next.js 14+ applications
- Compatible with shadcn/ui components
- Uses existing BlazePose Full model (39 keypoints)
- Integrates with Supabase database tables
- Memory-optimized tensor operations