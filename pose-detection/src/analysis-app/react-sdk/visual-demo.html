<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React SDK Visual Demo - Phases 1-5</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .demo-section {
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 1.5rem;
            margin-bottom: 2rem;
            background: white;
        }
        .demo-header {
            background: #1e40af;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.375rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        .code-block {
            background: #f3f4f6;
            border: 1px solid #d1d5db;
            border-radius: 0.375rem;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            overflow-x: auto;
        }
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
        }
        .status-complete {
            background: #d1fae5;
            color: #065f46;
        }
        .status-pending {
            background: #fef3c7;
            color: #92400e;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useCallback } = React;

        // Mock implementation of our components for visualization
        
        // Phase 3: ProcessedVideoPlayer Component
        const ProcessedVideoPlayer = ({ videoUrl, resultsUrl, viewType, overlayStyle }) => (
            <div className="relative bg-gray-900 rounded-lg overflow-hidden" style={{ paddingBottom: '56.25%' }}>
                <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center text-white">
                        <div className="mb-4">
                            <svg className="w-16 h-16 mx-auto text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <p className="text-sm text-gray-400 mb-2">ProcessedVideoPlayer Component</p>
                        <p className="text-xs text-gray-500">Video: {videoUrl}</p>
                        <p className="text-xs text-gray-500">Results: {resultsUrl}</p>
                        <p className="text-xs text-gray-500">View: {viewType} | Style: {overlayStyle}</p>
                    </div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 p-2">
                    <div className="flex items-center space-x-4 text-white text-xs">
                        <button className="p-1">▶️</button>
                        <div className="flex-1 bg-gray-600 rounded-full h-1">
                            <div className="bg-blue-500 h-1 rounded-full" style={{ width: '35%' }}></div>
                        </div>
                        <span>1:23 / 3:45</span>
                    </div>
                </div>
            </div>
        );

        // Phase 5: ResultsPage Component (simplified)
        const ResultsPage = ({ analysisId, userEmail, onBack, onNewAnalysis }) => {
            const [activeTab, setActiveTab] = useState('side');
            const [status, setStatus] = useState('completed');

            return (
                <div className="bg-white rounded-lg shadow-lg overflow-hidden">
                    {/* Header */}
                    <div className="bg-white border-b px-6 py-4">
                        <div className="flex justify-between items-center">
                            <h1 className="text-2xl font-bold text-gray-900">Running Analysis Results</h1>
                            <div className="space-x-2">
                                <button onClick={onBack} className="px-4 py-2 text-gray-700 bg-white border rounded hover:bg-gray-50">
                                    ← Back
                                </button>
                                <button className="px-4 py-2 text-gray-700 bg-white border rounded hover:bg-gray-50">
                                    Export
                                </button>
                                <button onClick={onNewAnalysis} className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700">
                                    New Analysis
                                </button>
                            </div>
                        </div>
                    </div>

                    {/* Tabs */}
                    <div className="border-b px-6">
                        <nav className="-mb-px flex space-x-8">
                            <button
                                onClick={() => setActiveTab('side')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'side' 
                                        ? 'border-blue-500 text-blue-600' 
                                        : 'border-transparent text-gray-500'
                                }`}
                            >
                                Side View
                            </button>
                            <button
                                onClick={() => setActiveTab('rear')}
                                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                                    activeTab === 'rear' 
                                        ? 'border-blue-500 text-blue-600' 
                                        : 'border-transparent text-gray-500'
                                }`}
                            >
                                Rear View
                            </button>
                        </nav>
                    </div>

                    {/* Content */}
                    <div className="p-6">
                        <div className="grid grid-cols-3 gap-6">
                            <div className="col-span-2">
                                <ProcessedVideoPlayer 
                                    videoUrl="./Michael_test_side.mp4"
                                    resultsUrl="./d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json"
                                    viewType={activeTab}
                                    overlayStyle="medical"
                                />
                            </div>
                            <div className="space-y-4">
                                <div className="bg-white border rounded-lg p-4">
                                    <h3 className="font-semibold mb-3">Analysis Metrics</h3>
                                    <div className="space-y-2 text-sm">
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Overall Score</span>
                                            <span className="font-medium">85%</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Cadence</span>
                                            <span className="font-medium">180 spm</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Stride Length</span>
                                            <span className="font-medium">1.2 m</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Vertical Osc.</span>
                                            <span className="font-medium">8.5 cm</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // Hook visualizations
        const HookDemo = ({ name, description, returns }) => (
            <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-semibold text-blue-900 mb-2">{name}</h4>
                <p className="text-sm text-gray-600 mb-3">{description}</p>
                <div className="code-block text-xs">
                    <div className="text-green-600">// Returns:</div>
                    {returns.map((item, i) => (
                        <div key={i}>• {item}</div>
                    ))}
                </div>
            </div>
        );

        // Main Demo App
        const DemoApp = () => {
            const [showCode, setShowCode] = useState(false);

            return (
                <div className="max-w-7xl mx-auto p-6">
                    {/* Header */}
                    <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
                        <h1 className="text-3xl font-bold text-gray-900 mb-4">
                            React SDK Visual Demo - Phases 1-5
                        </h1>
                        <p className="text-gray-600 mb-6">
                            Visual demonstration of the Peak Insight Pose Analysis SDK components and architecture
                        </p>
                        
                        {/* Progress Overview */}
                        <div className="grid grid-cols-5 gap-4">
                            {[1, 2, 3, 4, 5].map(phase => (
                                <div key={phase} className="text-center">
                                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-2 ${
                                        phase <= 5 ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-600'
                                    }`}>
                                        {phase <= 5 ? '✓' : phase}
                                    </div>
                                    <p className="text-xs font-medium">Phase {phase}</p>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Phase 1: Foundation */}
                    <div className="demo-section">
                        <div className="demo-header">Phase 1: SDK Foundation Setup</div>
                        <div className="grid grid-cols-2 gap-6">
                            <div>
                                <h3 className="font-semibold mb-2">Directory Structure</h3>
                                <div className="code-block">
                                    react-sdk/<br/>
                                    ├── components/<br/>
                                    ├── hooks/<br/>
                                    ├── types/<br/>
                                    ├── utils/<br/>
                                    ├── package.json<br/>
                                    ├── tsconfig.json<br/>
                                    └── index.ts
                                </div>
                            </div>
                            <div>
                                <h3 className="font-semibold mb-2">Key Features</h3>
                                <ul className="space-y-2 text-sm">
                                    <li>✅ TypeScript configuration</li>
                                    <li>✅ React 18.2.0 support</li>
                                    <li>✅ TensorFlow.js integration</li>
                                    <li>✅ ESM module exports</li>
                                    <li>✅ Development dependencies</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Phase 2: Types */}
                    <div className="demo-section">
                        <div className="demo-header">Phase 2: Type Definitions</div>
                        <div className="grid grid-cols-3 gap-4">
                            <div className="bg-blue-50 rounded p-3">
                                <h4 className="font-semibold text-blue-900 mb-2">pose.ts</h4>
                                <ul className="text-xs space-y-1">
                                    <li>• ProcessedPoseData</li>
                                    <li>• PoseKeypoint</li>
                                    <li>• BlazePoseModelType</li>
                                    <li>• PoseEstimationResult</li>
                                </ul>
                            </div>
                            <div className="bg-green-50 rounded p-3">
                                <h4 className="font-semibold text-green-900 mb-2">analysis.ts</h4>
                                <ul className="text-xs space-y-1">
                                    <li>• AnalysisData</li>
                                    <li>• ProcessedVideo</li>
                                    <li>• UserConfiguration</li>
                                    <li>• ProcessingStatus</li>
                                </ul>
                            </div>
                            <div className="bg-purple-50 rounded p-3">
                                <h4 className="font-semibold text-purple-900 mb-2">metrics.ts</h4>
                                <ul className="text-xs space-y-1">
                                    <li>• RunningMetrics</li>
                                    <li>• SideViewMetrics</li>
                                    <li>• RearViewMetrics</li>
                                    <li>• MetricStatus</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Phase 3: Video Player */}
                    <div className="demo-section">
                        <div className="demo-header">Phase 3: ProcessedVideoPlayer Component</div>
                        <div className="mb-4">
                            <ProcessedVideoPlayer 
                                videoUrl="./Michael_test_side.mp4"
                                resultsUrl="./d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json"
                                viewType="side"
                                overlayStyle="medical"
                            />
                        </div>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <h4 className="font-semibold mb-2">Features</h4>
                                <ul className="space-y-1">
                                    <li>✅ Video/Canvas synchronization</li>
                                    <li>✅ Pose overlay rendering</li>
                                    <li>✅ Playback controls</li>
                                    <li>✅ 0.25x playback rate</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-semibold mb-2">Props</h4>
                                <ul className="space-y-1 text-xs font-mono">
                                    <li>videoUrl: string</li>
                                    <li>resultsUrl: string</li>
                                    <li>viewType: 'side' | 'rear'</li>
                                    <li>overlayStyle: 'medical' | 'athletic'</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    {/* Phase 4: Hooks */}
                    <div className="demo-section">
                        <div className="demo-header">Phase 4: React Hooks</div>
                        <div className="grid grid-cols-3 gap-4">
                            <HookDemo 
                                name="useBlazePose"
                                description="BlazePose detector integration with memory management"
                                returns={[
                                    "isLoading: boolean",
                                    "isReady: boolean", 
                                    "detectPose(video): Promise<PoseData>",
                                    "dispose(): void"
                                ]}
                            />
                            <HookDemo 
                                name="useAnalysisData"
                                description="Analysis data state management with auto-refresh"
                                returns={[
                                    "analysis: AnalysisData | null",
                                    "isLoading: boolean",
                                    "error: Error | null",
                                    "refetch(): void"
                                ]}
                            />
                            <HookDemo 
                                name="useSupabaseAnalysis"
                                description="Database operations for analysis CRUD"
                                returns={[
                                    "createAnalysis(data): Promise<string>",
                                    "getAnalysis(id): Promise<Analysis>",
                                    "updateAnalysisStatus(id, status): Promise<void>",
                                    "subscribeToUpdates(id, cb): () => void"
                                ]}
                            />
                        </div>
                    </div>

                    {/* Phase 5: Results Page */}
                    <div className="demo-section">
                        <div className="demo-header">Phase 5: ResultsPage Component</div>
                        <ResultsPage 
                            analysisId="demo-analysis-123"
                            userEmail="<EMAIL>"
                            onBack={() => console.log('Back clicked')}
                            onNewAnalysis={() => console.log('New analysis clicked')}
                        />
                    </div>

                    {/* SDK Usage Example */}
                    <div className="demo-section">
                        <div className="demo-header">SDK Usage Example</div>
                        <button 
                            onClick={() => setShowCode(!showCode)}
                            className="mb-4 px-4 py-2 bg-gray-100 rounded hover:bg-gray-200"
                        >
                            {showCode ? 'Hide' : 'Show'} Integration Code
                        </button>
                        
                        {showCode && (
                            <div className="code-block">
                                <pre>{`import { ResultsPage, useBlazePose, useAnalysisData } from '@tfjs-models/react-sdk';

function MyAnalysisPage() {
  const analysisData = useAnalysisData({
    analysisId: 'abc-123',
    autoRefresh: true
  });

  const blazePose = useBlazePose({
    modelType: 'full',
    enableSmoothing: true
  });

  return (
    <ResultsPage
      analysisId="abc-123"
      userEmail="<EMAIL>"
      onBack={() => router.back()}
      onNewAnalysis={() => router.push('/upload')}
    />
  );
}`}</pre>
                            </div>
                        )}
                    </div>

                    {/* Architecture Overview */}
                    <div className="demo-section">
                        <div className="demo-header">Architecture Overview</div>
                        <div className="bg-gray-50 rounded-lg p-6">
                            <div className="grid grid-cols-3 gap-8 text-center">
                                <div>
                                    <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span className="text-2xl">🎥</span>
                                    </div>
                                    <h4 className="font-semibold mb-2">Video Processing</h4>
                                    <p className="text-sm text-gray-600">
                                        ProcessedVideoPlayer handles video playback with pose overlay
                                    </p>
                                </div>
                                <div>
                                    <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span className="text-2xl">🤖</span>
                                    </div>
                                    <h4 className="font-semibold mb-2">AI Analysis</h4>
                                    <p className="text-sm text-gray-600">
                                        BlazePose Full model with 39 keypoints for biomechanical analysis
                                    </p>
                                </div>
                                <div>
                                    <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                        <span className="text-2xl">📊</span>
                                    </div>
                                    <h4 className="font-semibold mb-2">Metrics Display</h4>
                                    <p className="text-sm text-gray-600">
                                        Real-time calculation and display of running biomechanics
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Status Summary */}
                    <div className="bg-white rounded-lg shadow-lg p-6">
                        <h2 className="text-xl font-bold mb-4">Implementation Status</h2>
                        <div className="space-y-2">
                            <div className="flex items-center justify-between">
                                <span>Phase 1-5 Complete</span>
                                <span className="status-badge status-complete">45% Complete</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span>Components Built</span>
                                <span className="font-mono text-sm">2 of 5</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span>Hooks Implemented</span>
                                <span className="font-mono text-sm">3 of 3</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span>Type Definitions</span>
                                <span className="font-mono text-sm">4 modules</span>
                            </div>
                        </div>
                        
                        <div className="mt-6 pt-6 border-t">
                            <h3 className="font-semibold mb-3">Next Phases</h3>
                            <div className="space-y-2 text-sm">
                                <div className="flex items-center space-x-2">
                                    <span className="status-badge status-pending">Phase 6</span>
                                    <span>Metrics Components (TabsContainer, MetricsDisplay)</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <span className="status-badge status-pending">Phase 7</span>
                                    <span>Utility Functions</span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <span className="status-badge status-pending">Phase 8</span>
                                    <span>Supabase Integration</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // Render the demo
        ReactDOM.render(<DemoApp />, document.getElementById('root'));
    </script>
</body>
</html>