/**
 * Peak Insight Pose Analysis SDK
 * 
 * React SDK for integrating BlazePose biomechanical analysis
 * into Next.js applications.
 * 
 * @version 1.0.0
 * <AUTHOR> Insight Team
 */

// =============================================================================
// MAIN COMPONENTS
// =============================================================================

// Video Player Component (Phase 3 - Complete)
export { ProcessedVideoPlayer } from './components/ProcessedVideoPlayer';

// Results Display Components (Phase 5 - Complete)
export { ResultsPage } from './components/ResultsPage';

// To be implemented in later phases
// export { TabsContainer } from './components/TabsContainer';
// export { MetricsDisplay } from './components/MetricsDisplay';

// =============================================================================
// HOOKS
// =============================================================================

// BlazePose Integration (Phase 4 - Complete)
export { useBlazePose } from './hooks/useBlazePose';
export { useAnalysisData } from './hooks/useAnalysisData';
export { useSupabaseAnalysis } from './hooks/useSupabaseAnalysis';

// Running Metrics Hook (Phase 7 - Complete)
export { useRunningMetrics } from './hooks/useRunningMetrics';

// =============================================================================
// UTILITIES
// =============================================================================

// Export all utilities (Phase 7 - Complete)
export * from './utils';

// =============================================================================
// TYPES
// =============================================================================

// Core Types
export * from './types/pose';
export * from './types/analysis';
export * from './types/metrics';
export * from './types/supabase';

// Component Props
export * from './types/components';

// =============================================================================
// CONFIGURATION
// =============================================================================

// Configuration (To be implemented as needed)
// export { SDK_CONFIG } from './config/constants';
// export { blazePoseConfig } from './config/blazepose';

// =============================================================================
// VERSION INFO
// =============================================================================

export const SDK_VERSION = '1.0.0';
export const BLAZEPOSE_VERSION = '2.1.0';
export const SUPPORTED_REACT_VERSIONS = '^18.2.0';