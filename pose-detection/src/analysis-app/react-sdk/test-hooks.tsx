/**
 * Hook Test Components
 * 
 * Test components to verify that all hooks work correctly
 */

import React, { useRef, useEffect, useState } from 'react';
import { useBlazePose } from './hooks/useBlazePose';
import { useAnalysisData } from './hooks/useAnalysisData';
import { useSupabaseAnalysis } from './hooks/useSupabaseAnalysis';
import { ProcessedPoseData } from './types/pose';

/**
 * Test component for useBlazePose hook
 */
export const BlazePoseHookTest: React.FC = () => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [poseData, setPoseData] = useState<ProcessedPoseData | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);

  const blazePose = useBlazePose({
    modelType: 'full',
    enableSmoothing: true,
    onPoseData: (data) => {
      setPoseData(data);
      console.log('Pose detected:', data);
    },
    onError: (error) => {
      console.error('BlazePose error:', error);
    },
    onStatusChange: (status) => {
      console.log('BlazePose status:', status);
    }
  });

  const handleStartDetection = async () => {
    if (!videoRef.current || !blazePose.isReady) return;
    
    setIsDetecting(true);
    try {
      const result = await blazePose.detectPose(videoRef.current);
      console.log('Detection result:', result);
    } catch (error) {
      console.error('Detection error:', error);
    } finally {
      setIsDetecting(false);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-semibold mb-4">useBlazePose Hook Test</h2>
      
      {/* Status Display */}
      <div className="mb-4 p-4 bg-gray-50 rounded">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>Loading: {blazePose.isLoading ? '✅' : '❌'}</div>
          <div>Ready: {blazePose.isReady ? '✅' : '❌'}</div>
          <div>Frame Count: {blazePose.frameCount}</div>
          <div>Model Type: {blazePose.modelInfo.type}</div>
          <div>Smoothing: {blazePose.modelInfo.smoothing ? '✅' : '❌'}</div>
          <div>Expected Keypoints: {blazePose.modelInfo.expectedKeypoints}</div>
        </div>
        
        {blazePose.error && (
          <div className="mt-2 p-2 bg-red-100 text-red-700 rounded">
            Error: {blazePose.error.message}
          </div>
        )}
      </div>

      {/* Video Element */}
      <div className="mb-4">
        <video
          ref={videoRef}
          src="./Michael_test_side.mp4"
          controls
          className="w-full max-w-md rounded"
        />
      </div>

      {/* Controls */}
      <div className="mb-4 space-x-2">
        <button
          onClick={handleStartDetection}
          disabled={!blazePose.isReady || isDetecting}
          className="px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50"
        >
          {isDetecting ? 'Detecting...' : 'Detect Pose'}
        </button>
        
        <button
          onClick={blazePose.reset}
          disabled={!blazePose.isReady}
          className="px-4 py-2 bg-gray-600 text-white rounded disabled:opacity-50"
        >
          Reset
        </button>
        
        <button
          onClick={blazePose.dispose}
          className="px-4 py-2 bg-red-600 text-white rounded"
        >
          Dispose
        </button>
      </div>

      {/* Pose Data Display */}
      {poseData && (
        <div className="p-4 bg-green-50 rounded">
          <h3 className="font-semibold mb-2">Latest Pose Data:</h3>
          <div className="text-sm space-y-1">
            <div>Frame: {poseData.frameNumber}</div>
            <div>Timestamp: {poseData.timestamp.toFixed(2)}ms</div>
            <div>Score: {poseData.score?.toFixed(3)}</div>
            <div>Keypoints: {poseData.keypoints.length}</div>
            <div>Model: {poseData.modelType}</div>
            <div>Smoothed: {poseData.smoothed ? '✅' : '❌'}</div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Test component for useAnalysisData hook
 */
export const AnalysisDataHookTest: React.FC = () => {
  const [testAnalysisId, setTestAnalysisId] = useState('test-analysis-123');

  const analysisData = useAnalysisData({
    analysisId: testAnalysisId,
    userEmail: '<EMAIL>',
    autoRefresh: true,
    onStatusChange: (status) => {
      console.log('Analysis status change:', status);
    },
    onError: (error) => {
      console.error('Analysis data error:', error);
    }
  });

  const handleUpdateAnalysis = async () => {
    try {
      await analysisData.updateAnalysis({
        status: 'processing',
        processingTime: 60
      });
      console.log('Analysis updated successfully');
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-semibold mb-4">useAnalysisData Hook Test</h2>
      
      {/* Controls */}
      <div className="mb-4">
        <input
          type="text"
          value={testAnalysisId}
          onChange={(e) => setTestAnalysisId(e.target.value)}
          placeholder="Analysis ID"
          className="px-3 py-2 border rounded mr-2"
        />
        <button
          onClick={analysisData.refetch}
          disabled={analysisData.isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50 mr-2"
        >
          {analysisData.isLoading ? 'Loading...' : 'Refetch'}
        </button>
        <button
          onClick={handleUpdateAnalysis}
          disabled={!analysisData.analysis}
          className="px-4 py-2 bg-green-600 text-white rounded disabled:opacity-50"
        >
          Test Update
        </button>
      </div>

      {/* Status */}
      <div className="mb-4 p-4 bg-gray-50 rounded">
        <div>Loading: {analysisData.isLoading ? '✅' : '❌'}</div>
        <div>Has Analysis: {analysisData.analysis ? '✅' : '❌'}</div>
        {analysisData.error && (
          <div className="mt-2 text-red-600">Error: {analysisData.error.message}</div>
        )}
      </div>

      {/* Analysis Data Display */}
      {analysisData.analysis && (
        <div className="p-4 bg-blue-50 rounded">
          <h3 className="font-semibold mb-2">Analysis Data:</h3>
          <div className="text-sm space-y-1">
            <div>ID: {analysisData.analysis.id}</div>
            <div>Status: {analysisData.analysis.status}</div>
            <div>User: {analysisData.analysis.userEmail}</div>
            <div>Height: {analysisData.analysis.userConfig.height.feet}'{analysisData.analysis.userConfig.height.inches}"</div>
            <div>Videos: {analysisData.analysis.videos.length}</div>
            <div>Score: {analysisData.analysis.metrics.overallScore}</div>
          </div>
        </div>
      )}
    </div>
  );
};

/**
 * Test component for useSupabaseAnalysis hook
 */
export const SupabaseAnalysisHookTest: React.FC = () => {
  const [testAnalysisId, setTestAnalysisId] = useState('');
  const [statusMessage, setStatusMessage] = useState('');

  const supabase = useSupabaseAnalysis({
    onStatusUpdate: (id, status) => {
      setStatusMessage(`Analysis ${id} status: ${status}`);
    },
    onError: (error) => {
      console.error('Supabase error:', error);
    },
    enableRealtime: true
  });

  const handleCreateAnalysis = async () => {
    try {
      const id = await supabase.createAnalysis({
        user_email: '<EMAIL>',
        height_inches: 70,
        height_display: "5'10\"",
        gender: 'male',
        weight_lbs: 150,
        weight_display: '150 lbs'
      });
      setTestAnalysisId(id);
      console.log('Created analysis:', id);
    } catch (error) {
      console.error('Create failed:', error);
    }
  };

  const handleGetAnalysis = async () => {
    if (!testAnalysisId) return;
    
    try {
      const analysis = await supabase.getAnalysis(testAnalysisId);
      console.log('Retrieved analysis:', analysis);
    } catch (error) {
      console.error('Get failed:', error);
    }
  };

  const handleUpdateStatus = async () => {
    if (!testAnalysisId) return;
    
    try {
      await supabase.updateAnalysisStatus(testAnalysisId, 'processing_side');
      console.log('Status updated');
    } catch (error) {
      console.error('Update failed:', error);
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-xl font-semibold mb-4">useSupabaseAnalysis Hook Test</h2>
      
      {/* Status */}
      <div className="mb-4 p-4 bg-gray-50 rounded">
        <div>Loading: {supabase.isLoading ? '✅' : '❌'}</div>
        {supabase.error && (
          <div className="text-red-600">Error: {supabase.error.message}</div>
        )}
        {statusMessage && (
          <div className="text-blue-600">Status: {statusMessage}</div>
        )}
      </div>

      {/* Controls */}
      <div className="mb-4 space-y-2">
        <div>
          <input
            type="text"
            value={testAnalysisId}
            onChange={(e) => setTestAnalysisId(e.target.value)}
            placeholder="Analysis ID"
            className="px-3 py-2 border rounded mr-2"
          />
        </div>
        
        <div className="space-x-2">
          <button
            onClick={handleCreateAnalysis}
            disabled={supabase.isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded disabled:opacity-50"
          >
            Create Analysis
          </button>
          
          <button
            onClick={handleGetAnalysis}
            disabled={!testAnalysisId || supabase.isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded disabled:opacity-50"
          >
            Get Analysis
          </button>
          
          <button
            onClick={handleUpdateStatus}
            disabled={!testAnalysisId || supabase.isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded disabled:opacity-50"
          >
            Update Status
          </button>
        </div>
      </div>
    </div>
  );
};

/**
 * Combined test component
 */
export const HooksTestSuite: React.FC = () => {
  return (
    <div className="p-6 max-w-6xl mx-auto space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          React SDK Hooks Test Suite
        </h1>
        <p className="text-gray-600">
          Testing all Phase 4 hooks: useBlazePose, useAnalysisData, useSupabaseAnalysis
        </p>
      </div>
      
      <BlazePoseHookTest />
      <AnalysisDataHookTest />
      <SupabaseAnalysisHookTest />
      
      <div className="mt-8 p-4 bg-green-50 rounded-lg">
        <h3 className="font-semibold text-green-900 mb-2">Test Instructions:</h3>
        <ul className="text-sm text-green-800 space-y-1">
          <li>1. <strong>useBlazePose:</strong> Load video, click "Detect Pose", check console for results</li>
          <li>2. <strong>useAnalysisData:</strong> Enter analysis ID, click "Refetch", verify data loads</li>
          <li>3. <strong>useSupabaseAnalysis:</strong> Click "Create Analysis", then test other operations</li>
          <li>4. Check browser console for detailed logs and error messages</li>
          <li>5. Verify all hooks handle loading states and errors properly</li>
        </ul>
      </div>
    </div>
  );
};