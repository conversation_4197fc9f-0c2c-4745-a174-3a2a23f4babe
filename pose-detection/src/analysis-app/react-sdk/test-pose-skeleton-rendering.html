<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 BLOCKER 1 TEST: Pose Skeleton Overlay Rendering</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .video-section {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .video-container {
            flex: 0 0 400px;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .metrics-container {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .video-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        #videoElement {
            width: 100%;
            height: auto;
            border-radius: 8px;
        }
        
        #canvasElement {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            border-radius: 8px;
        }
        
        .status {
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d7ff;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .test-controls button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        
        .test-controls button:hover {
            background: #0056b3;
        }
        
        .skeleton-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 BLOCKER 1 TEST: Pose Skeleton Overlay Rendering</h1>
            <p><strong>Purpose:</strong> Verify that pose skeleton overlay rendering matches dev-app-smoothnet.html reference exactly</p>
            <p><strong>Testing:</strong> ProcessedVideoPlayerModal component with actual modal data</p>
        </div>
        
        <div class="test-section">
            <h2>🔧 Skeleton Rendering Validation</h2>
            <div class="test-controls">
                <button onclick="loadAndTest()" id="testBtn">▶️ Start Skeleton Test</button>
                <button onclick="pauseTest()" id="pauseBtn">⏸️ Pause</button>
                <button onclick="resetTest()" id="resetBtn">🔄 Reset</button>
                <button onclick="toggleSkeleton()" id="skeletonBtn">🦴 Toggle Skeleton</button>
                <button onclick="toggleKeypoints()" id="keypointsBtn">⚫ Toggle Keypoints</button>
            </div>
            
            <div id="testStatus" class="status info">Ready to test pose skeleton rendering...</div>
        </div>
        
        <div class="video-section">
            <div class="video-container">
                <div class="video-wrapper">
                    <video id="videoElement" style="display: none;"></video>
                    <canvas id="canvasElement"></canvas>
                </div>
                
                <div class="test-controls" style="margin-top: 15px;">
                    <button onclick="stepFrame(-1)">⏮️ Prev Frame</button>
                    <button onclick="stepFrame(1)">⏭️ Next Frame</button>
                </div>
            </div>
            
            <div class="metrics-container">
                <h3>🎯 Skeleton Rendering Analysis</h3>
                
                <div class="metric-item">
                    <span>Modal Data Status:</span>
                    <span id="modalStatus">Not Loaded</span>
                </div>
                <div class="metric-item">
                    <span>Keypoints Filtered:</span>
                    <span id="filteredCount">0 / 33</span>
                </div>
                <div class="metric-item">
                    <span>Skeleton Connections:</span>
                    <span id="connectionCount">0 / 18</span>
                </div>
                <div class="metric-item">
                    <span>Coordinate Scaling:</span>
                    <span id="coordScaling">Not Applied</span>
                </div>
                <div class="metric-item">
                    <span>Confidence Coloring:</span>
                    <span id="confidenceStatus">Not Applied</span>
                </div>
                <div class="metric-item">
                    <span>Invalid Keypoints:</span>
                    <span id="invalidCount">0</span>
                </div>
                <div class="metric-item">
                    <span>Frame Processing:</span>
                    <span id="processingTime">0ms</span>
                </div>
                
                <div class="skeleton-info" id="skeletonInfo">
                    <h4>Skeleton Rendering Details:</h4>
                    <div id="renderingDetails">No frame data yet...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let modalData = null;
        let video = null;
        let canvas = null;
        let ctx = null;
        let currentFrame = null;
        let showSkeleton = true;
        let showKeypoints = true;
        let animationId = null;
        let isPlaying = false;
        
        // Configuration matching ProcessedVideoPlayerModal exactly
        const SKIP_KEYPOINTS = [
            1, 2, 3, 4, 5, 6,        // eyes
            9, 10,                   // mouth  
            17, 18, 19, 20, 21, 22   // fingers
        ];
        
        const SKELETON_CONNECTIONS = [
            // Head (nose to ears only)
            [0, 7], [0, 8],
            // Torso
            [11, 12], [11, 23], [12, 24], [23, 24],
            // Arms (to wrists only)
            [11, 13], [13, 15], [12, 14], [14, 16],
            // Legs (full leg structure)
            [23, 25], [25, 27], [27, 29], [27, 31],
            [24, 26], [26, 28], [28, 30], [28, 32]
        ];
        
        const KEYPOINT_NAMES = [
            'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
            'right_eye_inner', 'right_eye', 'right_eye_outer',
            'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
            'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
            'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
            'left_index', 'right_index', 'left_thumb', 'right_thumb',
            'left_hip', 'right_hip', 'left_knee', 'right_knee',
            'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
            'left_foot_index', 'right_foot_index'
        ];
        
        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            video = document.getElementById('videoElement');
            canvas = document.getElementById('canvasElement');
            ctx = canvas.getContext('2d');
            
            video.addEventListener('loadedmetadata', syncCanvasWithVideo);
            video.addEventListener('timeupdate', updateCurrentFrame);
            
            updateStatus('System initialized. Click "Start Skeleton Test" to begin.', 'info');
        });
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('testStatus');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // Load Modal data and start test
        async function loadAndTest() {
            try {
                updateStatus('Loading Modal data...', 'info');
                
                const response = await fetch('../d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                modalData = await response.json();
                
                updateStatus('Modal data loaded. Loading video...', 'info');
                document.getElementById('modalStatus').textContent = '✅ Loaded';
                
                // Load matching video
                video.src = '../Michael_test_side.mp4';
                video.style.display = 'block';
                video.playbackRate = 0.25;
                video.muted = true;
                
                // Start processing when video is ready
                video.addEventListener('loadedmetadata', () => {
                    syncCanvasWithVideo();
                    updateStatus('✅ Ready! Video and Modal data loaded. Processing frames...', 'success');
                    startProcessing();
                });
                
                console.log('Modal data loaded:', {
                    video: modalData.video,
                    dimensions: `${modalData.videoWidth}×${modalData.videoHeight}`,
                    frames: modalData.frames.length,
                    modelType: modalData.modelType
                });
                
            } catch (error) {
                updateStatus(`❌ Error: ${error.message}`, 'error');
                console.error('Test error:', error);
            }
        }
        
        function syncCanvasWithVideo() {
            if (!video || !canvas) return;
            
            const videoRect = video.getBoundingClientRect();
            canvas.width = videoRect.width;
            canvas.height = videoRect.height;
            canvas.style.width = `${videoRect.width}px`;
            canvas.style.height = `${videoRect.height}px`;
            
            console.log('Canvas synced with video:', {
                displaySize: { width: videoRect.width, height: videoRect.height },
                videoRes: { width: video.videoWidth, height: video.videoHeight }
            });
        }
        
        function findFrameForTime(currentTime) {
            if (!modalData?.frames) return null;
            
            // Find closest frame by timestamp
            let closestFrame = null;
            let minDiff = Infinity;
            
            for (const frame of modalData.frames) {
                const diff = Math.abs(frame.timestamp - currentTime);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestFrame = frame;
                }
            }
            
            return closestFrame;
        }
        
        function drawSkeletonOverlay(frameData) {
            if (!frameData || !frameData.keypoints) return;
            
            const startTime = performance.now();
            
            // Clear canvas and draw video frame
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            // Scale coordinates from Modal resolution to canvas display
            const scaleX = canvas.width / modalData.videoWidth;
            const scaleY = canvas.height / modalData.videoHeight;
            
            let drawnKeypoints = 0;
            let drawnConnections = 0;
            let invalidCount = 0;
            let confidenceCount = { high: 0, medium: 0, low: 0 };
            
            // Draw skeleton connections first (behind keypoints)
            if (showSkeleton) {
                ctx.strokeStyle = 'rgba(0, 255, 255, 0.9)'; // Bright cyan
                ctx.lineWidth = 3;
                
                SKELETON_CONNECTIONS.forEach(([i, j]) => {
                    const kp1 = frameData.keypoints[i];
                    const kp2 = frameData.keypoints[j];
                    
                    if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
                        ctx.beginPath();
                        ctx.moveTo(kp1.x * scaleX, kp1.y * scaleY);
                        ctx.lineTo(kp2.x * scaleX, kp2.y * scaleY);
                        ctx.stroke();
                        drawnConnections++;
                    }
                });
            }
            
            // Draw keypoints
            if (showKeypoints) {
                frameData.keypoints.forEach((keypoint, idx) => {
                    if (SKIP_KEYPOINTS.includes(idx)) return; // Skip filtered keypoints
                    
                    // Check for invalid coordinates
                    if (keypoint.x === 0.0 && keypoint.y === 0.0) {
                        invalidCount++;
                        return;
                    }
                    
                    if (keypoint.score > 0.3) {
                        const x = keypoint.x * scaleX;
                        const y = keypoint.y * scaleY;
                        
                        // Color based on confidence
                        let color;
                        if (keypoint.score > 0.8) {
                            color = '#00ff00'; // Green
                            confidenceCount.high++;
                        } else if (keypoint.score > 0.5) {
                            color = '#ffaa00'; // Orange
                            confidenceCount.medium++;
                        } else {
                            color = '#ff0000'; // Red
                            confidenceCount.low++;
                        }
                        
                        // Draw keypoint
                        ctx.fillStyle = color;
                        ctx.strokeStyle = 'white';
                        ctx.lineWidth = 2;
                        ctx.beginPath();
                        ctx.arc(x, y, 6, 0, 2 * Math.PI);
                        ctx.fill();
                        ctx.stroke();
                        
                        // Draw keypoint index
                        ctx.fillStyle = 'white';
                        ctx.font = '10px Arial';
                        ctx.fillText(idx.toString(), x + 6, y - 6);
                        
                        drawnKeypoints++;
                    }
                });
            }
            
            // Draw SmoothNet indicator
            ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
            ctx.fillRect(canvas.width - 220, 10, 210, 30);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('🔄 SmoothNet 3DPW-SPIN-3D', canvas.width - 210, 30);
            
            // Update metrics
            const processingTime = performance.now() - startTime;
            document.getElementById('filteredCount').textContent = `${33 - SKIP_KEYPOINTS.length} / 33`;
            document.getElementById('connectionCount').textContent = `${drawnConnections} / ${SKELETON_CONNECTIONS.length}`;
            document.getElementById('coordScaling').textContent = `${scaleX.toFixed(2)}x, ${scaleY.toFixed(2)}y`;
            document.getElementById('confidenceStatus').textContent = `H:${confidenceCount.high} M:${confidenceCount.medium} L:${confidenceCount.low}`;
            document.getElementById('invalidCount').textContent = invalidCount;
            document.getElementById('processingTime').textContent = `${processingTime.toFixed(1)}ms`;
            
            // Update detailed info
            const details = `
Frame: ${frameData.frameNumber}
Time: ${frameData.timestamp.toFixed(2)}s
Keypoints: ${drawnKeypoints} drawn, ${invalidCount} invalid
Connections: ${drawnConnections} drawn
Scale: ${scaleX.toFixed(3)} x ${scaleY.toFixed(3)}
Confidence: ${confidenceCount.high} high, ${confidenceCount.medium} medium, ${confidenceCount.low} low
Processing: ${processingTime.toFixed(1)}ms
            `;
            document.getElementById('renderingDetails').textContent = details;
        }
        
        function updateCurrentFrame() {
            if (!modalData || !video) return;
            
            const frameData = findFrameForTime(video.currentTime);
            if (frameData) {
                currentFrame = frameData;
                drawSkeletonOverlay(frameData);
            }
        }
        
        function startProcessing() {
            isPlaying = true;
            video.play();
            processLoop();
        }
        
        function processLoop() {
            if (!isPlaying || video.paused || video.ended) return;
            
            updateCurrentFrame();
            animationId = requestAnimationFrame(processLoop);
        }
        
        function pauseTest() {
            isPlaying = false;
            video.pause();
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }
        
        function resetTest() {
            pauseTest();
            video.currentTime = 0;
            if (ctx) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
            }
        }
        
        function toggleSkeleton() {
            showSkeleton = !showSkeleton;
            const btn = document.getElementById('skeletonBtn');
            btn.textContent = showSkeleton ? '🦴 Hide Skeleton' : '🦴 Show Skeleton';
            if (currentFrame) drawSkeletonOverlay(currentFrame);
        }
        
        function toggleKeypoints() {
            showKeypoints = !showKeypoints;
            const btn = document.getElementById('keypointsBtn');
            btn.textContent = showKeypoints ? '⚫ Hide Keypoints' : '⚫ Show Keypoints';
            if (currentFrame) drawSkeletonOverlay(currentFrame);
        }
        
        function stepFrame(direction) {
            if (!video) return;
            const frameDuration = 1 / 30; // Assume 30fps
            video.currentTime = Math.max(0, video.currentTime + (direction * frameDuration));
        }
        
        // Log instructions
        console.log(`
🎯 BLOCKER 1 SKELETON RENDERING TEST

This test validates that the pose skeleton overlay rendering works exactly like dev-app-smoothnet.html:

1. ✅ Keypoint filtering (skip eyes, mouth, fingers)
2. ✅ 18 skeleton connections 
3. ✅ Coordinate scaling from Modal resolution to canvas
4. ✅ Confidence-based coloring (green/orange/red)
5. ✅ Invalid keypoint handling (0,0 coordinates)
6. ✅ SmoothNet branding overlay
7. ✅ Real-time rendering at 30fps

Files being tested:
- Modal Data: ../d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json
- Video: ../Michael_test_side.mp4

Expected result: Full pose skeleton with colored keypoints and cyan connection lines.
        `);
    </script>
</body>
</html>