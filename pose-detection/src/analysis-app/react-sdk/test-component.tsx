/**
 * Test Component for ProcessedVideoPlayer
 * 
 * This component demonstrates how to use the ProcessedVideoPlayer
 * with the same data that works in dev-app-smoothnet.html
 */

import React from 'react';
import { ProcessedVideoPlayer } from './components/ProcessedVideoPlayer';
import { ProcessedPoseData } from './types/pose';

/**
 * Test component to verify ProcessedVideoPlayer compatibility
 */
export const ProcessedVideoPlayerTest: React.FC = () => {
  // URLs matching dev-app-smoothnet.html
  const videoUrl = './Michael_test_side.mp4';
  const resultsUrl = './d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json';
  
  // Handler functions
  const handlePoseData = (data: ProcessedPoseData) => {
    console.log('Pose data received:', {
      frameNumber: data.frameNumber,
      timestamp: data.timestamp,
      keypointCount: data.keypoints?.length,
      smoothed: data.smoothed
    });
  };
  
  const handleMetrics = (metrics: any) => {
    console.log('Metrics received:', metrics);
  };
  
  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 mb-2">
          ProcessedVideoPlayer Test
        </h1>
        <p className="text-gray-600">
          Testing React component with same data as dev-app-smoothnet.html
        </p>
      </div>
      
      <div className="bg-white rounded-lg shadow-lg p-6">
        <div className="mb-4">
          <h2 className="text-lg font-semibold text-gray-800 mb-2">
            Side View - Modal SmoothNet Results
          </h2>
          <div className="text-sm text-gray-600 space-y-1">
            <div><strong>Video:</strong> {videoUrl}</div>
            <div><strong>Pose Data:</strong> {resultsUrl}</div>
            <div><strong>Model:</strong> BlazePose Full with SmoothNet</div>
          </div>
        </div>
        
        <ProcessedVideoPlayer
          videoUrl={videoUrl}
          resultsUrl={resultsUrl}
          analysisType="running"
          viewType="side"
          overlayStyle="medical"
          userHeight={{ feet: 5, inches: 10 }}
          onPoseData={handlePoseData}
          onMetrics={handleMetrics}
          autoPlay={false}
          controls={true}
          loop={false}
        />
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-semibold text-blue-900 mb-2">Test Instructions:</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>1. Component should load without errors</li>
            <li>2. Video player should appear with controls</li>
            <li>3. Pose data should load from JSON file</li>
            <li>4. Pose overlay should sync with video playback</li>
            <li>5. Check browser console for pose data logs</li>
          </ul>
        </div>
        
        <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
          <h3 className="font-semibold text-yellow-900 mb-2">Compatibility Notes:</h3>
          <ul className="text-sm text-yellow-800 space-y-1">
            <li>• Uses same video file: Michael_test_side.mp4</li>
            <li>• Uses same pose data: d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json</li>
            <li>• Implements same video/canvas synchronization logic</li>
            <li>• Filters same keypoints (eyes, mouth, fingers)</li>
            <li>• Same pose overlay rendering with color coding</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

/**
 * Alternative test with different overlay styles
 */
export const ProcessedVideoPlayerStyleTest: React.FC = () => {
  const videoUrl = './Michael_test_side.mp4';
  const resultsUrl = './d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json';
  
  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">
        ProcessedVideoPlayer Overlay Styles Test
      </h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Medical Style */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Medical Style</h2>
          <ProcessedVideoPlayer
            videoUrl={videoUrl}
            resultsUrl={resultsUrl}
            analysisType="running"
            viewType="side"
            overlayStyle="medical"
            userHeight={{ feet: 5, inches: 10 }}
          />
        </div>
        
        {/* Athletic Style */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Athletic Style</h2>
          <ProcessedVideoPlayer
            videoUrl={videoUrl}
            resultsUrl={resultsUrl}
            analysisType="running"
            viewType="side"
            overlayStyle="athletic"
            userHeight={{ feet: 5, inches: 10 }}
          />
        </div>
        
        {/* Minimal Style */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Minimal Style</h2>
          <ProcessedVideoPlayer
            videoUrl={videoUrl}
            resultsUrl={resultsUrl}
            analysisType="running"
            viewType="side"
            overlayStyle="minimal"
            userHeight={{ feet: 5, inches: 10 }}
          />
        </div>
        
        {/* Debug Style */}
        <div className="bg-white rounded-lg shadow-lg p-4">
          <h2 className="text-lg font-semibold mb-4">Debug Style</h2>
          <ProcessedVideoPlayer
            videoUrl={videoUrl}
            resultsUrl={resultsUrl}
            analysisType="running"
            viewType="side"
            overlayStyle="debug"
            userHeight={{ feet: 5, inches: 10 }}
          />
        </div>
      </div>
    </div>
  );
};