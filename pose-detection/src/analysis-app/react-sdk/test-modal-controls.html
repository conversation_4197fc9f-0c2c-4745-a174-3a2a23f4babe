<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Controls - React SDK</title>
    
    <!-- React and dependencies -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .test-controls button {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-controls button:hover {
            background: #0056b3;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .status.ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.info {
            background: #cce5ff;
            color: #004085;
        }
        
        #app {
            min-height: 400px;
        }
        
        /* Import the component styles */
        @import url('./components/ProcessedVideoPlayer.css');
        @import url('./components/VideoControls.css');
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Modal Controls - React SDK Phase 6.2</h1>
            <p>Testing manual frame controls and metrics overlay with pre-processed Modal data</p>
        </div>
        
        <div class="content">
            <div class="test-controls">
                <button onclick="loadVideo()">Load Test Video</button>
                <button onclick="toggleControls()">Toggle Controls</button>
                <button onclick="toggleMetrics()">Toggle Metrics</button>
                <button onclick="toggleFrameInfo()">Toggle Frame Info</button>
            </div>
            
            <div id="status" class="status info">Ready to test...</div>
            
            <div id="app"></div>
        </div>
    </div>

    <script type="text/babel">
        const { useState, useCallback } = React;
        
        // Mock the imports - in real app these would be ES modules
        const ProcessedVideoPlayerModal = ({ 
            videoUrl, 
            poseDataUrl, 
            showControls, 
            showMetricsOverlay, 
            showFrameInfo,
            onFrameChange 
        }) => {
            return React.createElement('div', { 
                style: { 
                    padding: '20px', 
                    background: '#e3f2fd', 
                    borderRadius: '8px',
                    textAlign: 'center',
                    minHeight: '400px'
                } 
            }, 
                React.createElement('h3', null, 'ProcessedVideoPlayerModal Component'),
                React.createElement('p', null, `Video: ${videoUrl}`),
                React.createElement('p', null, `Pose Data: ${poseDataUrl}`),
                React.createElement('p', null, `Controls: ${showControls ? 'ON' : 'OFF'}`),
                React.createElement('p', null, `Metrics: ${showMetricsOverlay ? 'ON' : 'OFF'}`),
                React.createElement('p', null, `Frame Info: ${showFrameInfo ? 'ON' : 'OFF'}`),
                React.createElement('div', {
                    style: {
                        marginTop: '20px',
                        padding: '10px',
                        background: '#fff',
                        borderRadius: '4px'
                    }
                }, 'Component would render here with video player, controls, and overlay')
            );
        };
        
        // Test App Component
        const TestApp = () => {
            const [showControls, setShowControls] = useState(true);
            const [showMetricsOverlay, setShowMetricsOverlay] = useState(true);
            const [showFrameInfo, setShowFrameInfo] = useState(true);
            const [videoLoaded, setVideoLoaded] = useState(false);
            
            const handleFrameChange = useCallback((frameData) => {
                console.log('Frame changed:', frameData);
                updateStatus(`Frame: ${frameData?.frameNumber || 'N/A'} @ ${frameData?.timestamp?.toFixed(2) || 'N/A'}s`, 'info');
            }, []);
            
            if (!videoLoaded) {
                return React.createElement('div', {
                    style: {
                        padding: '40px',
                        textAlign: 'center',
                        background: '#f5f5f5',
                        borderRadius: '8px'
                    }
                }, 
                    React.createElement('p', null, 'Click "Load Test Video" to begin testing')
                );
            }
            
            return React.createElement(ProcessedVideoPlayerModal, {
                videoUrl: '../Michael_test_side.mp4',
                poseDataUrl: '../d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json',
                showControls: showControls,
                showMetricsOverlay: showMetricsOverlay,
                showFrameInfo: showFrameInfo,
                onFrameChange: handleFrameChange,
                autoPlay: false,
                loop: true
            });
        };
        
        // Global state for demo controls
        let app;
        
        function renderApp() {
            const root = ReactDOM.createRoot(document.getElementById('app'));
            root.render(React.createElement(TestApp, { ref: (ref) => app = ref }));
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function loadVideo() {
            updateStatus('Loading video and Modal data...', 'info');
            // In real implementation, this would trigger the video load
            setTimeout(() => {
                renderApp();
                updateStatus('Video loaded with manual controls and metrics overlay', 'ready');
            }, 500);
        }
        
        function toggleControls() {
            if (!app) return;
            app.setShowControls(prev => !prev);
            updateStatus(`Controls: ${app.showControls ? 'Hidden' : 'Shown'}`, 'info');
        }
        
        function toggleMetrics() {
            if (!app) return;
            app.setShowMetricsOverlay(prev => !prev);
            updateStatus(`Metrics Overlay: ${app.showMetricsOverlay ? 'Hidden' : 'Shown'}`, 'info');
        }
        
        function toggleFrameInfo() {
            if (!app) return;
            app.setShowFrameInfo(prev => !prev);
            updateStatus(`Frame Info: ${app.showFrameInfo ? 'Hidden' : 'Shown'}`, 'info');
        }
        
        // Initial render
        renderApp();
    </script>
    
    <script>
        // Instructions for testing
        console.log(`
=== Modal Controls Test Instructions ===

1. Click "Load Test Video" to initialize the component
2. Use the toggle buttons to test different display modes
3. Check the console for frame change events

Features to verify:
- Manual frame controls (step forward/backward)
- Fixed 0.25x playback speed
- Metrics overlay on canvas
- Frame-accurate seeking
- Keyboard shortcuts (Space, Arrow keys)

Note: This is a mock implementation for testing the interface.
The actual implementation would load real video and pose data.
        `);
    </script>
</body>
</html>