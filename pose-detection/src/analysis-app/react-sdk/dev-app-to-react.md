# dev-app-smoothnet.html to React Components Migration Guide

## 🚨 CRITICAL ISSUES TO FIX

### 1. **Video Aspect Ratio - PORTRAIT NOT LANDSCAPE**
- **Current Issue**: Our demo shows 16:9 landscape video
- **Required**: 9:16 portrait ratio (

)
- **Code Reference**: Lines 76-91, 975-994
- **Fix**: Ensure video container maintains portrait orientation with proper responsive sizing

### 2. **Canvas Overlay Synchronization**
- **Critical Function**: `syncCanvasWithVideo()` (lines 976-994)
- **Requirements**:
  - Canvas must match video display size EXACTLY
  - Canvas position absolute over video
  - Update on video load and window resize
  - Set both canvas internal size AND CSS size
  ```javascript
  canvas.width = videoRect.width;
  canvas.height = videoRect.height;
  canvas.style.width = videoRect.width + 'px';
  canvas.style.height = videoRect.height + 'px';
  ```

### 3. **Video Playback Rate**
- **Line 936**: `video.playbackRate = 0.25;`
- **Critical**: Must be 0.25x to sync with pose overlay timing
- **Note**: "Video playback rate set to 0.25x to synchronize with pose overlay"

## 📊 DATA PROCESSING FEATURES

### 4. **Modal Data Structure**
- **Lines 689-728**: Loading pre-processed Modal/SmoothNet data
- **Key Properties**:
  - `modalData.video` - original video filename
  - `modalData.videoWidth` / `modalData.videoHeight` - dimensions
  - `modalData.fps` - frame rate
  - `modalData.frames` - array of pose data
  - `modalData.modelType` - "BlazePose Full (39 keypoints) + SmoothNet"
  - `modalData.processingTime`

### 5. **Frame Finding Logic**
- **Lines 731-771**: Frame finding functions
  - `getValidFrames()` - Skip frames with all zeros
  - `findFrameForTime(currentTime)` - Find closest frame by timestamp
  - `findFrameByIndex(index)` - Get frame by index
  - Must filter out invalid frames (coordinates at 0,0)

### 6. **Coordinate Validation**
- **Lines 773-809**: `getValidKeypoints()` function
- **Handles**:
  - Invalid keypoints (both x,y = 0.0)
  - Edge cases (only x or y = 0.0)
  - Marks keypoints with `isInvalid` or `isEdgeCase` flags
  - Reduces confidence for edge cases

### 7. **Coordinate Scaling**
- **Lines 1213-1215**: Critical scaling from video to canvas
  ```javascript
  const scaleX = canvas.width / modalData.videoWidth;
  const scaleY = canvas.height / modalData.videoHeight;
  ```
- **Applied to all keypoint coordinates before drawing**

## 🎨 VISUALIZATION FEATURES

### 8. **Keypoint Filtering (SPEC Requirement)**
- **Lines 1206-1210**: Skip specific keypoints
  ```javascript
  const skipKeypoints = [
    1, 2, 3, 4, 5, 6,    // eyes
    9, 10,               // mouth
    17, 18, 19, 20, 21, 22  // fingers
  ];
  ```
- **Only show**: Nose, Ears, Shoulders, Elbows, Wrists, Hips, Knees, Ankles, Heels, Toes

### 9. **Color Coding System**
- **Lines 1236-1245**: Confidence-based colors
  - Green (#00ff00): High confidence (>0.8)
  - Orange (#ffaa00): Medium confidence (0.5-0.8)
  - Red (#ff0000): Low confidence (<0.5)
  - Orange (#ff8800): Edge cases (partial coordinates)

### 10. **Drawing Components**
- **Keypoint Drawing** (lines 1246-1262):
  - Circle with white outline
  - Radius: 6px (8px for edge cases)
  - Display keypoint index
- **Skeleton Connections** (lines 1389-1417):
  - Bright cyan lines (#00ffff)
  - 3px line width
  - Specific connection pairs for body structure
- **Bounding Box** (lines 1419-1454):
  - Cyan outline around detected person
  - 20px padding
  - Center point indicator

### 11. **Overlay Information**
- **Lines 372-379**: Pose overlay info display
  - FPS counter
  - Current frame number
  - Pose score (percentage)
  - Keypoint count (valid/total)
  - SmoothNet indicator
- **Lines 1280-1285**: SmoothNet branding overlay

## 📏 HEIGHT CALIBRATION SYSTEM

### 12. **User Height Input**
- **Lines 320-339**: Height input UI
  - Imperial (feet/inches) and Metric (cm) toggle
  - Validation ranges: 3-8 feet or 90-250 cm
  - Conversion functions (lines 461-470)
  - Storage in cm internally

### 13. **CoordinateScaler Class**
- **Lines 473-572**: Real-world measurement calibration
  - Uses nose-to-hip distance (~38% of height)
  - Calculates pixels per cm
  - `calibrate(pose)` - calibration function
  - `scaleDistance(pixelDistance)` - convert pixels to cm
  - `formatDistance(cm)` - format for display (imperial/metric)
  - Max 150 calibration attempts (~5 seconds)

### 14. **Running Metrics Display**
- **Lines 1288-1336**: `displayRunningMetrics()`
  - Stride length calculation (heel to heel)
  - Step height (ankle separation)
  - Real-world measurements using CoordinateScaler
  - Overlay display on canvas

## 🔧 TECHNICAL FEATURES

### 15. **Dimension Validation**
- **Lines 941-969**: Video dimension checking
  - Compare video dimensions to Modal data dimensions
  - Display prominent warning if mismatch
  - Red warning overlay on canvas
  - Critical for accurate coordinate mapping

### 16. **Status Updates**
- Multiple status indicators:
  - `modelStatus` - Modal data loading status
  - `detectionStage` - Current processing stage
  - `personDetected` - Frame validity
  - `confidence` - Average keypoint confidence
  - `roiCenter` - Region of interest center

### 17. **Debug Information**
- **Lines 839-866**: JSON-based logging system
  - Structured log entries with timestamp
  - Debug info panel (lines 259-269)
  - Keep last 10 entries
  - Formatted JSON output

### 18. **Frame Processing Loop**
- **Lines 1024-1054**: `processLoop()`
  - 30fps frame rate limiting (33ms between frames)
  - RequestAnimationFrame usage
  - Pause/resume handling

### 19. **Error States**
- Invalid coordinate warnings (lines 1266-1272)
- Missing frame data handling
- Dimension mismatch alerts
- No Modal data checks

### 20. **UI Responsiveness**
- **Lines 53-57**: Mobile responsive design
  - Video section flex direction changes
  - Max width constraints for mobile
  - Proper scaling for different screen sizes

## ⚠️ MISSING FROM REACT COMPONENTS

1. **Portrait video ratio handling** - Currently showing landscape
2. **0.25x playback rate** - Critical for synchronization
3. **Height calibration system** - CoordinateScaler class
4. **Invalid coordinate handling** - Edge cases and (0,0) detection
5. **Dimension validation** - Warning when video doesn't match pose data
6. **Keypoint filtering** - Skip eyes, mouth, fingers per SPEC
7. **Real-world measurements** - Stride length, step height display
8. **Debug logging system** - Structured JSON logs
9. **Frame finding by timestamp** - Not just by index
10. **SmoothNet branding overlay** - Visual indicator
11. **Confidence-based coloring** - Different colors for different confidence levels
12. **Keypoint info cards** - Detailed keypoint status display
13. **30fps frame limiting** - Performance optimization
14. **Mobile responsiveness** - Proper portrait video on mobile
15. **Multiple overlay styles removed** - Only need 'medical' style

## 🎯 IMPLEMENTATION PRIORITIES

### High Priority (Breaking Issues):
1. Fix video aspect ratio to 9:16 portrait
2. Implement 0.25x playback rate
3. Add proper canvas synchronization
4. Add coordinate scaling from video to canvas

### Medium Priority (Functionality):
5. Add keypoint filtering (skip eyes, mouth, fingers)
6. Implement invalid coordinate detection
7. Add confidence-based coloring
8. Add dimension validation warnings

### Low Priority (Enhancements):
9. Height calibration system
10. Real-world measurements
11. Debug logging system
12. Keypoint info cards

## 📝 NOTES

- The HTML uses vanilla JavaScript with direct DOM manipulation
- Our React components need to convert these to hooks and state management
- Pay special attention to the portrait video handling - this is currently broken
- The 0.25x playback rate is CRITICAL for pose synchronization
- All coordinate scaling must account for video-to-canvas size differences
