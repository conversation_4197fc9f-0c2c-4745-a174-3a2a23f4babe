# How to View the Visual Demo

The visual demo HTML file is located at:
```
/Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app/react-sdk/visual-demo.html
```

## Option 1: Direct File Opening
```bash
# From anywhere on your system
open /Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app/react-sdk/visual-demo.html
```

## Option 2: Using Python Server (Recommended)
```bash
# Navigate to the react-sdk directory
cd /Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app/react-sdk

# Run the server script (it will auto-open your browser)
python3 serve-demo.py
```

## Option 3: Using Python's Built-in Server
```bash
# Navigate to the react-sdk directory
cd /Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app/react-sdk

# Start a simple HTTP server
python3 -m http.server 8000

# Then open in your browser:
# http://localhost:8000/visual-demo.html
```

## Option 4: Using Node's HTTP Server
```bash
# If you have Node.js http-server installed globally
cd /Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app/react-sdk
npx http-server -p 8080

# Then open in your browser:
# http://localhost:8080/visual-demo.html
```

## Option 5: Drag and Drop
1. Open Finder
2. Navigate to: `/Users/<USER>/Desktop/PeakInsight-MaxWattz/tfjs-models/pose-detection/src/analysis-app/react-sdk/`
3. Find `visual-demo.html`
4. Drag it into your browser window

## What You'll See:
- Interactive visual demonstration of all Phase 1-5 components
- Progress indicators showing 45% completion
- Mock components showing the UI structure
- Architecture overview and code examples
- No actual functionality - just visual representation

The demo uses React from CDN and Tailwind CSS, so it should work in any modern browser without any build process.