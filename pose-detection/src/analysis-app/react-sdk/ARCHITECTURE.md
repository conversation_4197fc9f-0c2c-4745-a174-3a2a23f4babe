# React SDK Architecture - Visual Overview

## 📊 Current Implementation Status (Phases 1-5)

```
┌─────────────────────────────────────────────────────────────────┐
│                     React SDK (45% Complete)                     │
├─────────────────────────────────────────────────────────────────┤
│                                                                  │
│  ✅ Phase 1: Foundation        ✅ Phase 4: Hooks               │
│  ├── package.json              ├── useBlazePose                │
│  ├── tsconfig.json             ├── useAnalysisData             │
│  └── index.ts                  └── useSupabaseAnalysis         │
│                                                                  │
│  ✅ Phase 2: Types             ✅ Phase 5: ResultsPage         │
│  ├── pose.ts                   ├── Header & Navigation         │
│  ├── analysis.ts               ├── Tab Switching               │
│  ├── metrics.ts                ├── Video Integration           │
│  └── components.ts             └── Metrics Panel               │
│                                                                  │
│  ✅ Phase 3: Video Player      ⏳ Phases 6-11 (Pending)        │
│  ├── Canvas Overlay            ├── MetricsDisplay              │
│  ├── Pose Rendering            ├── TabsContainer               │
│  ├── Playback Controls         ├── Utilities                   │
│  └── Synchronization           └── Production Integration       │
│                                                                  │
└─────────────────────────────────────────────────────────────────┘
```

## 🏗️ Component Architecture

```
ResultsPage (Phase 5)
    │
    ├── PageHeader
    │   ├── Title & Breadcrumbs
    │   └── Action Buttons (Back, Export, New)
    │
    ├── ViewTabs
    │   ├── Side View Tab
    │   └── Rear View Tab
    │
    ├── Content Grid
    │   ├── ProcessedVideoPlayer (Phase 3)
    │   │   ├── Video Element
    │   │   ├── Canvas Overlay
    │   │   └── Controls
    │   │
    │   └── Metrics Panel
    │       ├── Overall Score
    │       ├── Running Metrics
    │       └── Analysis Info
    │
    └── State Management
        ├── useAnalysisData (Phase 4)
        ├── useSupabaseAnalysis (Phase 4)
        └── Local State (tabs, etc.)
```

## 🔄 Data Flow

```
1. User navigates to ResultsPage with analysisId
                    ↓
2. useAnalysisData hook fetches from Supabase (mock)
                    ↓
3. Data transformed to SDK format
                    ↓
4. ResultsPage renders appropriate state:
   - Loading → LoadingState component
   - Error → ErrorState component  
   - Processing → ProcessingStatus component
   - Completed → Full results UI
                    ↓
5. ProcessedVideoPlayer loads video & pose data
                    ↓
6. Canvas overlay renders pose keypoints
                    ↓
7. Metrics calculated and displayed
```

## 📁 File Structure

```
react-sdk/
├── components/
│   ├── ProcessedVideoPlayer.tsx  ✅ (Phase 3)
│   ├── ResultsPage.tsx          ✅ (Phase 5)
│   ├── TabsContainer.tsx        ⏳ (Phase 6)
│   ├── MetricsDisplay.tsx       ⏳ (Phase 6)
│   └── index.ts
│
├── hooks/
│   ├── useBlazePose.ts          ✅ (Phase 4)
│   ├── useAnalysisData.ts       ✅ (Phase 4)
│   ├── useSupabaseAnalysis.ts   ✅ (Phase 4)
│   └── index.ts
│
├── types/
│   ├── pose.ts                  ✅ (Phase 2)
│   ├── analysis.ts              ✅ (Phase 2)
│   ├── metrics.ts               ✅ (Phase 2)
│   ├── supabase.ts              ✅ (Phase 2)
│   ├── components.ts            ✅ (Phase 2)
│   └── index.ts
│
├── utils/                       ⏳ (Phase 7)
│   ├── coordinates.ts
│   ├── runningMetrics.ts
│   └── index.ts
│
├── package.json                 ✅ (Phase 1)
├── tsconfig.json               ✅ (Phase 1)
├── README.md                   ✅ (Phase 1)
└── index.ts                    ✅ (Phase 1)
```

## 🎨 Visual Components

### ProcessedVideoPlayer (Phase 3)
- **Purpose**: Display video with pose overlay
- **Features**: 
  - Video/canvas synchronization
  - Pose keypoint rendering
  - Playback controls
  - 0.25x playback rate for analysis

### ResultsPage (Phase 5)
- **Purpose**: Main results display container
- **Features**:
  - Multiple view states (loading, error, processing, completed)
  - Tab-based navigation (Side/Rear views)
  - Responsive grid layout
  - Export functionality

## 🪝 Hook Architecture

### useBlazePose
- Wraps BlazePose detector
- Memory management
- Frame-by-frame pose detection
- Model initialization

### useAnalysisData
- Fetches analysis from database
- Auto-refresh for processing analyses
- Data transformation
- Error handling

### useSupabaseAnalysis
- CRUD operations
- Real-time subscriptions
- Status updates
- Mock implementation (Phase 8 will add real client)

## 🚀 Usage Example

```typescript
import { ResultsPage } from '@tfjs-models/pose-detection/react-sdk';

function AnalysisResultsPage({ analysisId }) {
  return (
    <ResultsPage
      analysisId={analysisId}
      userEmail="<EMAIL>"
      onBack={() => router.back()}
      onNewAnalysis={() => router.push('/upload')}
      onExport={(format) => downloadAnalysis(format)}
    />
  );
}
```

## ✅ What's Working Now

1. **Complete TypeScript support** - All components and hooks fully typed
2. **Video playback with pose overlay** - ProcessedVideoPlayer renders poses on canvas
3. **State management** - Hooks handle all data fetching and state
4. **Responsive UI** - Tailwind CSS for mobile-first design
5. **Error handling** - Graceful fallbacks for all error states

## ⏳ Next Steps (Phases 6-11)

- **Phase 6**: Create MetricsDisplay and TabsContainer components
- **Phase 7**: Implement utility functions for metrics calculations
- **Phase 8**: Replace mock Supabase with real implementation
- **Phase 9**: Finalize styling system
- **Phase 10**: Clean up exports and documentation
- **Phase 11**: Integration testing with main app