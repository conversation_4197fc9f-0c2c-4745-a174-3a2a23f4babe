/**
 * CoordinateScaler Class
 * 
 * Handles real-world measurement calibration based on user height, weight, and gender
 * Ported from dev-app-smoothnet.html with enhanced features
 */

import { Keypoint } from '../types/pose';

export interface UserProfile {
  height: {
    value: number;
    unit: 'cm' | 'inches';
  };
  weight: {
    value: number;
    unit: 'kg' | 'lbs';
  };
  gender: 'male' | 'female' | 'other';
  facingDirection?: 'left' | 'right'; // Critical for heel vs toe identification
}

export interface CalibrationResult {
  success: boolean;
  pixelsPerCm: number | null;
  message: string;
  confidence: number;
}

export interface ScaledKeypoint extends Keypoint {
  realWorldX?: number; // in cm
  realWorldY?: number; // in cm
  realWorldZ?: number; // in cm (if available)
}

export class CoordinateScaler {
  private userProfile: UserProfile;
  private pixelsPerCm: number | null = null;
  private calibrated: boolean = false;
  private calibrationAttempts: number = 0;
  private readonly maxCalibrationAttempts: number = 150; // ~5 seconds at 30fps
  
  // Camera distance assumptions (5 feet away as per user instructions)
  private readonly CAMERA_DISTANCE_FEET = 5;
  private readonly CAMERA_DISTANCE_METERS = 1.524; // 5 feet in meters
  
  // Gender-based anatomical proportions (adjusted for 5-foot camera distance)
  private readonly anatomicalProportions = {
    male: {
      noseToHip: 0.38,      // 38% of height
      shoulderWidth: 0.26,   // 26% of height
      hipWidth: 0.20,        // 20% of height
      strideRatio: 1.1,      // Average stride as ratio of height
      perspectiveCorrection: 1.02 // Slight correction for 5-foot distance
    },
    female: {
      noseToHip: 0.37,      // 37% of height
      shoulderWidth: 0.24,   // 24% of height
      hipWidth: 0.22,        // 22% of height
      strideRatio: 1.05,     // Average stride as ratio of height
      perspectiveCorrection: 1.02 // Slight correction for 5-foot distance
    },
    other: {
      noseToHip: 0.375,     // Average of male/female
      shoulderWidth: 0.25,   // Average of male/female
      hipWidth: 0.21,        // Average of male/female
      strideRatio: 1.075,    // Average of male/female
      perspectiveCorrection: 1.02 // Slight correction for 5-foot distance
    }
  };

  constructor(userProfile: UserProfile) {
    this.userProfile = userProfile;
  }

  /**
   * Get user height in centimeters
   */
  private getUserHeightCm(): number {
    const { height } = this.userProfile;
    if (height.unit === 'cm') {
      return height.value;
    } else {
      // Convert inches to cm
      return height.value * 2.54;
    }
  }

  /**
   * Get user weight in kilograms
   */
  private getUserWeightKg(): number {
    const { weight } = this.userProfile;
    if (weight.unit === 'kg') {
      return weight.value;
    } else {
      // Convert lbs to kg
      return weight.value * 0.453592;
    }
  }

  /**
   * Calibrate the scaler using detected pose keypoints
   * Uses nose-to-hip distance for calibration
   */
  calibrate(keypoints: Keypoint[]): CalibrationResult {
    // Find required keypoints
    const nose = keypoints.find(kp => kp.name === 'nose');
    const leftHip = keypoints.find(kp => kp.name === 'left_hip');
    const rightHip = keypoints.find(kp => kp.name === 'right_hip');
    
    if (!nose || !leftHip || !rightHip) {
      this.calibrationAttempts++;
      return {
        success: false,
        pixelsPerCm: null,
        message: 'Required keypoints not detected',
        confidence: 0
      };
    }
    
    // Check confidence scores
    const minConfidence = 0.5;
    if (nose.score! < minConfidence || leftHip.score! < minConfidence || rightHip.score! < minConfidence) {
      this.calibrationAttempts++;
      return {
        success: false,
        pixelsPerCm: null,
        message: 'Keypoint confidence too low',
        confidence: Math.min(nose.score!, leftHip.score!, rightHip.score!)
      };
    }
    
    // Calculate hip midpoint
    const hipMidpoint = {
      x: (leftHip.x + rightHip.x) / 2,
      y: (leftHip.y + rightHip.y) / 2
    };
    
    // Vertical distance in pixels
    const pixelDistance = Math.abs(nose.y - hipMidpoint.y);
    
    // Check if person is reasonably upright (nose above hips)
    if (nose.y < hipMidpoint.y && pixelDistance > 50) {
      // Get gender-specific proportion
      const proportions = this.anatomicalProportions[this.userProfile.gender];
      const estimatedDistanceCm = this.getUserHeightCm() * proportions.noseToHip;
      
      // Apply perspective correction for 5-foot camera distance
      // At 5 feet, there's minimal perspective distortion, but slight adjustment needed
      const correctedDistanceCm = estimatedDistanceCm * proportions.perspectiveCorrection;
      
      // Calculate scaling factor with distance correction
      this.pixelsPerCm = pixelDistance / correctedDistanceCm;
      this.calibrated = true;
      
      const confidence = Math.min(nose.score!, leftHip.score!, rightHip.score!);
      
      return {
        success: true,
        pixelsPerCm: this.pixelsPerCm,
        message: `Calibration successful: ${this.pixelsPerCm.toFixed(2)} pixels/cm`,
        confidence
      };
    }
    
    this.calibrationAttempts++;
    
    if (this.calibrationAttempts >= this.maxCalibrationAttempts) {
      return {
        success: false,
        pixelsPerCm: null,
        message: 'Calibration failed after maximum attempts - ensure person is standing upright',
        confidence: 0
      };
    }
    
    return {
      success: false,
      pixelsPerCm: null,
      message: 'Person not in upright position for calibration',
      confidence: 0
    };
  }

  /**
   * Convert pixel distance to real-world distance in centimeters
   */
  scaleDistance(pixelDistance: number): number | null {
    if (!this.calibrated || !this.pixelsPerCm) return null;
    return pixelDistance / this.pixelsPerCm;
  }

  /**
   * Format distance for display based on user preference
   */
  formatDistance(cm: number): string {
    const { height } = this.userProfile;
    
    if (height.unit === 'inches') {
      const inches = cm / 2.54;
      if (inches < 12) {
        return `${inches.toFixed(1)}"`;
      } else {
        const feet = Math.floor(inches / 12);
        const remainingInches = (inches % 12).toFixed(1);
        return `${feet}'${remainingInches}"`;
      }
    } else {
      if (cm < 100) {
        return `${cm.toFixed(1)} cm`;
      } else {
        const meters = cm / 100;
        return `${meters.toFixed(2)} m`;
      }
    }
  }

  /**
   * Get scaled keypoints with real-world measurements
   */
  getScaledKeypoints(keypoints: Keypoint[]): ScaledKeypoint[] {
    if (!this.calibrated || !this.pixelsPerCm) {
      return keypoints as ScaledKeypoint[];
    }
    
    return keypoints.map(kp => ({
      ...kp,
      realWorldX: kp.x / this.pixelsPerCm!,
      realWorldY: kp.y / this.pixelsPerCm!,
      realWorldZ: kp.z ? kp.z / this.pixelsPerCm! : undefined
    }));
  }

  /**
   * Calculate stride length considering user facing direction
   * Critical for distinguishing heel from toe
   */
  calculateStrideLength(leftAnkle: Keypoint, rightAnkle: Keypoint, 
                       leftToe?: Keypoint, rightToe?: Keypoint): number | null {
    if (!this.calibrated || !this.pixelsPerCm) return null;
    
    // Determine which points to use based on facing direction
    let leftPoint = leftAnkle;
    let rightPoint = rightAnkle;
    
    if (this.userProfile.facingDirection && leftToe && rightToe) {
      // If facing left, use heel (ankle) for back foot, toe for front foot
      // If facing right, opposite
      const useToesForStride = this.shouldUseToesForStride(
        leftAnkle, rightAnkle, leftToe, rightToe
      );
      
      if (useToesForStride.left) leftPoint = leftToe;
      if (useToesForStride.right) rightPoint = rightToe;
    }
    
    // Calculate horizontal distance
    const pixelDistance = Math.abs(leftPoint.x - rightPoint.x);
    return this.scaleDistance(pixelDistance);
  }

  /**
   * Determine whether to use toes or heels for stride measurement
   * based on facing direction and foot positions
   */
  private shouldUseToesForStride(
    leftAnkle: Keypoint, 
    rightAnkle: Keypoint,
    leftToe: Keypoint, 
    rightToe: Keypoint
  ): { left: boolean; right: boolean } {
    const facingLeft = this.userProfile.facingDirection === 'left';
    
    // Determine which foot is forward
    const leftIsForward = facingLeft ? 
      leftAnkle.x < rightAnkle.x : 
      leftAnkle.x > rightAnkle.x;
    
    return {
      left: leftIsForward, // Use toe for forward foot
      right: !leftIsForward // Use toe for forward foot
    };
  }

  /**
   * Get expected stride length based on user profile
   */
  getExpectedStrideLength(): number {
    const heightCm = this.getUserHeightCm();
    const proportions = this.anatomicalProportions[this.userProfile.gender];
    return heightCm * proportions.strideRatio / 100; // Convert to meters
  }

  /**
   * Calculate vertical oscillation (bounce) during running
   */
  calculateVerticalOscillation(
    currentHipY: number, 
    previousHipY: number
  ): number | null {
    if (!this.calibrated || !this.pixelsPerCm) return null;
    
    const pixelDifference = Math.abs(currentHipY - previousHipY);
    return this.scaleDistance(pixelDifference);
  }

  /**
   * Reset calibration
   */
  reset(): void {
    this.calibrated = false;
    this.calibrationAttempts = 0;
    this.pixelsPerCm = null;
  }

  /**
   * Check if calibrated
   */
  isCalibrated(): boolean {
    return this.calibrated;
  }

  /**
   * Get calibration status
   */
  getCalibrationStatus(): {
    calibrated: boolean;
    attempts: number;
    pixelsPerCm: number | null;
  } {
    return {
      calibrated: this.calibrated,
      attempts: this.calibrationAttempts,
      pixelsPerCm: this.pixelsPerCm
    };
  }

  /**
   * Update user profile (e.g., if user changes facing direction)
   */
  updateUserProfile(updates: Partial<UserProfile>): void {
    this.userProfile = { ...this.userProfile, ...updates };
  }
}