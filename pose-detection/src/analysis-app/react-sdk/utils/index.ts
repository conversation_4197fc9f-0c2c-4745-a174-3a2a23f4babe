/**
 * Utility functions and classes for the React SDK
 */

export { CoordinateScaler, type UserProfile, type CalibrationResult, type ScaledKeypoint } from './CoordinateScaler';
export { RunningMetricsCalculator, type RunningMetricsConfig } from './runningMetrics';

// Re-export coordinate utilities from existing implementation
export { 
  calculateDistance,
  calculateAngle,
  getKeypointByName,
  validateEssentialKeypoints
} from '../../utils/coordinates';

/**
 * Convert centimeters to inches
 */
export const cmToInches = (cm: number): number => {
  return cm / 2.54;
};

/**
 * Convert inches to centimeters
 */
export const inchesToCm = (inches: number): number => {
  return inches * 2.54;
};

/**
 * Convert kilograms to pounds
 */
export const kgToLbs = (kg: number): number => {
  return kg * 2.20462;
};

/**
 * Convert pounds to kilograms
 */
export const lbsToKg = (lbs: number): number => {
  return lbs / 2.20462;
};

/**
 * Format height for display
 */
export const formatHeight = (cm: number, unit: 'cm' | 'inches'): string => {
  if (unit === 'cm') {
    return `${cm.toFixed(1)} cm`;
  } else {
    const totalInches = cmToInches(cm);
    const feet = Math.floor(totalInches / 12);
    const inches = Math.round(totalInches % 12);
    return `${feet}'${inches}"`;
  }
};

/**
 * Format weight for display
 */
export const formatWeight = (kg: number, unit: 'kg' | 'lbs'): string => {
  if (unit === 'kg') {
    return `${kg.toFixed(1)} kg`;
  } else {
    const lbs = kgToLbs(kg);
    return `${lbs.toFixed(1)} lbs`;
  }
};

/**
 * Validate that required keypoints for running analysis are present
 */
export const validateRunningKeypoints = (keypoints: any[], minConfidence: number = 0.5): {
  isValid: boolean;
  missingKeypoints: string[];
  message: string;
} => {
  const requiredKeypoints = [
    'nose',
    'left_shoulder', 'right_shoulder',
    'left_hip', 'right_hip',
    'left_knee', 'right_knee',
    'left_ankle', 'right_ankle',
    'left_heel', 'right_heel',
    'left_foot_index', 'right_foot_index'
  ];

  const missingKeypoints: string[] = [];
  
  for (const requiredName of requiredKeypoints) {
    const keypoint = keypoints.find(kp => kp.name === requiredName);
    
    if (!keypoint || !keypoint.score || keypoint.score < minConfidence) {
      missingKeypoints.push(requiredName);
    }
  }

  const isValid = missingKeypoints.length === 0;
  const message = isValid 
    ? 'All required keypoints detected' 
    : `Missing keypoints: ${missingKeypoints.join(', ')}`;

  return { isValid, missingKeypoints, message };
};

/**
 * Determine user facing direction from pose keypoints
 * Returns 'left' if person is facing left, 'right' if facing right
 */
export const detectFacingDirection = (keypoints: any[]): 'left' | 'right' | 'unknown' => {
  const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');
  const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');
  const nose = keypoints.find(kp => kp.name === 'nose');
  
  if (!leftShoulder || !rightShoulder || !nose) {
    return 'unknown';
  }
  
  // Calculate shoulder midpoint
  const shoulderMidX = (leftShoulder.x + rightShoulder.x) / 2;
  
  // If nose is to the left of shoulder midpoint, person is facing left
  // If nose is to the right of shoulder midpoint, person is facing right
  if (nose.x < shoulderMidX - 10) { // 10px threshold
    return 'left';
  } else if (nose.x > shoulderMidX + 10) {
    return 'right';
  }
  
  // If nose is roughly centered, check shoulder visibility
  // The shoulder closer to camera will have higher confidence
  if (leftShoulder.score > rightShoulder.score + 0.1) {
    return 'right'; // Left shoulder more visible = facing right
  } else if (rightShoulder.score > leftShoulder.score + 0.1) {
    return 'left'; // Right shoulder more visible = facing left
  }
  
  return 'unknown';
};

/**
 * Calculate running speed from stride length and cadence
 * Returns speed in meters per second
 */
export const calculateRunningSpeed = (
  strideLengthMeters: number, 
  cadenceSPM: number
): number => {
  // Convert cadence from steps/min to steps/sec
  const stepsPerSecond = cadenceSPM / 60;
  
  // Speed = stride length * steps per second / 2 (since each step is half a stride cycle)
  return (strideLengthMeters * stepsPerSecond) / 2;
};

/**
 * Format running speed for display
 */
export const formatSpeed = (
  metersPerSecond: number, 
  unit: 'metric' | 'imperial'
): string => {
  if (unit === 'metric') {
    const kmPerHour = metersPerSecond * 3.6;
    return `${kmPerHour.toFixed(1)} km/h`;
  } else {
    const milesPerHour = metersPerSecond * 2.23694;
    return `${milesPerHour.toFixed(1)} mph`;
  }
};

/**
 * Calculate running pace from speed
 * Returns pace as minutes per kilometer or mile
 */
export const calculatePace = (
  metersPerSecond: number,
  unit: 'metric' | 'imperial'
): { minutes: number; seconds: number; formatted: string } => {
  if (metersPerSecond === 0) {
    return { minutes: 0, seconds: 0, formatted: '--:--' };
  }
  
  let secondsPerUnit: number;
  
  if (unit === 'metric') {
    // Seconds per kilometer
    secondsPerUnit = 1000 / metersPerSecond;
  } else {
    // Seconds per mile
    secondsPerUnit = 1609.34 / metersPerSecond;
  }
  
  const minutes = Math.floor(secondsPerUnit / 60);
  const seconds = Math.round(secondsPerUnit % 60);
  
  const formatted = `${minutes}:${seconds.toString().padStart(2, '0')}`;
  
  return { minutes, seconds, formatted };
};