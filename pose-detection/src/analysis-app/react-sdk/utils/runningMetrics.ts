/**
 * Enhanced Running Metrics Calculator
 * 
 * Calculates biomechanical metrics with user profile consideration
 * Including weight, gender, and facing direction for accurate analysis
 */

import { Keypoint, ProcessedPoseData } from '../types/pose';
import { RunningMetrics, MetricValue } from '../types/metrics';
import { CoordinateScaler, UserProfile } from './CoordinateScaler';

export interface RunningMetricsConfig {
  userProfile: UserProfile;
  fps?: number; // Frame rate, default 30
  minConfidence?: number; // Minimum keypoint confidence, default 0.5
}

interface FrameAnalysis {
  timestamp: number;
  frameNumber: number;
  leftFootContact: boolean;
  rightFootContact: boolean;
  stridePhase: 'stance' | 'swing' | 'transition';
  keyMeasurements: {
    strideLength?: number;
    stepHeight?: number;
    kneeFlexionAngle?: number;
    hipAngle?: number;
    ankleAngle?: number;
    verticalOscillation?: number;
    lateralMovement?: number;
  };
}

export class RunningMetricsCalculator {
  private config: RunningMetricsConfig;
  private scaler: CoordinateScaler;
  private frameHistory: ProcessedPoseData[] = [];
  private analysisHistory: FrameAnalysis[] = [];
  
  // Analysis parameters
  private readonly HISTORY_SIZE = 90; // 3 seconds at 30 FPS
  private readonly GROUND_CONTACT_THRESHOLD = 0.95; // Relative position threshold
  
  constructor(config: RunningMetricsConfig) {
    this.config = {
      fps: 30,
      minConfidence: 0.5,
      ...config
    };
    this.scaler = new CoordinateScaler(config.userProfile);
  }

  /**
   * Add new pose frame for analysis
   */
  addFrame(poseData: ProcessedPoseData): RunningMetrics | null {
    // Skip invalid frames
    if (!poseData.keypoints || poseData.keypoints.length === 0) {
      return null;
    }

    // Calibrate scaler if not already calibrated
    if (!this.scaler.isCalibrated()) {
      const calibrationResult = this.scaler.calibrate(poseData.keypoints);
      if (!calibrationResult.success) {
        console.log('Calibrating...', calibrationResult.message);
        return null;
      }
    }

    // Add to history
    this.frameHistory.push(poseData);
    if (this.frameHistory.length > this.HISTORY_SIZE) {
      this.frameHistory.shift();
    }

    // Analyze current frame
    const frameAnalysis = this.analyzeFrame(poseData);
    this.analysisHistory.push(frameAnalysis);
    if (this.analysisHistory.length > this.HISTORY_SIZE) {
      this.analysisHistory.shift();
    }

    // Calculate metrics if we have enough data
    if (this.frameHistory.length >= 30) { // At least 1 second of data
      return this.calculateMetrics();
    }

    return null;
  }

  /**
   * Analyze single frame for running parameters
   */
  private analyzeFrame(poseData: ProcessedPoseData): FrameAnalysis {
    const { keypoints, frameNumber, timestamp } = poseData;
    
    // Get key points with proper naming
    const leftAnkle = keypoints.find(kp => kp.name === 'left_ankle');
    const rightAnkle = keypoints.find(kp => kp.name === 'right_ankle');
    const leftToe = keypoints.find(kp => kp.name === 'left_foot_index');
    const rightToe = keypoints.find(kp => kp.name === 'right_foot_index');
    const leftHeel = keypoints.find(kp => kp.name === 'left_heel');
    const rightHeel = keypoints.find(kp => kp.name === 'right_heel');
    const leftKnee = keypoints.find(kp => kp.name === 'left_knee');
    const rightKnee = keypoints.find(kp => kp.name === 'right_knee');
    const leftHip = keypoints.find(kp => kp.name === 'left_hip');
    const rightHip = keypoints.find(kp => kp.name === 'right_hip');

    const analysis: FrameAnalysis = {
      timestamp,
      frameNumber,
      leftFootContact: false,
      rightFootContact: false,
      stridePhase: 'transition',
      keyMeasurements: {}
    };

    // Analyze foot contact using heel/toe based on facing direction
    if (leftAnkle && rightAnkle) {
      analysis.leftFootContact = this.isFootInContact(leftAnkle, leftHeel, leftToe);
      analysis.rightFootContact = this.isFootInContact(rightAnkle, rightHeel, rightToe);
      
      // Determine stride phase
      if (analysis.leftFootContact && analysis.rightFootContact) {
        analysis.stridePhase = 'stance';
      } else if (!analysis.leftFootContact && !analysis.rightFootContact) {
        analysis.stridePhase = 'swing';
      } else {
        analysis.stridePhase = 'transition';
      }
    }

    // Calculate stride length with facing direction consideration
    if (leftAnkle && rightAnkle) {
      const strideLength = this.scaler.calculateStrideLength(
        leftAnkle, rightAnkle, leftToe, rightToe
      );
      if (strideLength !== null) {
        analysis.keyMeasurements.strideLength = strideLength / 100; // Convert to meters
      }
    }

    // Calculate step height (vertical distance between ankles)
    if (leftAnkle && rightAnkle) {
      const stepHeight = this.scaler.scaleDistance(
        Math.abs(leftAnkle.y - rightAnkle.y)
      );
      if (stepHeight !== null) {
        analysis.keyMeasurements.stepHeight = stepHeight;
      }
    }

    // Calculate knee flexion angle
    if (leftKnee && leftHip && leftAnkle) {
      analysis.keyMeasurements.kneeFlexionAngle = this.calculateAngle(
        leftHip, leftKnee, leftAnkle
      );
    }

    // Calculate hip angle
    if (leftHip && leftKnee && rightHip) {
      const shoulderApprox = { 
        x: leftHip.x, 
        y: leftHip.y - 30, // Approximate shoulder position
        score: leftHip.score 
      };
      analysis.keyMeasurements.hipAngle = this.calculateAngle(
        shoulderApprox, leftHip, leftKnee
      );
    }

    // Calculate vertical oscillation if we have previous frame
    if (this.analysisHistory.length > 0 && leftHip && rightHip) {
      const hipMidpointY = (leftHip.y + rightHip.y) / 2;
      const prevFrame = this.frameHistory[this.frameHistory.length - 2];
      
      if (prevFrame) {
        const prevLeftHip = prevFrame.keypoints.find(kp => kp.name === 'left_hip');
        const prevRightHip = prevFrame.keypoints.find(kp => kp.name === 'right_hip');
        
        if (prevLeftHip && prevRightHip) {
          const prevHipMidpointY = (prevLeftHip.y + prevRightHip.y) / 2;
          const vertOsc = this.scaler.calculateVerticalOscillation(
            hipMidpointY, prevHipMidpointY
          );
          if (vertOsc !== null) {
            analysis.keyMeasurements.verticalOscillation = vertOsc;
          }
        }
      }
    }

    return analysis;
  }

  /**
   * Determine if foot is in contact with ground
   * Uses heel/toe based on facing direction
   */
  private isFootInContact(
    ankle: Keypoint, 
    heel?: Keypoint, 
    toe?: Keypoint
  ): boolean {
    // Find the lowest point in the frame to establish ground level
    const allKeypoints = this.frameHistory[this.frameHistory.length - 1]?.keypoints || [];
    const lowestY = Math.max(
      ...allKeypoints
        .filter(kp => kp.score && kp.score > this.config.minConfidence!)
        .map(kp => kp.y)
    );
    
    // Use heel/toe based on facing direction if available
    let footPoint = ankle;
    if (this.config.userProfile.facingDirection === 'left' && heel && heel.score! > this.config.minConfidence!) {
      footPoint = heel; // Use heel when facing left
    } else if (this.config.userProfile.facingDirection === 'right' && toe && toe.score! > this.config.minConfidence!) {
      footPoint = toe; // Use toe when facing right
    }
    
    // Check if foot is near ground level
    const relativePosition = footPoint.y / lowestY;
    return relativePosition > this.GROUND_CONTACT_THRESHOLD;
  }

  /**
   * Calculate angle between three keypoints in degrees
   */
  private calculateAngle(
    point1: Keypoint,
    vertex: Keypoint,
    point3: Keypoint
  ): number {
    const a = { x: point1.x - vertex.x, y: point1.y - vertex.y };
    const b = { x: point3.x - vertex.x, y: point3.y - vertex.y };
    
    const dotProduct = a.x * b.x + a.y * b.y;
    const magnitudeA = Math.sqrt(a.x * a.x + a.y * a.y);
    const magnitudeB = Math.sqrt(b.x * b.x + b.y * b.y);
    
    if (magnitudeA === 0 || magnitudeB === 0) return 0;
    
    const cosAngle = dotProduct / (magnitudeA * magnitudeB);
    const angleRadians = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
    
    return angleRadians * (180 / Math.PI);
  }

  /**
   * Calculate comprehensive running metrics from frame history
   */
  private calculateMetrics(): RunningMetrics {
    const recentFrames = this.analysisHistory.slice(-60); // Last 2 seconds
    
    // Calculate averages and peaks
    const strideLengths = recentFrames
      .map(f => f.keyMeasurements.strideLength)
      .filter((s): s is number => s !== undefined);
    
    const avgStrideLength = strideLengths.length > 0 
      ? strideLengths.reduce((sum, s) => sum + s, 0) / strideLengths.length 
      : 0;

    const stepHeights = recentFrames
      .map(f => f.keyMeasurements.stepHeight)
      .filter((s): s is number => s !== undefined);
    
    const avgStepHeight = stepHeights.length > 0
      ? stepHeights.reduce((sum, s) => sum + s, 0) / stepHeights.length
      : 0;

    const verticalOscillations = recentFrames
      .map(f => f.keyMeasurements.verticalOscillation)
      .filter((v): v is number => v !== undefined);
    
    const avgVerticalOscillation = verticalOscillations.length > 0
      ? verticalOscillations.reduce((sum, v) => sum + v, 0) / verticalOscillations.length
      : 0;

    // Calculate cadence
    const cadence = this.calculateCadence();

    // Calculate ground contact time
    const groundContactTime = this.calculateGroundContactTime();

    // Get expected values based on user profile
    const expectedStrideLength = this.scaler.getExpectedStrideLength();

    return {
      cadence: this.createMetricValue(
        cadence, 'spm', 
        this.scoreCadence(cadence),
        this.getCadenceDescription(cadence)
      ),
      strideLength: this.createMetricValue(
        avgStrideLength, 'm',
        this.scoreStrideLength(avgStrideLength, expectedStrideLength),
        this.getStrideLengthDescription(avgStrideLength, expectedStrideLength)
      ),
      stepHeight: this.createMetricValue(
        avgStepHeight, 'cm',
        this.scoreStepHeight(avgStepHeight),
        this.getStepHeightDescription(avgStepHeight)
      ),
      verticalOscillation: this.createMetricValue(
        avgVerticalOscillation, 'cm',
        this.scoreVerticalOscillation(avgVerticalOscillation),
        this.getVerticalOscillationDescription(avgVerticalOscillation)
      ),
      groundContactTime: this.createMetricValue(
        groundContactTime, 'ms',
        this.scoreGroundContactTime(groundContactTime),
        this.getGroundContactTimeDescription(groundContactTime)
      ),
      overallScore: this.calculateOverallScore({
        cadence, 
        strideLength: avgStrideLength, 
        stepHeight: avgStepHeight,
        verticalOscillation: avgVerticalOscillation,
        groundContactTime
      }, expectedStrideLength)
    };
  }

  /**
   * Calculate running cadence from foot contact patterns
   */
  private calculateCadence(): number {
    const recentFrames = this.analysisHistory.slice(-90); // 3 seconds
    if (recentFrames.length < 30) return 0;

    let stepCount = 0;
    let previousLeftContact = false;
    let previousRightContact = false;

    for (const frame of recentFrames) {
      // Count step when foot contact changes from false to true
      if (frame.leftFootContact && !previousLeftContact) stepCount++;
      if (frame.rightFootContact && !previousRightContact) stepCount++;
      
      previousLeftContact = frame.leftFootContact;
      previousRightContact = frame.rightFootContact;
    }

    // Convert to steps per minute
    const timeSpanSeconds = recentFrames.length / this.config.fps!;
    return stepCount > 0 ? (stepCount / timeSpanSeconds) * 60 : 0;
  }

  /**
   * Calculate average ground contact time
   */
  private calculateGroundContactTime(): number {
    const recentFrames = this.analysisHistory.slice(-90); // 3 seconds
    if (recentFrames.length < 10) return 0;

    const contactDurations: number[] = [];
    let currentContactStart = -1;

    for (let i = 0; i < recentFrames.length; i++) {
      const frame = recentFrames[i];
      const isInContact = frame.leftFootContact || frame.rightFootContact;

      if (isInContact && currentContactStart === -1) {
        currentContactStart = i;
      } else if (!isInContact && currentContactStart !== -1) {
        const duration = (i - currentContactStart) * (1000 / this.config.fps!); // Convert to milliseconds
        contactDurations.push(duration);
        currentContactStart = -1;
      }
    }

    return contactDurations.length > 0
      ? contactDurations.reduce((sum, d) => sum + d, 0) / contactDurations.length
      : 0;
  }

  /**
   * Create metric value object
   */
  private createMetricValue(
    value: number, 
    unit: string, 
    score: number, 
    description: string
  ): MetricValue {
    return {
      value,
      unit,
      score,
      status: this.getMetricStatus(score),
      description
    };
  }

  /**
   * Get metric status based on score
   */
  private getMetricStatus(score: number): 'excellent' | 'good' | 'needs-work' {
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    return 'needs-work';
  }

  /**
   * Calculate overall running efficiency score
   */
  private calculateOverallScore(
    metrics: {
      cadence: number;
      strideLength: number;
      stepHeight: number;
      verticalOscillation: number;
      groundContactTime: number;
    },
    expectedStrideLength: number
  ): number {
    const scores = [
      this.scoreCadence(metrics.cadence) * 0.25,
      this.scoreStrideLength(metrics.strideLength, expectedStrideLength) * 0.25,
      this.scoreStepHeight(metrics.stepHeight) * 0.15,
      this.scoreVerticalOscillation(metrics.verticalOscillation) * 0.20,
      this.scoreGroundContactTime(metrics.groundContactTime) * 0.15
    ];
    
    return Math.round(scores.reduce((sum, score) => sum + score, 0));
  }

  // Scoring functions (0-100 scale) with user profile consideration
  private scoreCadence(value: number): number {
    const optimal = 180;
    const diff = Math.abs(value - optimal);
    return Math.max(0, 100 - (diff * 0.5));
  }

  private scoreStrideLength(value: number, expected: number): number {
    const diff = Math.abs(value - expected);
    return Math.max(0, 100 - (diff * 100)); // Penalize 1% per cm difference
  }

  private scoreStepHeight(value: number): number {
    const optimal = 8; // 8cm optimal step height
    const diff = Math.abs(value - optimal);
    return Math.max(0, 100 - (diff * 5));
  }

  private scoreVerticalOscillation(value: number): number {
    // Lower is better, optimal around 6-8cm
    if (value < 6) return 100;
    if (value > 12) return 50;
    return Math.max(0, 100 - ((value - 6) * 8));
  }

  private scoreGroundContactTime(value: number): number {
    const optimal = 200; // 200ms optimal
    const diff = Math.abs(value - optimal);
    return Math.max(0, 100 - (diff / 2));
  }

  // Description generators
  private getCadenceDescription(value: number): string {
    if (value > 190) return 'High cadence - excellent for efficiency';
    if (value > 175) return 'Good cadence - near optimal range';
    if (value > 160) return 'Consider increasing cadence slightly';
    return 'Low cadence - aim for 170-180 steps/min';
  }

  private getStrideLengthDescription(value: number, expected: number): string {
    const diff = value - expected;
    if (Math.abs(diff) < 0.05) return 'Optimal stride length for your height';
    if (diff > 0.1) return 'Stride length longer than optimal - may cause overstriding';
    if (diff < -0.1) return 'Stride length shorter than optimal - consider lengthening';
    return 'Stride length near optimal range';
  }

  private getStepHeightDescription(value: number): string {
    if (value < 5) return 'Low step height - may indicate shuffling';
    if (value > 12) return 'High step height - consider reducing vertical movement';
    return 'Good step height - efficient movement pattern';
  }

  private getVerticalOscillationDescription(value: number): string {
    if (value < 6) return 'Excellent - minimal vertical movement';
    if (value < 9) return 'Good - efficient vertical oscillation';
    if (value < 12) return 'Consider reducing bounce for efficiency';
    return 'High vertical oscillation - energy being wasted upward';
  }

  private getGroundContactTimeDescription(value: number): string {
    if (value < 180) return 'Very quick ground contact - excellent';
    if (value < 220) return 'Good ground contact time';
    if (value < 250) return 'Slightly long ground contact';
    return 'Long ground contact - work on quick turnover';
  }

  /**
   * Reset calculator
   */
  reset(): void {
    this.frameHistory = [];
    this.analysisHistory = [];
    this.scaler.reset();
  }

  /**
   * Update user profile
   */
  updateUserProfile(updates: Partial<UserProfile>): void {
    this.config.userProfile = { ...this.config.userProfile, ...updates };
    this.scaler.updateUserProfile(updates);
  }
}