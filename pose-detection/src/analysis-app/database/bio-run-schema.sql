-- MaxWattz Bio Run Analysis Database Schema
-- Server-side processing pipeline with SmoothNet integration

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================
-- Table: bio_run_videos
-- Stores video file metadata separately
-- ============================================
CREATE TABLE bio_run_videos (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- File Information
    filename TEXT NOT NULL,
    original_filename TEXT NOT NULL,
    file_size_bytes BIGINT NOT NULL,
    mime_type TEXT NOT NULL,
    
    -- S3 Storage
    s3_bucket TEXT NOT NULL,
    s3_key TEXT NOT NULL,
    s3_url TEXT NOT NULL,
    s3_upload_completed BOOLEAN DEFAULT FALSE,
    
    -- Video Metadata
    duration_seconds REAL NOT NULL,
    width INTEGER NOT NULL,
    height INTEGER NOT NULL,
    fps REAL NOT NULL,
    codec TEXT,
    bitrate INTEGER,
    
    -- Analysis Type
    view_type TEXT CHECK (view_type IN ('side', 'rear')) NOT NULL,
    
    -- Validation
    is_valid BOOLEAN DEFAULT TRUE,
    validation_errors JSONB,
    
    -- Temporary user tracking (until auth system)
    user_email TEXT NOT NULL,
    session_id TEXT NOT NULL -- Groups side/rear videos together
);

CREATE INDEX idx_bio_run_videos_session ON bio_run_videos(session_id);
CREATE INDEX idx_bio_run_videos_user_email ON bio_run_videos(user_email);
CREATE INDEX idx_bio_run_videos_created ON bio_run_videos(created_at DESC);

-- ============================================
-- Table: bio_run_analysis
-- Main analysis sessions with runner biometrics
-- ============================================
CREATE TABLE bio_run_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- User Information (temporary until auth)
    user_email TEXT NOT NULL,
    
    -- Runner Biometrics
    height_inches REAL NOT NULL,
    height_display TEXT NOT NULL, -- "5'10"" or "1m75cm"
    gender TEXT CHECK (gender IN ('male', 'female')) NOT NULL,
    weight_lbs REAL NOT NULL,
    weight_display TEXT NOT NULL, -- "150 lbs" or "68 kg"
    
    -- Video References
    side_video_id UUID REFERENCES bio_run_videos(id),
    rear_video_id UUID REFERENCES bio_run_videos(id),
    
    -- Processing Status
    status TEXT CHECK (status IN (
        'uploading',
        'pending',
        'processing_side',
        'processing_rear',
        'completed',
        'error'
    )) DEFAULT 'uploading',
    processing_started_at TIMESTAMPTZ,
    processing_completed_at TIMESTAMPTZ,
    error_message TEXT,
    error_details JSONB,
    
    -- SmoothNet Processed Results (Full JSON)
    side_analysis_json JSONB, -- Complete smoothed keypoints from SmoothNet
    rear_analysis_json JSONB,
    
    -- S3 Backup URLs (optional)
    side_metrics_s3_url TEXT,
    rear_metrics_s3_url TEXT,
    
    -- Processing Metadata
    modal_job_id TEXT,
    processing_duration_seconds INTEGER,
    model_version TEXT DEFAULT 'blazepose_heavy',
    smoothnet_version TEXT,
    
    -- Extracted Summary Metrics
    cadence_avg REAL,
    stride_length_avg REAL,
    vertical_oscillation_avg REAL,
    ground_contact_time_avg REAL,
    lean_angle_avg REAL,
    
    -- Analysis Summary
    analysis_summary TEXT,
    key_findings JSONB,
    
    -- Quality Metrics
    side_detection_confidence REAL,
    rear_detection_confidence REAL,
    side_frames_processed INTEGER,
    rear_frames_processed INTEGER,
    
    CONSTRAINT valid_video_refs CHECK (
        side_video_id IS NOT NULL OR rear_video_id IS NOT NULL
    ),
    CONSTRAINT valid_timestamps CHECK (
        (processing_started_at IS NULL OR processing_started_at >= created_at) AND
        (processing_completed_at IS NULL OR processing_completed_at >= processing_started_at)
    )
);

CREATE INDEX idx_bio_run_analysis_user_email ON bio_run_analysis(user_email);
CREATE INDEX idx_bio_run_analysis_status ON bio_run_analysis(status);
CREATE INDEX idx_bio_run_analysis_created ON bio_run_analysis(created_at DESC);

-- ============================================
-- Table: bio_run_upload_sessions
-- Temporary tracking during video upload
-- ============================================
CREATE TABLE bio_run_upload_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    analysis_id UUID REFERENCES bio_run_analysis(id) ON DELETE CASCADE,
    
    -- Upload Progress
    side_video_uploaded BOOLEAN DEFAULT FALSE,
    rear_video_uploaded BOOLEAN DEFAULT FALSE,
    upload_completed BOOLEAN DEFAULT FALSE,
    
    -- Temporary S3 keys for multipart uploads
    side_upload_id TEXT, -- S3 multipart upload ID
    rear_upload_id TEXT,
    side_temp_key TEXT,
    rear_temp_key TEXT,
    
    -- Session Management
    session_token TEXT UNIQUE DEFAULT gen_random_uuid()::TEXT,
    expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '2 hours'),
    
    -- Upload Metadata
    upload_started_at TIMESTAMPTZ,
    side_upload_completed_at TIMESTAMPTZ,
    rear_upload_completed_at TIMESTAMPTZ
);

CREATE INDEX idx_bio_run_upload_sessions_analysis ON bio_run_upload_sessions(analysis_id);
CREATE INDEX idx_bio_run_upload_sessions_token ON bio_run_upload_sessions(session_token);
CREATE INDEX idx_bio_run_upload_sessions_expires ON bio_run_upload_sessions(expires_at);

-- ============================================
-- Table: bio_modal_processing_queue
-- Modal GPU job queue management
-- ============================================
CREATE TABLE bio_modal_processing_queue (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Analysis Reference
    analysis_id UUID REFERENCES bio_run_analysis(id) ON DELETE CASCADE,
    video_id UUID REFERENCES bio_run_videos(id),
    view_type TEXT CHECK (view_type IN ('side', 'rear')) NOT NULL,
    
    -- Queue Status
    status TEXT CHECK (status IN (
        'queued',
        'processing',
        'completed',
        'failed'
    )) DEFAULT 'queued',
    priority INTEGER DEFAULT 0, -- Higher = more priority
    
    -- Modal Job Details
    modal_job_id TEXT,
    modal_function_name TEXT DEFAULT 'process_running_video',
    modal_request_payload JSONB,
    modal_response_payload JSONB,
    
    -- Processing Timeline
    queued_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    
    -- Error Handling
    error_message TEXT,
    error_details JSONB,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    last_retry_at TIMESTAMPTZ,
    
    -- Performance Tracking
    gpu_time_seconds REAL,
    total_time_seconds REAL,
    frames_processed INTEGER,
    
    CONSTRAINT unique_analysis_view UNIQUE (analysis_id, view_type)
);

CREATE INDEX idx_bio_modal_queue_status ON bio_modal_processing_queue(status);
CREATE INDEX idx_bio_modal_queue_analysis ON bio_modal_processing_queue(analysis_id);
CREATE INDEX idx_bio_modal_queue_priority ON bio_modal_processing_queue(priority DESC, queued_at);

-- ============================================
-- Table: bio_run_analysis_metrics
-- Frame-by-frame keypoint data (optional detail storage)
-- ============================================
CREATE TABLE bio_run_analysis_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    analysis_id UUID REFERENCES bio_run_analysis(id) ON DELETE CASCADE,
    view_type TEXT CHECK (view_type IN ('side', 'rear')) NOT NULL,
    
    -- Frame Information
    frame_number INTEGER NOT NULL,
    timestamp_ms REAL NOT NULL,
    
    -- Smoothed Keypoints from SmoothNet
    -- Array of 33 keypoints: [[x,y,z,confidence], ...]
    keypoints JSONB NOT NULL,
    
    -- Derived Metrics
    stride_phase TEXT CHECK (stride_phase IN (
        'contact',
        'midstance',
        'propulsion',
        'swing'
    )),
    ground_contact_left BOOLEAN,
    ground_contact_right BOOLEAN,
    
    -- Biomechanics
    center_of_mass_x REAL,
    center_of_mass_y REAL,
    center_of_mass_z REAL,
    lean_angle REAL,
    
    -- Joint Angles
    hip_angle_left REAL,
    hip_angle_right REAL,
    knee_angle_left REAL,
    knee_angle_right REAL,
    ankle_angle_left REAL,
    ankle_angle_right REAL,
    
    -- Performance
    instantaneous_velocity REAL,
    vertical_displacement REAL,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    
    CONSTRAINT unique_frame_per_analysis UNIQUE (analysis_id, view_type, frame_number)
);

CREATE INDEX idx_bio_run_metrics_analysis ON bio_run_analysis_metrics(analysis_id);
CREATE INDEX idx_bio_run_metrics_frame ON bio_run_analysis_metrics(analysis_id, view_type, frame_number);

-- ============================================
-- Update Triggers
-- ============================================
CREATE OR REPLACE FUNCTION update_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER bio_run_videos_updated_at
    BEFORE UPDATE ON bio_run_videos
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

CREATE TRIGGER bio_run_analysis_updated_at
    BEFORE UPDATE ON bio_run_analysis
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at();

-- ============================================
-- Helper Functions
-- ============================================

-- Function to create a new analysis session
CREATE OR REPLACE FUNCTION create_bio_run_analysis(
    p_user_email TEXT,
    p_height_inches REAL,
    p_height_display TEXT,
    p_gender TEXT,
    p_weight_lbs REAL,
    p_weight_display TEXT
)
RETURNS TABLE (
    analysis_id UUID,
    upload_session_id UUID,
    session_token TEXT
) AS $$
DECLARE
    v_analysis_id UUID;
    v_upload_session_id UUID;
    v_session_token TEXT;
BEGIN
    -- Create analysis record
    INSERT INTO bio_run_analysis (
        user_email, height_inches, height_display,
        gender, weight_lbs, weight_display, status
    ) VALUES (
        p_user_email, p_height_inches, p_height_display,
        p_gender, p_weight_lbs, p_weight_display, 'uploading'
    ) RETURNING id INTO v_analysis_id;
    
    -- Create upload session
    INSERT INTO bio_run_upload_sessions (analysis_id)
    VALUES (v_analysis_id)
    RETURNING id, session_token INTO v_upload_session_id, v_session_token;
    
    RETURN QUERY SELECT v_analysis_id, v_upload_session_id, v_session_token;
END;
$$ LANGUAGE plpgsql;

-- Function to queue video for processing
CREATE OR REPLACE FUNCTION queue_video_processing(
    p_analysis_id UUID,
    p_video_id UUID,
    p_view_type TEXT
)
RETURNS UUID AS $$
DECLARE
    v_queue_id UUID;
BEGIN
    INSERT INTO bio_modal_processing_queue (
        analysis_id, video_id, view_type, status
    ) VALUES (
        p_analysis_id, p_video_id, p_view_type, 'queued'
    ) RETURNING id INTO v_queue_id;
    
    -- Update analysis status
    UPDATE bio_run_analysis
    SET status = CASE 
        WHEN p_view_type = 'side' THEN 'processing_side'
        ELSE 'processing_rear'
    END
    WHERE id = p_analysis_id;
    
    RETURN v_queue_id;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup expired upload sessions
CREATE OR REPLACE FUNCTION cleanup_expired_upload_sessions()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM bio_run_upload_sessions
    WHERE expires_at < NOW()
    AND upload_completed = FALSE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================
-- Row Level Security (RLS)
-- ============================================
ALTER TABLE bio_run_videos ENABLE ROW LEVEL SECURITY;
ALTER TABLE bio_run_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE bio_run_upload_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE bio_modal_processing_queue ENABLE ROW LEVEL SECURITY;
ALTER TABLE bio_run_analysis_metrics ENABLE ROW LEVEL SECURITY;

-- Temporary public access (will restrict when auth is added)
CREATE POLICY "Public access for bio_run_videos" ON bio_run_videos FOR ALL USING (true);
CREATE POLICY "Public access for bio_run_analysis" ON bio_run_analysis FOR ALL USING (true);
CREATE POLICY "Public access for bio_run_upload_sessions" ON bio_run_upload_sessions FOR ALL USING (true);
CREATE POLICY "Public access for bio_modal_processing_queue" ON bio_modal_processing_queue FOR ALL USING (true);
CREATE POLICY "Public access for bio_run_analysis_metrics" ON bio_run_analysis_metrics FOR ALL USING (true);

-- ============================================
-- Helper Views
-- ============================================

-- View for active analyses with upload status
CREATE VIEW bio_run_active_analyses AS
SELECT 
    ba.*,
    us.side_video_uploaded,
    us.rear_video_uploaded,
    us.upload_completed,
    sv.filename as side_filename,
    rv.filename as rear_filename
FROM bio_run_analysis ba
LEFT JOIN bio_run_upload_sessions us ON ba.id = us.analysis_id
LEFT JOIN bio_run_videos sv ON ba.side_video_id = sv.id
LEFT JOIN bio_run_videos rv ON ba.rear_video_id = rv.id
WHERE ba.status != 'error'
ORDER BY ba.created_at DESC;

-- View for processing queue status
CREATE VIEW bio_run_processing_status AS
SELECT 
    ba.id,
    ba.user_email,
    ba.status as analysis_status,
    COUNT(pq.id) as total_jobs,
    COUNT(CASE WHEN pq.status = 'completed' THEN 1 END) as completed_jobs,
    COUNT(CASE WHEN pq.status = 'failed' THEN 1 END) as failed_jobs,
    COUNT(CASE WHEN pq.status = 'processing' THEN 1 END) as processing_jobs
FROM bio_run_analysis ba
LEFT JOIN bio_modal_processing_queue pq ON ba.id = pq.analysis_id
GROUP BY ba.id, ba.user_email, ba.status;

-- ============================================
-- Comments for documentation
-- ============================================
COMMENT ON TABLE bio_run_analysis IS 'Main table for running biomechanics analysis sessions';
COMMENT ON TABLE bio_run_videos IS 'Video file metadata and S3 storage information';
COMMENT ON TABLE bio_run_upload_sessions IS 'Temporary tracking for multi-video upload sessions';
COMMENT ON TABLE bio_modal_processing_queue IS 'GPU processing job queue for Modal';
COMMENT ON TABLE bio_run_analysis_metrics IS 'Optional detailed frame-by-frame metrics storage';

COMMENT ON COLUMN bio_run_analysis.side_analysis_json IS 'Complete SmoothNet-processed keypoints for side view';
COMMENT ON COLUMN bio_run_analysis.rear_analysis_json IS 'Complete SmoothNet-processed keypoints for rear view';
COMMENT ON COLUMN bio_modal_processing_queue.priority IS 'Higher values processed first. Default 0, premium users could be 10';

-- Example: Schedule cleanup via pg_cron (if available)
-- SELECT cron.schedule('cleanup-upload-sessions', '0 */6 * * *', 'SELECT cleanup_expired_upload_sessions();');