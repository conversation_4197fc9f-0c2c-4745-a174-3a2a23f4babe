<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxWattz - Test Processed Results</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
        }
        
        .test-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 16px;
        }
        
        .form-group label {
            display: block;
            font-weight: 600;
            margin-bottom: 6px;
            color: #333;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #4ecdc4;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
        
        .alert-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            color: #1976d2;
        }
        
        .alert-error {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
        }
        
        .video-container {
            margin-top: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">MaxWattz Running Analysis</div>
            <div class="subtitle">Processed Results Viewer - Testing SmoothNet Output</div>
        </div>
        
        <div id="root"></div>
    </div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function TestApp() {
            const [videoUrl, setVideoUrl] = useState('https://maxwattz-videos.s3.amazonaws.com/maxwattz-running-videos-raw-side/Michael_test_side.mp4');
            const [resultsUrl, setResultsUrl] = useState('./test-results.json');
            const [showPlayer, setShowPlayer] = useState(false);
            const [error, setError] = useState(null);
            const [poseData, setPoseData] = useState(null);

            const userHeight = { feet: 5, inches: 10 };

            const loadPlayer = () => {
                setError(null);
                setShowPlayer(true);
            };

            const handlePoseData = React.useCallback((data) => {
                setPoseData(data);
                console.log('Received pose data:', data);
            }, []);

            const validateUrls = () => {
                if (!videoUrl || !resultsUrl) {
                    setError('Both video URL and results URL are required');
                    return false;
                }
                
                if (!videoUrl.includes('.mp4')) {
                    setError('Video URL must point to an MP4 file');
                    return false;
                }
                
                if (!resultsUrl.includes('.json')) {
                    setError('Results URL must point to a JSON file');
                    return false;
                }
                
                return true;
            };

            const handleLoad = () => {
                if (validateUrls()) {
                    loadPlayer();
                }
            };

            return (
                <div>
                    <div className="test-section">
                        <h3 style={{marginBottom: '16px', color: '#333'}}>Test Configuration</h3>
                        
                        <div className="alert alert-info">
                            <strong>Test Purpose:</strong> Validate that SmoothNet-processed pose data displays correctly 
                            and matches the coordinate system expected by the frontend.
                        </div>
                        
                        <div className="form-group">
                            <label>Video URL (MP4):</label>
                            <input 
                                type="url"
                                value={videoUrl}
                                onChange={(e) => setVideoUrl(e.target.value)}
                                placeholder="https://maxwattz-videos.s3.amazonaws.com/maxwattz-running-videos-raw-side/video.mp4"
                            />
                        </div>
                        
                        <div className="form-group">
                            <label>Processed Results URL (JSON):</label>
                            <input 
                                type="url"
                                value={resultsUrl}
                                onChange={(e) => setResultsUrl(e.target.value)}
                                placeholder="./test-results.json or S3 URL"
                            />
                        </div>
                        
                        <div className="form-group">
                            <label>Or upload JSON file directly:</label>
                            <input 
                                type="file"
                                accept=".json"
                                onChange={(e) => {
                                    const file = e.target.files[0];
                                    if (file) {
                                        const url = URL.createObjectURL(file);
                                        setResultsUrl(url);
                                    }
                                }}
                                style={{padding: '8px'}}
                            />
                        </div>
                        
                        {error && (
                            <div className="alert alert-error">
                                {error}
                            </div>
                        )}
                        
                        <button 
                            className="btn"
                            onClick={handleLoad}
                            disabled={!videoUrl || !resultsUrl}
                        >
                            Load Processed Results
                        </button>
                    </div>

                    {showPlayer && (
                        <div className="test-section">
                            <h3 style={{marginBottom: '16px', color: '#333'}}>Processed Video Player</h3>
                            
                            <div className="alert alert-info">
                                <strong>What to look for:</strong>
                                <ul style={{margin: '8px 0', paddingLeft: '20px'}}>
                                    <li>Pose keypoints should align with the person in the video</li>
                                    <li>Skeleton connections should be smooth (thanks to SmoothNet)</li>
                                    <li>Blue indicator shows "SmoothNet Processed"</li>
                                    <li>Frame synchronization should be accurate</li>
                                </ul>
                            </div>
                            
                            <div className="video-container">
                                {React.createElement(ProcessedVideoPlayer, {
                                    key: `${videoUrl}-${resultsUrl}`, // Prevent unnecessary re-creation
                                    videoUrl: videoUrl,
                                    resultsUrl: resultsUrl,
                                    analysisType: "running",
                                    viewType: "side", 
                                    overlayStyle: "Medical",
                                    userHeight: userHeight,
                                    onPoseData: handlePoseData
                                })}
                            </div>
                            
                            {poseData && (
                                <div style={{marginTop: '16px', padding: '12px', background: '#f0f0f0', borderRadius: '6px'}}>
                                    <strong>Current Frame Data:</strong>
                                    <div style={{fontSize: '12px', marginTop: '4px', fontFamily: 'monospace'}}>
                                        Frame: {poseData.frameNumber} | 
                                        Confidence: {(poseData.score * 100).toFixed(1)}% |
                                        Smoothed: {poseData.smoothed ? 'Yes' : 'No'} |
                                        Model: {poseData.modelType}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            );
        }

        // Mock ProcessedVideoPlayer component for testing
        function ProcessedVideoPlayer({ videoUrl, resultsUrl, onPoseData }) {
            const [isLoading, setIsLoading] = useState(true);
            const [error, setError] = useState(null);
            const [processedData, setProcessedData] = useState(null);
            const [loadComplete, setLoadComplete] = useState(false);

            // Memoize the onPoseData callback to prevent re-renders
            const handlePoseData = React.useCallback((data) => {
                if (onPoseData) {
                    onPoseData(data);
                }
            }, [onPoseData]);

            useEffect(() => {
                let isMounted = true;
                
                async function loadData() {
                    if (!resultsUrl || loadComplete) return;
                    
                    try {
                        console.log('🔄 Loading data from:', resultsUrl);
                        setIsLoading(true);
                        setError(null);
                        
                        const response = await fetch(resultsUrl);
                        
                        if (!isMounted) return;
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                        
                        const data = await response.json();
                        
                        if (!isMounted) return;
                        
                        console.log('✅ Data loaded successfully:', {
                            modelType: data.modelType,
                            frameCount: data.frames?.length,
                            processingTime: data.processingTime
                        });
                        
                        setProcessedData(data);
                        setIsLoading(false);
                        setLoadComplete(true);
                        
                        // Call pose data callback once
                        if (data.frames && data.frames.length > 0) {
                            const firstFrame = data.frames[0];
                            const avgScore = firstFrame.keypoints.reduce((sum, kp) => sum + (kp.score || 0), 0) / firstFrame.keypoints.length;
                            
                            handlePoseData({
                                keypoints: firstFrame.keypoints,
                                score: avgScore,
                                timestamp: firstFrame.timestamp,
                                frameNumber: firstFrame.frameNumber,
                                modelType: data.modelType,
                                smoothed: true
                            });
                        }
                        
                    } catch (err) {
                        if (!isMounted) return;
                        
                        console.error('❌ Load failed:', err);
                        setError(err.message);
                        setIsLoading(false);
                    }
                }
                
                loadData();
                
                return () => {
                    isMounted = false;
                };
            }, [resultsUrl, handlePoseData, loadComplete]);

            console.log('ProcessedVideoPlayer render:', { isLoading, error: !!error, hasData: !!processedData, loadComplete });
            
            if (isLoading && !loadComplete) {
                return (
                    <div style={{padding: '40px', textAlign: 'center'}}>
                        <div style={{fontSize: '14px', color: '#666'}}>Loading processed results...</div>
                        <div style={{fontSize: '12px', color: '#999', marginTop: '8px'}}>From: {resultsUrl}</div>
                    </div>
                );
            }

            if (error) {
                return (
                    <div className="alert alert-error">
                        <strong>Error loading results:</strong> {error}
                    </div>
                );
            }

            if (!processedData) {
                return (
                    <div className="alert alert-error">
                        No processed data available
                    </div>
                );
            }

            return (
                <div>
                    <div style={{background: '#e8f5e8', padding: '12px', borderRadius: '6px', marginBottom: '16px'}}>
                        <strong>✅ Processed Results Loaded Successfully!</strong>
                        <div style={{fontSize: '12px', marginTop: '4px'}}>
                            Model: {processedData.modelType} | 
                            Frames: {processedData.frames.length} | 
                            Processing Time: {processedData.processingTime.toFixed(1)}s
                        </div>
                    </div>
                    
                    <video 
                        src={videoUrl} 
                        controls 
                        style={{width: '100%', maxHeight: '400px'}}
                    />
                    
                    <div style={{marginTop: '12px', fontSize: '12px', color: '#666'}}>
                        <strong>Note:</strong> This is a simplified test view. The full ProcessedVideoPlayer component 
                        would overlay pose keypoints synchronized with video playback.
                    </div>
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>