import React from 'react';
import { ViewType } from '../types';

interface TabsContainerProps {
    activeTab: ViewType;
    availableViews: ViewType[];
    onTabChange: (view: ViewType) => void;
}

export const TabsContainer: React.FC<TabsContainerProps> = ({
    activeTab,
    availableViews,
    onTabChange
}) => {
    const getTabLabel = (view: ViewType): string => {
        return view === 'side' ? 'Side View' : 'Rear View';
    };

    const getTabDescription = (view: ViewType): string => {
        if (view === 'side') {
            return 'Stride length, vertical oscillation, forward lean analysis';
        } else {
            return 'Hip drop, knee alignment, lateral movement analysis';
        }
    };

    const getTabIcon = (view: ViewType): string => {
        return view === 'side' ? '👤' : '🔄';
    };

    return (
        <div className="tabs-container">
            <div className="tabs-header">
                <div className="tabs-nav">
                    {availableViews.map((view) => (
                        <button
                            key={view}
                            className={`tab-button ${activeTab === view ? 'active' : ''}`}
                            onClick={() => onTabChange(view)}
                        >
                            <span className="tab-icon">{getTabIcon(view)}</span>
                            <div className="tab-content">
                                <span className="tab-label">{getTabLabel(view)}</span>
                                <span className="tab-description">{getTabDescription(view)}</span>
                            </div>
                        </button>
                    ))}
                </div>
                
                {availableViews.length < 2 && (
                    <div className="missing-view-notice">
                        {!availableViews.includes('side') && (
                            <div className="missing-tab">
                                <span className="tab-icon disabled">👤</span>
                                <div className="tab-content">
                                    <span className="tab-label disabled">Side View</span>
                                    <span className="tab-description disabled">Not processed yet</span>
                                </div>
                            </div>
                        )}
                        {!availableViews.includes('rear') && (
                            <div className="missing-tab">
                                <span className="tab-icon disabled">🔄</span>
                                <div className="tab-content">
                                    <span className="tab-label disabled">Rear View</span>
                                    <span className="tab-description disabled">Not processed yet</span>
                                </div>
                            </div>
                        )}
                    </div>
                )}
            </div>

            <div className="tab-indicator">
                <div className="indicator-line">
                    <div 
                        className="active-indicator"
                        style={{
                            transform: `translateX(${availableViews.indexOf(activeTab) * 100}%)`,
                            width: `${100 / availableViews.length}%`
                        }}
                    />
                </div>
            </div>
        </div>
    );
};