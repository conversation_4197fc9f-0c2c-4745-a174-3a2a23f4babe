/**
 * Processed Video Player Component
 * Displays videos with pre-processed pose data from Modal/SmoothNet
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { ProcessedVideoPlayerProps, ProcessedPoseData, ProcessingStatus } from '../types';
import { calculateCoordinateTransform, validateEssentialKeypoints } from '../utils/coordinates';

interface ProcessedFrame {
  frameNumber: number;
  timestamp: number;
  keypoints: Array<{
    x: number;
    y: number;
    z: number;
    score: number;
    name: string;
  }>;
}

interface ProcessedResults {
  video: string;
  videoWidth: number;
  videoHeight: number;
  fps: number;
  modelType: string;
  analysisId: string;
  viewType: string;
  processingTime: number;
  frames: ProcessedFrame[];
}

const ProcessedVideoPlayer: React.FC<ProcessedVideoPlayerProps> = ({
  videoUrl,
  resultsUrl,
  analysisType,
  viewType,
  overlayStyle,
  userHeight,
  onPoseData,
  onMetrics
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [processedData, setProcessedData] = useState<ProcessedResults | null>(null);
  const [currentFrameData, setCurrentFrameData] = useState<ProcessedFrame | null>(null);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    isProcessing: false,
    progress: 0,
    currentFrame: 0,
    totalFrames: 0,
    elapsed: 0,
    remaining: 0,
    status: 'idle'
  });

  // Load processed results from S3
  const loadProcessedResults = useCallback(async () => {
    if (!resultsUrl) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('🔄 Loading processed results from:', resultsUrl);
      
      const response = await fetch(resultsUrl);
      if (!response.ok) {
        throw new Error(`Failed to load results: ${response.status} ${response.statusText}`);
      }

      const data: ProcessedResults = await response.json();
      
      // Validate the data structure
      if (!data.frames || !Array.isArray(data.frames)) {
        throw new Error('Invalid results format: missing frames array');
      }

      console.log('✅ Loaded processed results:', {
        video: data.video,
        modelType: data.modelType,
        viewType: data.viewType,
        frameCount: data.frames.length,
        processingTime: data.processingTime
      });

      setProcessedData(data);
      setIsLoading(false);

      // Update processing status with loaded data
      setProcessingStatus(prev => ({
        ...prev,
        totalFrames: data.frames.length,
        status: 'ready'
      }));

    } catch (err) {
      console.error('❌ Failed to load processed results:', err);
      setError(err instanceof Error ? err.message : 'Failed to load processed results');
      setIsLoading(false);
    }
  }, [resultsUrl]);

  // Find the closest frame data for current video time
  const findFrameForTime = useCallback((currentTime: number) => {
    if (!processedData) return null;

    // Convert video time to frame number (assuming consistent FPS)
    const targetFrameNumber = Math.round(currentTime * processedData.fps);
    
    // Find exact match or closest frame
    let closestFrame = processedData.frames[0];
    let minDiff = Math.abs(closestFrame.frameNumber - targetFrameNumber);

    for (const frame of processedData.frames) {
      const diff = Math.abs(frame.frameNumber - targetFrameNumber);
      if (diff < minDiff) {
        minDiff = diff;
        closestFrame = frame;
      }
    }

    return closestFrame;
  }, [processedData]);

  // Handle video playback state
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    startPlayback();
  }, [processedData]);

  const handlePause = useCallback(() => {
    setIsPlaying(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  // Real-time playback synchronization loop
  const startPlayback = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !processedData) return;

    const syncFrame = () => {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      if (!video || !canvas || video.paused || video.ended || !processedData) return;

      try {
        // Find frame data for current video time
        const frameData = findFrameForTime(video.currentTime);
        setCurrentFrameData(frameData);

        // Draw video frame and pose overlay
        drawFrame(video, canvas, frameData);

        // Update processing status
        const progress = (video.currentTime / video.duration) * 100;
        const currentFrameNumber = frameData ? frameData.frameNumber : Math.round(video.currentTime * processedData.fps);
        
        setProcessingStatus(prev => ({
          ...prev,
          progress,
          currentFrame: currentFrameNumber,
          elapsed: video.currentTime,
          remaining: video.duration - video.currentTime,
          status: 'playing'
        }));

        // Trigger pose data callback
        if (frameData && onPoseData) {
          const poseData: ProcessedPoseData = {
            keypoints: frameData.keypoints,
            score: frameData.keypoints.reduce((sum, kp) => sum + kp.score, 0) / frameData.keypoints.length,
            timestamp: frameData.timestamp,
            frameNumber: frameData.frameNumber,
            modelType: processedData.modelType,
            smoothed: true
          };
          onPoseData(poseData);
        }

      } catch (err) {
        console.error('Error in playback sync:', err);
      }

      // Continue sync loop
      if (isPlaying) {
        animationFrameRef.current = requestAnimationFrame(syncFrame);
      }
    };

    syncFrame();
  }, [processedData, isPlaying, findFrameForTime, onPoseData]);

  // Draw video frame and pose overlay
  const drawFrame = useCallback((
    video: HTMLVideoElement,
    canvas: HTMLCanvasElement,
    frameData: ProcessedFrame | null
  ) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match video display
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw pose overlay if data available
    if (frameData && frameData.keypoints.length > 0) {
      drawPoseOverlay(ctx, frameData, canvas.width, canvas.height);
    }
  }, []);

  // Draw pose keypoints and connections
  const drawPoseOverlay = useCallback((
    ctx: CanvasRenderingContext2D,
    frameData: ProcessedFrame,
    canvasWidth: number,
    canvasHeight: number
  ) => {
    const { keypoints } = frameData;
    
    if (!processedData) return;

    // Calculate scaling from original video size to current canvas size
    const scaleX = canvasWidth / processedData.videoWidth;
    const scaleY = canvasHeight / processedData.videoHeight;

    // Validate essential keypoints
    const validation = validateEssentialKeypoints(keypoints);

    // Draw keypoints
    keypoints.forEach((keypoint, index) => {
      if (!keypoint.score || keypoint.score < 0.3) return; // Lower threshold for processed data

      // Scale coordinates to current canvas size
      const x = keypoint.x * scaleX;
      const y = keypoint.y * scaleY;
      
      // Color coding by confidence
      const alpha = Math.min(keypoint.score, 1.0);
      let color = `rgba(0, 255, 0, ${alpha})`; // Green for good confidence
      
      if (keypoint.score < 0.7) {
        color = `rgba(255, 165, 0, ${alpha})`; // Orange for medium confidence
      }
      if (keypoint.score < 0.5) {
        color = `rgba(255, 0, 0, ${alpha})`; // Red for low confidence
      }

      // Draw keypoint circle
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(x, y, 6, 0, 2 * Math.PI);
      ctx.fill();

      // Draw keypoint name for medical overlay style
      if (overlayStyle === 'Medical' && keypoint.name) {
        ctx.fillStyle = 'white';
        ctx.font = '12px Arial';
        ctx.strokeStyle = 'black';
        ctx.lineWidth = 2;
        ctx.strokeText(keypoint.name, x + 8, y - 8);
        ctx.fillText(keypoint.name, x + 8, y - 8);
      }
    });

    // Draw skeleton connections for side view
    if (viewType === 'side') {
      drawSideViewSkeleton(ctx, keypoints, scaleX, scaleY);
    }

    // Draw SmoothNet indicator
    drawSmoothNetIndicator(ctx, canvasWidth, canvasHeight);

    // Draw validation status
    drawValidationStatus(ctx, validation, canvasWidth);
  }, [processedData, overlayStyle, viewType]);

  // Draw skeleton connections for side view analysis
  const drawSideViewSkeleton = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[],
    scaleX: number,
    scaleY: number
  ) => {
    // Define connections for side view running analysis
    const connections = [
      // Head and torso
      ['nose', 'left_shoulder'],
      ['nose', 'right_shoulder'],
      ['left_shoulder', 'right_shoulder'],
      ['left_shoulder', 'left_hip'],
      ['right_shoulder', 'right_hip'],
      ['left_hip', 'right_hip'],
      
      // Left leg
      ['left_hip', 'left_knee'],
      ['left_knee', 'left_ankle'],
      
      // Right leg
      ['right_hip', 'right_knee'],
      ['right_knee', 'right_ankle'],
      
      // Arms
      ['left_shoulder', 'left_elbow'],
      ['left_elbow', 'left_wrist'],
      ['right_shoulder', 'right_elbow'],
      ['right_elbow', 'right_wrist']
    ];

    ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
    ctx.lineWidth = 3;

    connections.forEach(([point1Name, point2Name]) => {
      const point1 = keypoints.find(kp => kp.name === point1Name);
      const point2 = keypoints.find(kp => kp.name === point2Name);

      if (point1 && point2 && 
          point1.score && point1.score > 0.3 &&
          point2.score && point2.score > 0.3) {
        ctx.beginPath();
        ctx.moveTo(point1.x * scaleX, point1.y * scaleY);
        ctx.lineTo(point2.x * scaleX, point2.y * scaleY);
        ctx.stroke();
      }
    });
  }, []);

  // Draw SmoothNet processing indicator
  const drawSmoothNetIndicator = useCallback((
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => {
    const text = `🔄 SmoothNet Processed (${processedData?.modelType || 'Unknown'})`;
    
    ctx.fillStyle = 'rgba(0, 0, 255, 0.8)';
    ctx.fillRect(width - 300, 10, 290, 30);
    
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.fillText(text, width - 290, 30);
  }, [processedData]);

  // Draw pose validation status
  const drawValidationStatus = useCallback((
    ctx: CanvasRenderingContext2D,
    validation: any,
    width: number
  ) => {
    const statusText = validation.isValid 
      ? `✅ Pose Valid (${(validation.confidence * 100).toFixed(0)}%)`
      : `⚠️ Missing: ${validation.missingKeypoints.join(', ')}`;
    
    ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
    ctx.fillRect(10, 10, 350, 40);
    
    ctx.fillStyle = validation.isValid ? '#00ff00' : '#ffaa00';
    ctx.font = '14px Arial';
    ctx.fillText(statusText, 20, 35);
  }, []);

  // Load processed results when URL changes
  useEffect(() => {
    if (resultsUrl) {
      loadProcessedResults();
    }
  }, [loadProcessedResults]);

  // Start playback when video plays and data is ready
  useEffect(() => {
    if (isPlaying && processedData) {
      startPlayback();
    }
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, processedData, startPlayback]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, []);

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      {/* Loading State */}
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
          <div className="bg-white p-4 rounded-lg text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm">Loading processed pose data...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="absolute inset-0 bg-red-500 bg-opacity-50 flex items-center justify-center z-10">
          <div className="bg-white p-4 rounded-lg text-center max-w-md">
            <p className="text-red-600 font-semibold mb-2">Error Loading Results</p>
            <p className="text-sm">{error}</p>
            <button 
              onClick={loadProcessedResults}
              className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Retry
            </button>
          </div>
        </div>
      )}

      {/* Video and Canvas Container */}
      <div className="relative bg-black rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          src={videoUrl}
          className="w-full h-auto"
          controls
          onPlay={handlePlay}
          onPause={handlePause}
          onEnded={handlePause}
        />
        
        {/* Pose Overlay Canvas */}
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
          style={{ display: processedData && !isLoading ? 'block' : 'none' }}
        />
      </div>

      {/* Processing Info */}
      {processedData && (
        <div className="mt-4 bg-blue-50 p-4 rounded-lg">
          <div className="flex justify-between items-center mb-2">
            <h3 className="font-semibold text-blue-900">Processed Results Info</h3>
            <span className="text-xs bg-blue-200 px-2 py-1 rounded">
              Model: {processedData.modelType}
            </span>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Frames:</span>
              <span className="ml-1 font-medium">{processedData.frames.length}</span>
            </div>
            <div>
              <span className="text-gray-600">FPS:</span>
              <span className="ml-1 font-medium">{processedData.fps}</span>
            </div>
            <div>
              <span className="text-gray-600">Processing Time:</span>
              <span className="ml-1 font-medium">{processedData.processingTime.toFixed(1)}s</span>
            </div>
            <div>
              <span className="text-gray-600">View:</span>
              <span className="ml-1 font-medium capitalize">{processedData.viewType}</span>
            </div>
          </div>
        </div>
      )}

      {/* Current Frame Info */}
      {currentFrameData && (
        <div className="mt-2 text-xs text-gray-500 text-center">
          Frame {currentFrameData.frameNumber} • 
          Timestamp: {currentFrameData.timestamp.toFixed(2)}s • 
          Avg confidence: {(currentFrameData.keypoints.reduce((sum, kp) => sum + kp.score, 0) / currentFrameData.keypoints.length * 100).toFixed(1)}%
        </div>
      )}
    </div>
  );
};

export default ProcessedVideoPlayer;