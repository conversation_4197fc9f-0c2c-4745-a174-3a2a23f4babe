import React, { useState, useEffect } from 'react';
import { ProcessedVideoPlayer } from './ProcessedVideoPlayer';
import { MetricsDisplay } from './MetricsDisplay';
import { TabsContainer } from './TabsContainer';
import { AnalysisData, ViewType, ProcessingStatus } from '../types';

interface ResultsPageProps {
    analysisId: string;
    userEmail: string;
    onBack: () => void;
    onNewAnalysis: () => void;
}

export const ResultsPage: React.FC<ResultsPageProps> = ({
    analysisId,
    userEmail,
    onBack,
    onNewAnalysis
}) => {
    const [activeTab, setActiveTab] = useState<ViewType>('side');
    const [analysisData, setAnalysisData] = useState<AnalysisData | null>(null);
    const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>('loading');
    const [error, setError] = useState<string | null>(null);

    // Load analysis results from Supabase
    useEffect(() => {
        loadAnalysisResults();
        // Poll for updates if still processing
        const pollInterval = setInterval(() => {
            if (processingStatus === 'processing') {
                checkProcessingStatus();
            }
        }, 5000);

        return () => clearInterval(pollInterval);
    }, [analysisId]);

    const loadAnalysisResults = async () => {
        try {
            setProcessingStatus('loading');
            
            // Fetch analysis data from Supabase
            const response = await fetch(`/api/analysis/${analysisId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Email': userEmail
                }
            });

            if (!response.ok) {
                throw new Error(`Failed to load analysis: ${response.statusText}`);
            }

            const data = await response.json();
            setAnalysisData(data);
            
            // Determine status based on available data
            if (data.sideAnalysisJson && data.rearAnalysisJson) {
                setProcessingStatus('completed');
            } else if (data.sideAnalysisJson || data.rearAnalysisJson) {
                setProcessingStatus('partial');
            } else {
                setProcessingStatus('processing');
            }

        } catch (err) {
            console.error('Error loading analysis results:', err);
            setError(err instanceof Error ? err.message : 'Failed to load results');
            setProcessingStatus('error');
        }
    };

    const checkProcessingStatus = async () => {
        try {
            const response = await fetch(`/api/processing-status/${analysisId}`);
            if (response.ok) {
                const status = await response.json();
                if (status.status === 'completed') {
                    loadAnalysisResults(); // Reload full data
                }
            }
        } catch (err) {
            console.error('Error checking processing status:', err);
        }
    };

    const getAvailableViews = (): ViewType[] => {
        const views: ViewType[] = [];
        if (analysisData?.sideAnalysisJson) views.push('side');
        if (analysisData?.rearAnalysisJson) views.push('rear');
        return views;
    };

    const getCurrentAnalysisJson = () => {
        if (!analysisData) return null;
        return activeTab === 'side' 
            ? analysisData.sideAnalysisJson 
            : analysisData.rearAnalysisJson;
    };

    const getCurrentVideoUrl = () => {
        if (!analysisData) return null;
        return activeTab === 'side'
            ? analysisData.sideVideoUrl
            : analysisData.rearVideoUrl;
    };

    // Loading state
    if (processingStatus === 'loading') {
        return (
            <div className="results-page loading">
                <div className="loading-spinner">
                    <div className="spinner"></div>
                    <p>Loading analysis results...</p>
                </div>
            </div>
        );
    }

    // Error state
    if (processingStatus === 'error' || error) {
        return (
            <div className="results-page error">
                <div className="error-container">
                    <h2>Error Loading Results</h2>
                    <p>{error || 'An unexpected error occurred'}</p>
                    <div className="error-actions">
                        <button onClick={loadAnalysisResults} className="btn-retry">
                            Try Again
                        </button>
                        <button onClick={onBack} className="btn-secondary">
                            Go Back
                        </button>
                    </div>
                </div>
            </div>
        );
    }

    // Processing state
    if (processingStatus === 'processing') {
        return (
            <div className="results-page processing">
                <div className="processing-container">
                    <div className="processing-spinner">
                        <div className="spinner"></div>
                    </div>
                    <h2>Processing Your Videos</h2>
                    <p>Your running analysis is being processed with our advanced AI models.</p>
                    <p>This typically takes 1-2 minutes per video.</p>
                    
                    <div className="processing-status">
                        <div className="status-item">
                            <span className="status-label">Side View:</span>
                            <span className={`status-badge ${analysisData?.sideAnalysisJson ? 'completed' : 'processing'}`}>
                                {analysisData?.sideAnalysisJson ? '✓ Complete' : '⏳ Processing'}
                            </span>
                        </div>
                        <div className="status-item">
                            <span className="status-label">Rear View:</span>
                            <span className={`status-badge ${analysisData?.rearAnalysisJson ? 'completed' : 'processing'}`}>
                                {analysisData?.rearAnalysisJson ? '✓ Complete' : '⏳ Processing'}
                            </span>
                        </div>
                    </div>

                    <button onClick={onBack} className="btn-secondary">
                        Go Back
                    </button>
                </div>
            </div>
        );
    }

    // Results ready
    const availableViews = getAvailableViews();
    const currentAnalysisJson = getCurrentAnalysisJson();
    const currentVideoUrl = getCurrentVideoUrl();

    return (
        <div className="results-page">
            <div className="results-header">
                <h1>Running Analysis Results</h1>
                <div className="header-actions">
                    <button onClick={onBack} className="btn-secondary">
                        ← Back
                    </button>
                    <button onClick={onNewAnalysis} className="btn-primary">
                        New Analysis
                    </button>
                </div>
            </div>

            {availableViews.length > 0 && (
                <>
                    <TabsContainer
                        activeTab={activeTab}
                        availableViews={availableViews}
                        onTabChange={setActiveTab}
                    />

                    <div className="results-content">
                        <div className="video-section">
                            {currentAnalysisJson && currentVideoUrl && (
                                <ProcessedVideoPlayer
                                    videoUrl={currentVideoUrl}
                                    poseData={currentAnalysisJson}
                                    viewType={activeTab}
                                    height={analysisData?.heightInches}
                                />
                            )}
                        </div>

                        <div className="metrics-section">
                            {currentAnalysisJson && (
                                <MetricsDisplay
                                    analysisData={currentAnalysisJson}
                                    viewType={activeTab}
                                    userHeight={analysisData?.heightInches}
                                />
                            )}
                        </div>
                    </div>
                </>
            )}

            {availableViews.length === 0 && (
                <div className="no-results">
                    <p>No analysis results available yet.</p>
                    <button onClick={loadAnalysisResults} className="btn-primary">
                        Refresh
                    </button>
                </div>
            )}
        </div>
    );
};