/**
 * Upload Panel Component
 * Handles video file upload with drag-and-drop support
 */

import React, { useRef, useState, useCallback } from 'react';
import { UploadPanelProps, VideoFile } from '../types';

const UploadPanel: React.FC<UploadPanelProps> = ({
  title,
  description,
  acceptedFormats,
  video,
  onVideoUpload
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Supported video formats
  const supportedFormats = ['mp4', 'mov', 'avi', 'webm'];
  const maxFileSize = 100 * 1024 * 1024; // 100MB

  // Handle file selection
  const handleFileSelect = useCallback((file: File) => {
    setUploadError(null);

    // Validate file type
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !supportedFormats.includes(fileExtension)) {
      setUploadError(`Unsupported file format. Please use: ${supportedFormats.join(', ').toUpperCase()}`);
      return;
    }

    // Validate file size
    if (file.size > maxFileSize) {
      setUploadError(`File too large. Maximum size is ${maxFileSize / (1024 * 1024)}MB.`);
      return;
    }

    // Validate MIME type
    if (!file.type.startsWith('video/')) {
      setUploadError('Please select a valid video file.');
      return;
    }

    // Create video file object
    const videoFile: VideoFile = {
      file,
      url: URL.createObjectURL(file),
      name: file.name
    };

    console.log(`📹 Video uploaded: ${file.name} (${(file.size / (1024 * 1024)).toFixed(2)}MB)`);
    onVideoUpload(videoFile);
  }, [onVideoUpload]);

  // Handle input change
  const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  // Handle drag events
  const handleDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    const file = event.dataTransfer.files[0];
    if (file) {
      handleFileSelect(file);
    }
  }, [handleFileSelect]);

  // Handle upload area click
  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  // Handle video removal
  const handleRemoveVideo = useCallback(() => {
    if (video) {
      URL.revokeObjectURL(video.url);
      onVideoUpload(null);
      setUploadError(null);
    }
  }, [video, onVideoUpload]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold mb-2 text-gray-900">{title}</h3>
      <p className="text-sm text-gray-600 mb-4">{description}</p>

      {/* Upload Area */}
      {!video ? (
        <div
          className={`
            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200
            ${isDragging 
              ? 'border-blue-400 bg-blue-50' 
              : 'border-gray-300 hover:border-blue-400 hover:bg-gray-50'
            }
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={handleUploadClick}
        >
          <div className="space-y-4">
            {/* Upload Icon */}
            <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
            </div>

            {/* Upload Text */}
            <div>
              <p className="text-lg font-medium text-gray-900 mb-1">
                {isDragging ? 'Drop video here' : 'Click to upload or drag video here'}
              </p>
              <p className="text-sm text-gray-500">{acceptedFormats}</p>
            </div>

            {/* File Input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="video/*"
              onChange={handleInputChange}
              className="hidden"
            />
          </div>
        </div>
      ) : (
        /* Video Preview */
        <div className="space-y-4">
          <div className="bg-gray-50 rounded-lg p-4 border">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{video.name}</p>
                  <p className="text-sm text-gray-500">
                    {(video.file.size / (1024 * 1024)).toFixed(2)}MB • Ready for analysis
                  </p>
                </div>
              </div>
              <button
                onClick={handleRemoveVideo}
                className="text-gray-400 hover:text-red-500 transition-colors p-1"
                title="Remove video"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          {/* Video Preview */}
          <div className="bg-black rounded-lg overflow-hidden">
            <video
              src={video.url}
              className="w-full h-auto max-h-48 object-contain"
              controls
              preload="metadata"
            />
          </div>
        </div>
      )}

      {/* Error Message */}
      {uploadError && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center space-x-2">
            <svg className="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <p className="text-sm text-red-700">{uploadError}</p>
          </div>
        </div>
      )}

      {/* Upload Guidelines */}
      <div className="mt-4 text-xs text-gray-500 space-y-1">
        <p>• Record from 5 feet away from the runner</p>
        <p>• Ensure the entire body is visible in the frame</p>
        <p>• Record for at least 30 seconds to capture multiple stride cycles</p>
        <p>• Use landscape orientation for better coverage</p>
      </div>
    </div>
  );
};

export default UploadPanel;