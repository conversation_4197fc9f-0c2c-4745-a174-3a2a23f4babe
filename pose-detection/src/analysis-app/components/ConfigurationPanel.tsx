/**
 * Configuration Panel Component
 * Handles user height and configuration input
 */

import React from 'react';
import { ConfigurationPanelProps, UserConfiguration } from '../types';

const ConfigurationPanel: React.FC<ConfigurationPanelProps> = ({
  userConfig,
  onConfigChange
}) => {
  
  // Handle height change
  const handleHeightChange = (field: 'feet' | 'inches', value: number) => {
    const newConfig: UserConfiguration = {
      ...userConfig,
      height: {
        ...userConfig.height,
        [field]: value
      }
    };
    onConfigChange(newConfig);
  };

  // Handle other field changes
  const handleFieldChange = (field: keyof UserConfiguration, value: any) => {
    const newConfig: UserConfiguration = {
      ...userConfig,
      [field]: value
    };
    onConfigChange(newConfig);
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold mb-6 text-center text-gray-900">User Information</h3>
      
      <div className="grid md:grid-cols-3 gap-8">
        {/* Height Input */}
        <div className="text-center">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Height <span className="text-red-500">*</span>
          </label>
          <div className="flex gap-2 justify-center">
            <select 
              value={userConfig.height.feet}
              onChange={(e) => handleHeightChange('feet', parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {[4, 5, 6, 7].map(ft => (
                <option key={ft} value={ft}>{ft} ft</option>
              ))}
            </select>
            <select 
              value={userConfig.height.inches}
              onChange={(e) => handleHeightChange('inches', parseInt(e.target.value))}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              {Array.from({length: 12}, (_, i) => (
                <option key={i} value={i}>{i} in</option>
              ))}
            </select>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Used for coordinate scaling
          </p>
        </div>

        {/* Gender Input */}
        <div className="text-center">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Gender
          </label>
          <select
            value={userConfig.gender || ''}
            onChange={(e) => handleFieldChange('gender', e.target.value || undefined)}
            className="w-full max-w-xs mx-auto px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Select gender</option>
            <option value="male">Male</option>
            <option value="female">Female</option>
            <option value="other">Other</option>
            <option value="prefer-not-to-say">Prefer not to say</option>
          </select>
          <p className="text-xs text-gray-500 mt-2">
            Optional for analysis
          </p>
        </div>

        {/* Weight Input */}
        <div className="text-center">
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Weight
          </label>
          <div className="flex gap-2 justify-center max-w-xs mx-auto">
            <input
              type="number"
              placeholder="Enter weight"
              value={userConfig.weight || ''}
              onChange={(e) => handleFieldChange('weight', e.target.value ? parseInt(e.target.value) : undefined)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <select
              value={userConfig.weightUnit}
              onChange={(e) => handleFieldChange('weightUnit', e.target.value as 'lbs' | 'kg')}
              className="w-20 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="lbs">lbs</option>
              <option value="kg">kg</option>
            </select>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Optional for biomechanics
          </p>
        </div>
      </div>

      {/* Height Display */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-center justify-center space-x-2">
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div className="text-center">
            <p className="text-sm font-medium text-blue-900">
              User Height: {userConfig.height.feet}'{userConfig.height.inches}"
              ({((userConfig.height.feet * 12 + userConfig.height.inches) * 0.0254).toFixed(2)}m)
            </p>
            <p className="text-xs text-blue-700">
              This height will be used to scale pose coordinates to real-world measurements
            </p>
          </div>
        </div>
      </div>

      {/* Analysis Configuration Info */}
      <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
        <div className="p-3 bg-gray-50 rounded-lg">
          <p className="text-xs font-medium text-gray-600">Analysis Mode</p>
          <p className="text-sm font-semibold text-gray-900">3D</p>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <p className="text-xs font-medium text-gray-600">Model</p>
          <p className="text-sm font-semibold text-gray-900">BlazePose Full</p>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <p className="text-xs font-medium text-gray-600">Keypoints</p>
          <p className="text-sm font-semibold text-gray-900">39 Points</p>
        </div>
        <div className="p-3 bg-gray-50 rounded-lg">
          <p className="text-xs font-medium text-gray-600">Activity</p>
          <p className="text-sm font-semibold text-gray-900">Running</p>
        </div>
      </div>
    </div>
  );
};

export default ConfigurationPanel;