import React, { useMemo } from 'react';
import { ViewType, PoseAnalysisData } from '../types';
import { RunningMetrics } from '../utils/runningMetrics';

interface MetricsDisplayProps {
    analysisData: PoseAnalysisData;
    viewType: ViewType;
    userHeight?: number;
}

interface MetricCard {
    label: string;
    value: string;
    unit: string;
    description: string;
    status: 'good' | 'warning' | 'needs-attention' | 'neutral';
    icon: string;
}

export const MetricsDisplay: React.FC<MetricsDisplayProps> = ({
    analysisData,
    viewType,
    userHeight = 70 // Default 5'10"
}) => {
    // Calculate metrics using existing utility
    const metrics = useMemo(() => {
        if (!analysisData?.frames) return null;
        
        const calculator = new RunningMetrics();
        return calculator.calculateFromFrames(analysisData.frames, userHeight);
    }, [analysisData, userHeight]);

    // Get view-specific metrics
    const getMetricsForView = (viewType: ViewType): MetricCard[] => {
        if (!metrics) return [];

        if (viewType === 'side') {
            return [
                {
                    label: 'Stride Length',
                    value: metrics.strideLength?.toFixed(2) || 'N/A',
                    unit: 'inches',
                    description: 'Distance covered in one complete stride cycle',
                    status: getStrideStatus(metrics.strideLength),
                    icon: '📏'
                },
                {
                    label: 'Cadence',
                    value: metrics.cadence?.toFixed(0) || 'N/A',
                    unit: 'steps/min',
                    description: 'Number of steps per minute',
                    status: getCadenceStatus(metrics.cadence),
                    icon: '⏱️'
                },
                {
                    label: 'Vertical Oscillation',
                    value: metrics.verticalOscillation?.toFixed(2) || 'N/A',
                    unit: 'inches',
                    description: 'Up and down movement of your center of mass',
                    status: getVerticalOscillationStatus(metrics.verticalOscillation),
                    icon: '↕️'
                },
                {
                    label: 'Forward Lean',
                    value: metrics.forwardLean?.toFixed(1) || 'N/A',
                    unit: 'degrees',
                    description: 'Angle of forward lean from vertical',
                    status: getForwardLeanStatus(metrics.forwardLean),
                    icon: '📐'
                },
                {
                    label: 'Ground Contact Time',
                    value: metrics.groundContactTime?.toFixed(0) || 'N/A',
                    unit: 'ms',
                    description: 'Time your foot stays in contact with ground',
                    status: getGroundContactStatus(metrics.groundContactTime),
                    icon: '👟'
                },
                {
                    label: 'Flight Time',
                    value: metrics.flightTime?.toFixed(0) || 'N/A',
                    unit: 'ms',
                    description: 'Time both feet are off the ground',
                    status: 'neutral',
                    icon: '🦅'
                }
            ];
        } else {
            // Rear view metrics
            return [
                {
                    label: 'Hip Drop',
                    value: metrics.hipDrop?.toFixed(1) || 'N/A',
                    unit: 'degrees',
                    description: 'Lateral tilt of pelvis during stance phase',
                    status: getHipDropStatus(metrics.hipDrop),
                    icon: '🏋️'
                },
                {
                    label: 'Knee Alignment',
                    value: metrics.kneeAlignment?.toFixed(1) || 'N/A',
                    unit: 'degrees',
                    description: 'Inward/outward deviation of knee tracking',
                    status: getKneeAlignmentStatus(metrics.kneeAlignment),
                    icon: '🦵'
                },
                {
                    label: 'Lateral Movement',
                    value: metrics.lateralMovement?.toFixed(2) || 'N/A',
                    unit: 'inches',
                    description: 'Side-to-side movement of center of mass',
                    status: getLateralMovementStatus(metrics.lateralMovement),
                    icon: '↔️'
                },
                {
                    label: 'Pelvic Rotation',
                    value: metrics.pelvicRotation?.toFixed(1) || 'N/A',
                    unit: 'degrees',
                    description: 'Rotational movement of pelvis',
                    status: 'neutral',
                    icon: '🔄'
                },
                {
                    label: 'Foot Strike Symmetry',
                    value: metrics.strikeSymmetry?.toFixed(1) || 'N/A',
                    unit: '%',
                    description: 'Balance between left and right foot strikes',
                    status: getSymmetryStatus(metrics.strikeSymmetry),
                    icon: '⚖️'
                },
                {
                    label: 'Hip Stability',
                    value: metrics.hipStability?.toFixed(1) || 'N/A',
                    unit: 'score',
                    description: 'Overall stability of hip movement',
                    status: getStabilityStatus(metrics.hipStability),
                    icon: '🎯'
                }
            ];
        }
    };

    // Status determination functions
    const getStrideStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value >= 40 && value <= 50) return 'good';
        if (value >= 35 && value <= 55) return 'warning';
        return 'needs-attention';
    };

    const getCadenceStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value >= 170 && value <= 190) return 'good';
        if (value >= 160 && value <= 200) return 'warning';
        return 'needs-attention';
    };

    const getVerticalOscillationStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value <= 3.5) return 'good';
        if (value <= 4.5) return 'warning';
        return 'needs-attention';
    };

    const getForwardLeanStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (Math.abs(value) >= 3 && Math.abs(value) <= 8) return 'good';
        if (Math.abs(value) <= 12) return 'warning';
        return 'needs-attention';
    };

    const getGroundContactStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value >= 200 && value <= 250) return 'good';
        if (value >= 180 && value <= 280) return 'warning';
        return 'needs-attention';
    };

    const getHipDropStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (Math.abs(value) <= 5) return 'good';
        if (Math.abs(value) <= 8) return 'warning';
        return 'needs-attention';
    };

    const getKneeAlignmentStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (Math.abs(value) <= 3) return 'good';
        if (Math.abs(value) <= 6) return 'warning';
        return 'needs-attention';
    };

    const getLateralMovementStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value <= 2) return 'good';
        if (value <= 3) return 'warning';
        return 'needs-attention';
    };

    const getSymmetryStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value >= 90) return 'good';
        if (value >= 80) return 'warning';
        return 'needs-attention';
    };

    const getStabilityStatus = (value?: number): MetricCard['status'] => {
        if (!value) return 'neutral';
        if (value >= 8) return 'good';
        if (value >= 6) return 'warning';
        return 'needs-attention';
    };

    const viewMetrics = getMetricsForView(viewType);

    const getStatusIcon = (status: MetricCard['status']): string => {
        switch (status) {
            case 'good': return '✅';
            case 'warning': return '⚠️';
            case 'needs-attention': return '🔴';
            default: return 'ℹ️';
        }
    };

    const getStatusClass = (status: MetricCard['status']): string => {
        return `metric-card ${status}`;
    };

    return (
        <div className="metrics-display">
            <div className="metrics-header">
                <h3>
                    {viewType === 'side' ? 'Side View Metrics' : 'Rear View Metrics'}
                </h3>
                <p className="metrics-subtitle">
                    {viewType === 'side' 
                        ? 'Forward motion and efficiency analysis'
                        : 'Stability and alignment analysis'
                    }
                </p>
            </div>

            <div className="metrics-grid">
                {viewMetrics.map((metric, index) => (
                    <div key={index} className={getStatusClass(metric.status)}>
                        <div className="metric-header">
                            <span className="metric-icon">{metric.icon}</span>
                            <span className="metric-status">{getStatusIcon(metric.status)}</span>
                        </div>
                        <div className="metric-value">
                            <span className="value">{metric.value}</span>
                            <span className="unit">{metric.unit}</span>
                        </div>
                        <div className="metric-label">{metric.label}</div>
                        <div className="metric-description">{metric.description}</div>
                    </div>
                ))}
            </div>

            <div className="metrics-summary">
                <div className="summary-header">
                    <h4>Analysis Summary</h4>
                </div>
                <div className="summary-content">
                    {getAnalysisSummary(viewType, viewMetrics)}
                </div>
            </div>
        </div>
    );

    function getAnalysisSummary(viewType: ViewType, metrics: MetricCard[]): string {
        const goodMetrics = metrics.filter(m => m.status === 'good').length;
        const totalMetrics = metrics.length;
        const percentage = Math.round((goodMetrics / totalMetrics) * 100);

        if (viewType === 'side') {
            if (percentage >= 80) {
                return `Excellent running form! ${goodMetrics}/${totalMetrics} metrics in optimal range. Your stride efficiency and forward motion mechanics are well-balanced.`;
            } else if (percentage >= 60) {
                return `Good overall form with room for improvement. Focus on the metrics highlighted in yellow and red for enhanced efficiency.`;
            } else {
                return `Several areas need attention. Consider working with a running coach to address the highlighted metrics for injury prevention and performance improvement.`;
            }
        } else {
            if (percentage >= 80) {
                return `Outstanding stability and alignment! Your hip and knee mechanics show excellent control and symmetry.`;
            } else if (percentage >= 60) {
                return `Good stability with some areas to monitor. Pay attention to highlighted metrics to prevent potential overuse injuries.`;
            } else {
                return `Stability concerns detected. Consider strength training and form work to address alignment issues and reduce injury risk.`;
            }
        }
    }
};