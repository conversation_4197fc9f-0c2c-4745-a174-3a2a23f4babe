/**
 * Video Player Component with BlazePose Integration
 * Provides real-time pose analysis with canvas overlay
 */

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { useBlazePose } from '../hooks/useBlazePose';
import { VideoPlayerProps, PoseData, ProcessingStatus } from '../types';
import { calculateCoordinateTransform, validateEssentialKeypoints } from '../utils/coordinates';

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  analysisType,
  viewType,
  analysisMode,
  videoSetup,
  overlayStyle,
  userHeight,
  onPoseData,
  onMetrics
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    isProcessing: false,
    progress: 0,
    currentFrame: 0,
    totalFrames: 0,
    elapsed: 0,
    remaining: 0,
    status: 'idle'
  });
  const [poseHistory, setPoseHistory] = useState<PoseData[]>([]);

  // Initialize BlazePose hook
  const {
    detector,
    isLoading,
    isReady,
    error,
    processFrame,
    dispose,
    reset
  } = useBlazePose({
    userHeight,
    onPoseData: (data) => {
      setPoseHistory(prev => [...prev.slice(-29), data]); // Keep last 30 frames
      onPoseData?.(data);
    },
    onStatusChange: setProcessingStatus
  });

  // Handle video playback state
  const handlePlay = useCallback(() => {
    setIsPlaying(true);
    if (isReady) {
      startAnalysis();
    }
  }, [isReady]);

  const handlePause = useCallback(() => {
    setIsPlaying(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  }, []);

  // Real-time analysis loop
  const startAnalysis = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !isReady) return;

    const processVideoFrame = async () => {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      if (!video || !canvas || video.paused || video.ended) return;

      try {
        // Process current frame with BlazePose
        const poseData = await processFrame(video);
        
        // Draw video frame and pose overlay
        drawFrame(video, canvas, poseData);
        
        // Update processing status
        const progress = (video.currentTime / video.duration) * 100;
        setProcessingStatus(prev => ({
          ...prev,
          progress,
          currentFrame: Math.floor(video.currentTime * 30), // Assume 30 FPS
          totalFrames: Math.floor(video.duration * 30),
          elapsed: video.currentTime,
          remaining: video.duration - video.currentTime,
          status: 'processing'
        }));

      } catch (err) {
        console.error('Error in analysis loop:', err);
      }

      // Continue analysis loop
      if (isPlaying) {
        animationFrameRef.current = requestAnimationFrame(processVideoFrame);
      }
    };

    processVideoFrame();
  }, [isReady, isPlaying, processFrame]);

  // Draw video frame and pose overlay
  const drawFrame = useCallback((
    video: HTMLVideoElement,
    canvas: HTMLCanvasElement,
    poseData: PoseData | null
  ) => {
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw video frame
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Draw pose overlay if data available
    if (poseData && poseData.keypoints.length > 0) {
      drawPoseOverlay(ctx, poseData, canvas.width, canvas.height);
    }
  }, []);

  // Draw pose keypoints and connections
  const drawPoseOverlay = useCallback((
    ctx: CanvasRenderingContext2D,
    poseData: PoseData,
    width: number,
    height: number
  ) => {
    const { keypoints } = poseData;
    
    // Validate essential keypoints
    const validation = validateEssentialKeypoints(keypoints);
    
    // Calculate coordinate transform for scaling
    const transform = calculateCoordinateTransform(
      userHeight,
      width,
      height,
      keypoints
    );

    // Draw keypoints
    keypoints.forEach((keypoint, index) => {
      if (!keypoint.score || keypoint.score < 0.5) return;

      const { x, y } = keypoint;
      
      // Color coding by confidence
      const alpha = keypoint.score;
      let color = `rgba(0, 255, 0, ${alpha})`; // Green for good confidence
      
      if (keypoint.score < 0.7) {
        color = `rgba(255, 165, 0, ${alpha})`; // Orange for medium confidence
      }
      if (keypoint.score < 0.5) {
        color = `rgba(255, 0, 0, ${alpha})`; // Red for low confidence
      }

      // Draw keypoint circle
      ctx.fillStyle = color;
      ctx.beginPath();
      ctx.arc(x, y, 4, 0, 2 * Math.PI);
      ctx.fill();

      // Draw keypoint name for medical overlay style
      if (overlayStyle === 'Medical' && keypoint.name) {
        ctx.fillStyle = 'white';
        ctx.font = '10px Arial';
        ctx.fillText(keypoint.name, x + 6, y - 6);
      }
    });

    // Draw skeleton connections for side view
    if (viewType === 'side') {
      drawSideViewSkeleton(ctx, keypoints);
    }

    // Draw validation status
    drawValidationStatus(ctx, validation, width);
  }, [userHeight, overlayStyle, viewType]);

  // Draw skeleton connections for side view analysis
  const drawSideViewSkeleton = useCallback((
    ctx: CanvasRenderingContext2D,
    keypoints: any[]
  ) => {
    // Define connections for side view running analysis
    const connections = [
      // Head and torso
      ['nose', 'left_shoulder'],
      ['nose', 'right_shoulder'],
      ['left_shoulder', 'right_shoulder'],
      ['left_shoulder', 'left_hip'],
      ['right_shoulder', 'right_hip'],
      ['left_hip', 'right_hip'],
      
      // Left leg
      ['left_hip', 'left_knee'],
      ['left_knee', 'left_ankle'],
      
      // Right leg
      ['right_hip', 'right_knee'],
      ['right_knee', 'right_ankle'],
      
      // Arms
      ['left_shoulder', 'left_elbow'],
      ['left_elbow', 'left_wrist'],
      ['right_shoulder', 'right_elbow'],
      ['right_elbow', 'right_wrist']
    ];

    ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
    ctx.lineWidth = 2;

    connections.forEach(([point1Name, point2Name]) => {
      const point1 = keypoints.find(kp => kp.name === point1Name);
      const point2 = keypoints.find(kp => kp.name === point2Name);

      if (point1 && point2 && 
          point1.score && point1.score > 0.5 &&
          point2.score && point2.score > 0.5) {
        ctx.beginPath();
        ctx.moveTo(point1.x, point1.y);
        ctx.lineTo(point2.x, point2.y);
        ctx.stroke();
      }
    });
  }, []);

  // Draw pose validation status
  const drawValidationStatus = useCallback((
    ctx: CanvasRenderingContext2D,
    validation: any,
    width: number
  ) => {
    const statusText = validation.isValid 
      ? `✅ Pose Valid (${(validation.confidence * 100).toFixed(0)}%)`
      : `⚠️ Missing: ${validation.missingKeypoints.join(', ')}`;
    
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
    ctx.fillRect(10, 10, 300, 40);
    
    ctx.fillStyle = validation.isValid ? '#00ff00' : '#ffaa00';
    ctx.font = '14px Arial';
    ctx.fillText(statusText, 20, 35);
  }, []);

  // Start analysis when video plays and detector is ready
  useEffect(() => {
    if (isPlaying && isReady) {
      startAnalysis();
    }
    
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isPlaying, isReady, startAnalysis]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      dispose();
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [dispose]);

  return (
    <div className="relative w-full max-w-4xl mx-auto">
      {/* Loading and Error States */}
      {isLoading && (
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center z-10">
          <div className="bg-white p-4 rounded-lg text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-sm">Loading BlazePose Full model...</p>
          </div>
        </div>
      )}

      {error && (
        <div className="absolute inset-0 bg-red-500 bg-opacity-50 flex items-center justify-center z-10">
          <div className="bg-white p-4 rounded-lg text-center max-w-md">
            <p className="text-red-600 font-semibold mb-2">Error</p>
            <p className="text-sm">{error}</p>
          </div>
        </div>
      )}

      {/* Video and Canvas Container */}
      <div className="relative bg-black rounded-lg overflow-hidden">
        <video
          ref={videoRef}
          src={videoUrl}
          className="w-full h-auto"
          controls
          onPlay={handlePlay}
          onPause={handlePause}
          onEnded={handlePause}
        />
        
        {/* Pose Overlay Canvas */}
        <canvas
          ref={canvasRef}
          className="absolute inset-0 w-full h-full pointer-events-none"
          style={{ display: isReady ? 'block' : 'none' }}
        />
      </div>

      {/* Processing Status */}
      {processingStatus.isProcessing && (
        <div className="mt-4 bg-gray-100 p-4 rounded-lg">
          <div className="flex justify-between text-sm text-gray-600 mb-2">
            <span>Processing {analysisMode} pose analysis</span>
            <span>Frame: {processingStatus.currentFrame} / {processingStatus.totalFrames}</span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
            <div 
              className="h-2 bg-blue-500 rounded-full transition-all duration-300"
              style={{ width: `${processingStatus.progress}%` }}
            />
          </div>
          
          <div className="flex justify-between text-xs text-gray-500">
            <span>{processingStatus.progress.toFixed(1)}% Complete</span>
            <span>
              Remaining: {Math.max(0, processingStatus.remaining).toFixed(1)}s
            </span>
          </div>
        </div>
      )}

      {/* Pose History Info */}
      {poseHistory.length > 0 && (
        <div className="mt-2 text-xs text-gray-500 text-center">
          Detected {poseHistory.length} pose frames • 
          Avg confidence: {(poseHistory.reduce((sum, p) => sum + (p.score || 0), 0) / poseHistory.length * 100).toFixed(1)}%
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;