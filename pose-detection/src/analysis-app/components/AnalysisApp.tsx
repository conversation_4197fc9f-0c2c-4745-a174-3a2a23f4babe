/**
 * Main Analysis Application Component
 * Integrates video upload, configuration, processing, and results
 */

import React, { useState, useCallback, useRef } from 'react';
import VideoPlayer from './VideoPlayer';
import UploadPanel from './UploadPanel';
import ConfigurationPanel from './ConfigurationPanel';
import { ResultsPage } from './ResultsPage';
import { 
  AppState, 
  VideoFile, 
  UserConfiguration, 
  AnalysisConfiguration,
  PoseData,
  RunningMetrics,
  ProcessingStatus
} from '../types';
import { RunningMetricsCalculator } from '../utils/runningMetrics';

const AnalysisApp: React.FC = () => {
  // App state
  const [currentState, setCurrentState] = useState<AppState>('upload');
  const [sideVideo, setSideVideo] = useState<VideoFile | null>(null);
  const [rearVideo, setRearVideo] = useState<VideoFile | null>(null);
  const [viewType] = useState<'side' | 'rear'>('side');
  
  // User configuration
  const [userConfig, setUserConfig] = useState<UserConfiguration>({
    height: { feet: 5, inches: 10 },
    weightUnit: 'lbs'
  });

  // Modal integration state
  const [analysisId, setAnalysisId] = useState<string | null>(null);
  const [userEmail, setUserEmail] = useState<string>('<EMAIL>'); // Default for testing
  
  // Analysis results (moved to ResultsPage)
  const [, setCurrentMetrics] = useState<RunningMetrics | null>(null);
  const [processingStatus, setProcessingStatus] = useState<ProcessingStatus>({
    isProcessing: false,
    progress: 0,
    currentFrame: 0,
    totalFrames: 0,
    elapsed: 0,
    remaining: 0,
    status: 'idle'
  });

  // Analysis configuration (fixed for BlazePose Full)
  const analysisConfig: AnalysisConfiguration = {
    analysisType: 'running',
    analysisMode: '3D',
    activityType: 'Running',
    videoSetup: 'Treadmill',
    overlayStyle: 'Medical',
    analysisQuality: 'Full',
    viewType: viewType
  };

  // Metrics calculator reference
  const metricsCalculatorRef = useRef<RunningMetricsCalculator | null>(null);

  // Initialize metrics calculator when user config changes
  React.useEffect(() => {
    metricsCalculatorRef.current = new RunningMetricsCalculator(userConfig.height);
  }, [userConfig.height]);

  // Check if analysis can start
  const canStartAnalysis = sideVideo !== null;

  // Handle video upload
  const handleVideoUpload = useCallback((type: 'side' | 'rear') => (video: VideoFile | null) => {
    if (type === 'side') {
      setSideVideo(video);
    } else {
      setRearVideo(video);
    }
    
    // Reset state when videos change
    if (video) {
      setCurrentState('upload');
      setCurrentMetrics(null);
      metricsCalculatorRef.current?.reset();
    }
  }, []);

  // Handle configuration changes
  const handleConfigChange = useCallback((config: UserConfiguration) => {
    setUserConfig(config);
  }, []);

  // Handle start analysis
  const handleStartAnalysis = useCallback(() => {
    if (!canStartAnalysis) return;
    
    console.log('🚀 Starting BlazePose running analysis...');
    console.log('Configuration:', {
      analysisConfig,
      userHeight: userConfig.height,
      videoName: sideVideo?.name
    });
    
    setCurrentState('processing');
    setCurrentMetrics(null);
    setProcessingStatus({
      isProcessing: true,
      progress: 0,
      currentFrame: 0,
      totalFrames: 0,
      elapsed: 0,
      remaining: 0,
      status: 'processing',
      message: 'Starting analysis...'
    });
  }, [canStartAnalysis, analysisConfig, userConfig.height, sideVideo]);

  // Handle Modal processing trigger
  const handleProcessingComplete = useCallback((analysisId: string) => {
    setAnalysisId(analysisId);
    setCurrentState('results');
  }, []);

  // Handle pose data from video analysis (transitioning to Modal preprocessing)
  const handlePoseData = useCallback((poseData: PoseData) => {
    // Update metrics if we have a calculator and we're in processing state
    if (metricsCalculatorRef.current && currentState === 'processing') {
      // Assume video dimensions for now - in real app, get from video element
      const videoWidth = 1920;
      const videoHeight = 1080;
      
      const metrics = metricsCalculatorRef.current.addFrame(poseData, videoWidth, videoHeight);
      
      if (metrics) {
        setCurrentMetrics(metrics);
        
        // Auto-transition to results after getting first metrics
        // This simulates processing completion until Modal integration is complete
        if (currentState === 'processing') {
          setTimeout(() => {
            // Generate analysis ID and transition using the proper flow
            const tempAnalysisId = `temp-analysis-${Date.now()}`;
            handleProcessingComplete(tempAnalysisId);
            setProcessingStatus(prev => ({ ...prev, isProcessing: false, status: 'complete' }));
          }, 1000);
        }
      }
    }
  }, [currentState, handleProcessingComplete]);

  // Handle new analysis
  const handleNewAnalysis = useCallback(() => {
    setCurrentState('upload');
    setSideVideo(null);
    setRearVideo(null);
    setCurrentMetrics(null);
    setAnalysisId(null);
    metricsCalculatorRef.current?.reset();
    setProcessingStatus({
      isProcessing: false,
      progress: 0,
      currentFrame: 0,
      totalFrames: 0,
      elapsed: 0,
      remaining: 0,
      status: 'idle'
    });
  }, []);

  // Handle back to processing/upload
  const handleBackFromResults = useCallback(() => {
    setCurrentState('processing');
  }, []);

  // Processing Screen
  if (currentState === 'processing') {
    return (
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-bold text-gray-900">
                🏃‍♂️ BlazePose Running Analysis
              </h1>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-blue-600">Processing</span>
              </div>
            </div>
          </div>
        </div>

        {/* Processing Content */}
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <div className="text-center space-y-6">
              <h2 className="text-3xl font-bold text-gray-900">3D Pose Processing</h2>
              <div className="text-lg text-gray-600">
                Status: <span className="font-semibold text-blue-600">PROCESSING</span>
              </div>
              
              {/* Progress Bar */}
              <div className="space-y-4">
                <div className="flex justify-between text-sm text-gray-600">
                  <span>Processing BlazePose Full at {processingStatus.elapsed.toFixed(1)}s</span>
                  <span>Frame: {processingStatus.currentFrame} / {processingStatus.totalFrames}</span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                  <div 
                    className="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-300 ease-out"
                    style={{ width: `${processingStatus.progress}%` }}
                  />
                </div>

                <div className="flex justify-between text-sm text-gray-600">
                  <span>{processingStatus.progress.toFixed(1)}% Complete</span>
                  <div className="flex gap-4">
                    <span>Time Remaining: {Math.max(0, processingStatus.remaining).toFixed(0)}s</span>
                    <span>Elapsed: {processingStatus.elapsed.toFixed(0)}s</span>
                  </div>
                </div>
              </div>

              {/* Video Player for Real-time Analysis */}
              {sideVideo && (
                <div className="mt-8">
                  <VideoPlayer
                    videoUrl={sideVideo.url}
                    analysisType="running"
                    viewType="side"
                    analysisMode="3D"
                    videoSetup="Treadmill"
                    overlayStyle="Medical"
                    userHeight={userConfig.height}
                    onPoseData={handlePoseData}
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Results Screen
  if (currentState === 'results') {
    // Generate analysisId if not already set
    if (!analysisId) {
      setAnalysisId(`analysis-${Date.now()}`);
      return <div>Loading...</div>;
    }

    return (
      <ResultsPage
        analysisId={analysisId}
        userEmail={userEmail}
        onBack={handleBackFromResults}
        onNewAnalysis={handleNewAnalysis}
      />
    );
  }

  // Upload Screen (Default)
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="text-center space-y-2">
            <h1 className="text-4xl font-bold text-gray-900">
              🏃‍♂️ BlazePose Running Analysis
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Upload your treadmill running videos for advanced 3D biomechanical analysis using BlazePose Full (39 keypoints).
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto p-6 space-y-8">
        {/* Upload Panels */}
        <div className="grid md:grid-cols-2 gap-6">
          <UploadPanel
            title="Upload Side View Video"
            description="Record from the side to capture leg extension and body posture"
            acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
            video={sideVideo}
            onVideoUpload={handleVideoUpload('side')}
          />
          <UploadPanel
            title="Upload Rear View Video (Optional)"
            description="Record from behind to analyze stride symmetry and foot placement"
            acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB"
            video={rearVideo}
            onVideoUpload={handleVideoUpload('rear')}
          />
        </div>

        {/* Configuration Panel */}
        <ConfigurationPanel
          userConfig={userConfig}
          onConfigChange={handleConfigChange}
        />

        {/* Start Analysis Button */}
        <div className="bg-white rounded-lg border border-gray-200 p-6 text-center">
          <button
            onClick={handleStartAnalysis}
            disabled={!canStartAnalysis}
            className={`inline-flex items-center gap-3 px-8 py-4 rounded-lg text-lg font-semibold transition-all ${
              canStartAnalysis
                ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            <div className="w-6 h-6 rounded-full border-2 border-current flex items-center justify-center">
              <div className="w-2 h-2 rounded-full bg-current"></div>
            </div>
            Start 3D Analysis
          </button>
          {!canStartAnalysis && (
            <p className="text-sm text-gray-500 mt-2">
              Please upload at least a side view video to begin analysis
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default AnalysisApp;