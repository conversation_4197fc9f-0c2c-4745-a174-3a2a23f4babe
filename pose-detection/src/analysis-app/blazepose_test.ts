/**
 * End-to-end test for BlazePose TFJS pipeline
 * This test verifies the complete user journey
 */

import * as poseDetection from '../index';

/**
 * Test the complete BlazePose TFJS user journey
 */
async function testBlazePoseUserJourney() {
  console.log('🚀 Starting BlazePose TFJS User Journey Test...');
  
  try {
    // Step 1: Verify SupportedModels enum
    console.log('✅ Step 1: Checking SupportedModels enum');
    console.log('Available models:', Object.values(poseDetection.SupportedModels));
    
    if (!poseDetection.SupportedModels.BlazePose) {
      throw new Error('BlazePose model not found in SupportedModels');
    }
    
    // Step 2: Create detector with TFJS runtime
    console.log('✅ Step 2: Creating BlazePose TFJS detector...');
    
    const modelConfig: poseDetection.BlazePoseTfjsModelConfig = {
      runtime: 'tfjs',
      modelType: 'full',
      enableSmoothing: true
    };
    
    const detector = await poseDetection.createDetector(
      poseDetection.SupportedModels.BlazePose, 
      modelConfig
    );
    
    console.log('✅ Step 2 Complete: Detector created successfully');
    console.log('Detector methods:', Object.getOwnPropertyNames(Object.getPrototypeOf(detector)));
    
    // Step 3: Verify detector interface
    console.log('✅ Step 3: Verifying detector interface...');
    
    if (typeof detector.estimatePoses !== 'function') {
      throw new Error('estimatePoses method not found');
    }
    
    if (typeof detector.dispose !== 'function') {
      throw new Error('dispose method not found');
    }
    
    if (typeof detector.reset !== 'function') {
      throw new Error('reset method not found');
    }
    
    console.log('✅ Step 3 Complete: All required methods available');
    
    // Step 4: Create a simple test image (canvas)
    console.log('✅ Step 4: Creating test image...');
    
    // Create a simple test canvas
    const canvas = new OffscreenCanvas(224, 224);
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      throw new Error('Could not create canvas context');
    }
    
    // Draw a simple pattern
    ctx.fillStyle = 'red';
    ctx.fillRect(0, 0, 224, 224);
    ctx.fillStyle = 'blue';
    ctx.fillRect(50, 50, 100, 100);
    
    console.log('✅ Step 4 Complete: Test image created');
    
    // Step 5: Test pose estimation
    console.log('✅ Step 5: Testing pose estimation...');
    
    const estimationConfig: poseDetection.BlazePoseTfjsEstimationConfig = {
      maxPoses: 1,
      flipHorizontal: false
    };
    
    const poses = await detector.estimatePoses(canvas, estimationConfig);
    
    console.log('✅ Step 5 Complete: Pose estimation executed');
    console.log(`Number of poses detected: ${poses.length}`);
    
    if (poses.length > 0) {
      const pose = poses[0];
      console.log(`Pose score: ${pose.score}`);
      console.log(`Number of keypoints: ${pose.keypoints.length}`);
      console.log(`Has 3D keypoints: ${pose.keypoints3D ? 'Yes' : 'No'}`);
      
      if (pose.keypoints3D) {
        console.log(`Number of 3D keypoints: ${pose.keypoints3D.length}`);
      }
    }
    
    // Step 6: Test reset and dispose
    console.log('✅ Step 6: Testing reset and dispose...');
    
    detector.reset();
    console.log('Reset executed successfully');
    
    detector.dispose();
    console.log('Dispose executed successfully');
    
    console.log('🎉 BlazePose TFJS User Journey Test PASSED!');
    return true;
    
  } catch (error) {
    console.error('❌ BlazePose TFJS User Journey Test FAILED:', error);
    return false;
  }
}

// Export for use in other tests
export { testBlazePoseUserJourney };

// If running directly in Node.js environment
if (typeof window === 'undefined') {
  console.log('Node.js environment detected - test will require browser environment to run fully');
}