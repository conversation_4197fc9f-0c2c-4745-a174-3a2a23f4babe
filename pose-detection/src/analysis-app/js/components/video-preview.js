/**
 * Video Preview Component
 * Shows video thumbnail and metadata after validation
 */

import { videoValidator } from '../services/video-validator.js';

class VideoPreview {
    constructor(containerId) {
        this.container = document.getElementById(containerId);
        this.video = null;
        this.canvas = null;
        this.thumbnailUrl = null;
    }

    /**
     * Create preview from validated video
     */
    async createPreview(file, validation) {
        if (!validation.isValid) {
            return this.showError(validation);
        }

        const metadata = validation.metadata;
        const previewHTML = `
            <div class="video-preview">
                <div class="preview-header">
                    <h4>${this.getViewTypeLabel(file.name)} Video</h4>
                    <span class="quality-badge quality-${validation.summary.quality.toLowerCase()}">
                        ${validation.summary.quality}
                    </span>
                </div>
                
                <div class="preview-content">
                    <div class="thumbnail-container">
                        <canvas id="thumbnail-${Date.now()}" width="320" height="180"></canvas>
                        <div class="duration-badge">${metadata.duration.toFixed(1)}s</div>
                    </div>
                    
                    <div class="metadata-info">
                        <div class="metadata-row">
                            <span class="metadata-label">File:</span>
                            <span class="metadata-value">${this.truncateFilename(file.name, 25)}</span>
                        </div>
                        <div class="metadata-row">
                            <span class="metadata-label">Size:</span>
                            <span class="metadata-value">${validation.summary.size}</span>
                        </div>
                        <div class="metadata-row">
                            <span class="metadata-label">Resolution:</span>
                            <span class="metadata-value">${validation.summary.resolution}</span>
                        </div>
                        <div class="metadata-row">
                            <span class="metadata-label">FPS:</span>
                            <span class="metadata-value">${validation.summary.fps}</span>
                        </div>
                        <div class="metadata-row">
                            <span class="metadata-label">Codec:</span>
                            <span class="metadata-value">${metadata.codec}</span>
                        </div>
                    </div>
                </div>
                
                ${this.renderWarnings(validation.warnings)}
                
                <div class="preview-actions">
                    <button class="btn btn-secondary btn-sm" onclick="window.videoPreviewManager.removePreview('${this.container.id}')">
                        Remove
                    </button>
                    <button class="btn btn-primary btn-sm" disabled>
                        <svg class="icon-check" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                        </svg>
                        Ready to Upload
                    </button>
                </div>
            </div>
        `;

        this.container.innerHTML = previewHTML;
        
        // Generate thumbnail
        await this.generateThumbnail(file, metadata);
        
        // Add preview styling
        this.addPreviewStyles();
    }

    /**
     * Generate video thumbnail
     */
    async generateThumbnail(file, metadata) {
        const canvasId = this.container.querySelector('canvas').id;
        const canvas = document.getElementById(canvasId);
        const ctx = canvas.getContext('2d');
        
        const video = document.createElement('video');
        const url = URL.createObjectURL(file);
        
        return new Promise((resolve) => {
            video.onloadeddata = () => {
                // Seek to 10% of video for better thumbnail
                video.currentTime = metadata.duration * 0.1;
            };
            
            video.onseeked = () => {
                // Scale to fit canvas maintaining aspect ratio
                const scale = Math.min(
                    canvas.width / video.videoWidth,
                    canvas.height / video.videoHeight
                );
                
                const scaledWidth = video.videoWidth * scale;
                const scaledHeight = video.videoHeight * scale;
                const x = (canvas.width - scaledWidth) / 2;
                const y = (canvas.height - scaledHeight) / 2;
                
                // Draw black background
                ctx.fillStyle = '#000';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // Draw video frame
                ctx.drawImage(video, x, y, scaledWidth, scaledHeight);
                
                // Store thumbnail data URL
                this.thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8);
                
                // Cleanup
                URL.revokeObjectURL(url);
                video.remove();
                resolve();
            };
            
            video.onerror = () => {
                // Draw placeholder on error
                ctx.fillStyle = '#2d3748';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.fillStyle = '#718096';
                ctx.font = '14px sans-serif';
                ctx.textAlign = 'center';
                ctx.fillText('Preview unavailable', canvas.width / 2, canvas.height / 2);
                
                URL.revokeObjectURL(url);
                video.remove();
                resolve();
            };
            
            video.src = url;
            video.load();
        });
    }

    /**
     * Show error state
     */
    showError(validation) {
        const errorHTML = `
            <div class="video-preview error">
                <div class="preview-header">
                    <h4>Invalid Video</h4>
                    <span class="quality-badge quality-error">Error</span>
                </div>
                
                <div class="error-content">
                    ${validation.errors.map(error => `
                        <div class="error-item">
                            <svg class="error-icon" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                            <div class="error-text">
                                <div class="error-message">${error.message}</div>
                                <div class="error-details">${error.details}</div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                
                <div class="preview-actions">
                    <button class="btn btn-secondary" onclick="window.videoPreviewManager.removePreview('${this.container.id}')">
                        Remove & Try Again
                    </button>
                </div>
            </div>
        `;
        
        this.container.innerHTML = errorHTML;
        this.addPreviewStyles();
    }

    /**
     * Render warnings
     */
    renderWarnings(warnings) {
        if (!warnings || warnings.length === 0) return '';
        
        return `
            <div class="warnings-container">
                ${warnings.map(warning => `
                    <div class="warning-item">
                        <svg class="warning-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                        <span>${warning.message}</span>
                    </div>
                `).join('')}
            </div>
        `;
    }

    /**
     * Get view type label
     */
    getViewTypeLabel(filename) {
        const lower = filename.toLowerCase();
        if (lower.includes('side')) return 'Side View';
        if (lower.includes('rear') || lower.includes('back')) return 'Rear View';
        return 'Running';
    }

    /**
     * Truncate filename
     */
    truncateFilename(filename, maxLength) {
        if (filename.length <= maxLength) return filename;
        const ext = filename.split('.').pop();
        const name = filename.substring(0, filename.lastIndexOf('.'));
        const truncated = name.substring(0, maxLength - ext.length - 4) + '...';
        return `${truncated}.${ext}`;
    }

    /**
     * Add preview styles
     */
    addPreviewStyles() {
        if (document.getElementById('video-preview-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'video-preview-styles';
        styles.textContent = `
            .video-preview {
                background: var(--bg-secondary);
                border: 1px solid var(--border-color);
                border-radius: 12px;
                padding: 20px;
                margin-bottom: 16px;
            }
            
            .video-preview.error {
                border-color: var(--error);
                background: rgba(229, 62, 62, 0.05);
            }
            
            .preview-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
            }
            
            .preview-header h4 {
                margin: 0;
                font-size: 16px;
                color: var(--text-primary);
            }
            
            .quality-badge {
                padding: 4px 12px;
                border-radius: 16px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
            }
            
            .quality-excellent { background: #38a169; color: white; }
            .quality-good { background: #4299e1; color: white; }
            .quality-fair { background: #d69e2e; color: white; }
            .quality-poor { background: #e53e3e; color: white; }
            .quality-error { background: #e53e3e; color: white; }
            
            .preview-content {
                display: flex;
                gap: 20px;
                margin-bottom: 16px;
            }
            
            .thumbnail-container {
                position: relative;
                flex-shrink: 0;
            }
            
            .thumbnail-container canvas {
                border-radius: 8px;
                background: #000;
            }
            
            .duration-badge {
                position: absolute;
                bottom: 8px;
                right: 8px;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 2px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: 600;
            }
            
            .metadata-info {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }
            
            .metadata-row {
                display: flex;
                justify-content: space-between;
                font-size: 14px;
            }
            
            .metadata-label {
                color: var(--text-muted);
                font-weight: 500;
            }
            
            .metadata-value {
                color: var(--text-primary);
                font-weight: 600;
            }
            
            .warnings-container {
                background: rgba(214, 158, 46, 0.1);
                border: 1px solid rgba(214, 158, 46, 0.3);
                border-radius: 8px;
                padding: 12px;
                margin-bottom: 16px;
            }
            
            .warning-item {
                display: flex;
                align-items: center;
                gap: 8px;
                color: #d69e2e;
                font-size: 13px;
                margin-bottom: 4px;
            }
            
            .warning-item:last-child {
                margin-bottom: 0;
            }
            
            .warning-icon {
                width: 16px;
                height: 16px;
                flex-shrink: 0;
            }
            
            .error-content {
                margin-bottom: 16px;
            }
            
            .error-item {
                display: flex;
                gap: 12px;
                margin-bottom: 12px;
            }
            
            .error-icon {
                width: 20px;
                height: 20px;
                color: var(--error);
                flex-shrink: 0;
            }
            
            .error-message {
                font-weight: 600;
                color: var(--error);
                margin-bottom: 4px;
            }
            
            .error-details {
                font-size: 13px;
                color: var(--text-muted);
            }
            
            .preview-actions {
                display: flex;
                gap: 12px;
                justify-content: flex-end;
            }
            
            .icon-check {
                width: 16px;
                height: 16px;
                margin-right: 4px;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * Remove preview
     */
    remove() {
        if (this.thumbnailUrl) {
            URL.revokeObjectURL(this.thumbnailUrl);
        }
        this.container.innerHTML = '';
    }

    /**
     * Get thumbnail data URL
     */
    getThumbnail() {
        return this.thumbnailUrl;
    }
}

// Global preview manager
window.videoPreviewManager = {
    previews: new Map(),
    
    createPreview(containerId, file, validation) {
        const preview = new VideoPreview(containerId);
        preview.createPreview(file, validation);
        this.previews.set(containerId, preview);
        return preview;
    },
    
    removePreview(containerId) {
        const preview = this.previews.get(containerId);
        if (preview) {
            preview.remove();
            this.previews.delete(containerId);
        }
    }
};

export { VideoPreview };