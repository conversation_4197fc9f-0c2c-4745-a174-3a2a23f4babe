/**
 * Processing Screen Component
 * Beautiful processing UI with progress tracking and status updates
 */

import { ENV } from '../../config/env.js';
import { supabaseService } from '../services/supabase-client.js';

class ProcessingScreen {
    constructor() {
        this.analysisId = null;
        this.subscription = null;
        this.pollInterval = null;
        this.startTime = null;
        this.statusHistory = [];
    }

    /**
     * Show processing screen for analysis
     */
    async show(analysisId, analysisData) {
        this.analysisId = analysisId;
        this.startTime = Date.now();
        this.statusHistory = [];

        // Create processing screen HTML
        const screenHTML = this.createProcessingHTML(analysisData);
        
        // Find or create processing container
        let container = document.getElementById('processingScreen');
        if (!container) {
            container = document.createElement('div');
            container.id = 'processingScreen';
            container.className = 'processing-overlay';
            document.body.appendChild(container);
        }
        
        container.innerHTML = screenHTML;
        container.style.display = 'flex';
        
        // Add processing screen styles
        this.addProcessingStyles();
        
        // Start monitoring
        await this.startMonitoring();
        
        // Show estimated time
        this.showEstimatedTime();
    }

    /**
     * Create processing screen HTML
     */
    createProcessingHTML(analysisData) {
        const hasVideos = analysisData.side_video_id || analysisData.rear_video_id;
        const videoCount = (analysisData.side_video_id ? 1 : 0) + (analysisData.rear_video_id ? 1 : 0);
        
        return `
            <div class="processing-modal">
                <!-- Header -->
                <div class="processing-header">
                    <div class="processing-logo">
                        <div class="logo-container">
                            <img src="../../Logos/MaxWattzLogoSm-White.png" alt="MaxWattz" class="logo-image">
                            <h2>MaxWattz</h2>
                        </div>
                    </div>
                    <div class="processing-title">
                        <h1>3D Pose Processing</h1>
                        <p class="processing-subtitle">Status: <span id="processingStatus">INITIALIZING</span></p>
                    </div>
                </div>

                <!-- Main Processing Area -->
                <div class="processing-main">
                    <!-- Progress Circle -->
                    <div class="progress-circle-container">
                        <svg class="progress-circle" viewBox="0 0 200 200">
                            <defs>
                                <linearGradient id="progressGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            <!-- Background circle -->
                            <circle cx="100" cy="100" r="90" stroke="rgba(255,255,255,0.1)" stroke-width="8" fill="none"/>
                            <!-- Progress circle -->
                            <circle id="progressCircle" cx="100" cy="100" r="90" stroke="url(#progressGradient)" 
                                    stroke-width="8" fill="none" stroke-linecap="round"
                                    stroke-dasharray="565.48" stroke-dashoffset="565.48"
                                    transform="rotate(-90 100 100)"/>
                        </svg>
                        <div class="progress-content">
                            <div class="progress-percentage" id="progressPercentage">0%</div>
                            <div class="progress-text" id="progressText">Processing 3D pose at 3.70s</div>
                        </div>
                    </div>

                    <!-- Status Information -->
                    <div class="status-info">
                        <div class="status-row">
                            <span class="status-label">Frame:</span>
                            <span class="status-value" id="frameCount">0 / 300</span>
                        </div>
                        <div class="status-row">
                            <span class="status-label">Completion:</span>
                            <span class="status-value" id="completionPercent">0% Complete</span>
                        </div>
                        <div class="status-row">
                            <span class="status-label">Time Remaining:</span>
                            <span class="status-value" id="timeRemaining">0:30</span>
                        </div>
                        <div class="status-row">
                            <span class="status-label">Elapsed:</span>
                            <span class="status-value" id="elapsedTime">0:05</span>
                        </div>
                    </div>

                    <!-- Processing Steps -->
                    <div class="processing-steps">
                        <div class="step-item" id="step-upload">
                            <div class="step-icon completed">✓</div>
                            <span class="step-text">Video Upload</span>
                        </div>
                        <div class="step-item active" id="step-processing">
                            <div class="step-icon processing">
                                <div class="spinner"></div>
                            </div>
                            <span class="step-text">3D Pose Analysis</span>
                        </div>
                        <div class="step-item" id="step-smoothing">
                            <div class="step-icon">3</div>
                            <span class="step-text">Temporal Smoothing</span>
                        </div>
                        <div class="step-item" id="step-metrics">
                            <div class="step-icon">4</div>
                            <span class="step-text">Biomechanics Calculation</span>
                        </div>
                        <div class="step-item" id="step-complete">
                            <div class="step-icon">5</div>
                            <span class="step-text">Analysis Complete</span>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="processing-footer">
                    <p class="processing-note">
                        🔬 Using Heavy BlazePose + SmoothNet for maximum accuracy<br>
                        ⏱️ This can take 1-2 minutes to process
                    </p>
                    <div class="processing-actions">
                        <button class="btn btn-secondary" id="cancelProcessing">Cancel</button>
                        <button class="btn btn-primary" id="minimizeProcessing">Continue in Background</button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Start monitoring analysis progress
     */
    async startMonitoring() {
        // Subscribe to real-time updates
        this.subscription = supabaseService.subscribeToAnalysisUpdates(
            this.analysisId,
            (analysis) => {
                this.handleStatusUpdate(analysis);
            }
        );

        // Fallback polling in case real-time updates fail
        this.pollInterval = setInterval(async () => {
            try {
                const analysis = await supabaseService.getAnalysisStatus(this.analysisId);
                this.handleStatusUpdate(analysis);
            } catch (error) {
                console.error('Failed to poll analysis status:', error);
            }
        }, ENV.STATUS_POLL_INTERVAL_MS);

        // Update elapsed time every second
        this.timeInterval = setInterval(() => {
            this.updateElapsedTime();
        }, 1000);
    }

    /**
     * Handle status updates from Supabase
     */
    handleStatusUpdate(analysis) {
        const status = analysis.status.toUpperCase();
        const previousStatus = this.statusHistory[this.statusHistory.length - 1];
        
        // Only update if status changed
        if (previousStatus !== status) {
            this.statusHistory.push(status);
            this.updateProcessingStep(status);
            this.updateStatusDisplay(status);
            
            // Handle completion or errors
            if (status === 'COMPLETED') {
                this.handleCompletion(analysis);
            } else if (status === 'ERROR') {
                this.handleError(analysis);
            }
        }

        // Update queue information if available
        if (analysis.processing_queue && analysis.processing_queue.length > 0) {
            this.updateProcessingProgress(analysis.processing_queue);
        }
    }

    /**
     * Update processing step visual
     */
    updateProcessingStep(status) {
        // Reset all steps
        document.querySelectorAll('.step-item').forEach(step => {
            step.classList.remove('active', 'completed');
        });

        switch (status) {
            case 'UPLOADING':
                this.setStepActive('step-upload');
                break;
            case 'PENDING':
                this.setStepCompleted('step-upload');
                this.setStepActive('step-processing');
                break;
            case 'PROCESSING_SIDE':
            case 'PROCESSING_REAR':
                this.setStepCompleted('step-upload');
                this.setStepActive('step-processing');
                this.animateProgress(25); // 25% for pose detection
                break;
            case 'COMPLETED':
                this.setAllStepsCompleted();
                this.animateProgress(100);
                break;
        }
    }

    /**
     * Update processing progress from queue data
     */
    updateProcessingProgress(queueData) {
        const currentJob = queueData.find(job => job.status === 'processing');
        if (!currentJob) return;

        // Simulate frame-by-frame progress
        const progress = this.simulateFrameProgress();
        this.updateProgressDisplay(progress);
    }

    /**
     * Simulate frame-by-frame progress for visual appeal
     */
    simulateFrameProgress() {
        const elapsed = (Date.now() - this.startTime) / 1000;
        const estimatedTotalTime = 90; // 90 seconds estimated
        const baseProgress = Math.min((elapsed / estimatedTotalTime) * 80, 80); // Max 80% from time
        
        // Add some realistic variation
        const variation = Math.sin(elapsed * 0.1) * 5;
        const progress = Math.max(0, Math.min(95, baseProgress + variation)); // Never exceed 95% until complete
        
        return {
            percentage: Math.round(progress),
            frame: Math.round((progress / 100) * 300), // Assume 300 frames total
            totalFrames: 300,
            timeRemaining: Math.max(0, estimatedTotalTime - elapsed)
        };
    }

    /**
     * Update progress display
     */
    updateProgressDisplay(progress) {
        // Update progress circle
        this.animateProgress(progress.percentage);
        
        // Update status text
        const frameElement = document.getElementById('frameCount');
        if (frameElement) {
            frameElement.textContent = `${progress.frame} / ${progress.totalFrames}`;
        }
        
        const completionElement = document.getElementById('completionPercent');
        if (completionElement) {
            completionElement.textContent = `${progress.percentage}% Complete`;
        }
        
        const timeRemainingElement = document.getElementById('timeRemaining');
        if (timeRemainingElement) {
            const minutes = Math.floor(progress.timeRemaining / 60);
            const seconds = Math.round(progress.timeRemaining % 60);
            timeRemainingElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
        
        const progressTextElement = document.getElementById('progressText');
        if (progressTextElement) {
            const currentTime = ((progress.frame / progress.totalFrames) * 10).toFixed(2);
            progressTextElement.textContent = `Processing 3D pose at ${currentTime}s`;
        }
    }

    /**
     * Animate progress circle
     */
    animateProgress(percentage) {
        const circle = document.getElementById('progressCircle');
        const percentElement = document.getElementById('progressPercentage');
        
        if (circle && percentElement) {
            const circumference = 2 * Math.PI * 90; // radius = 90
            const offset = circumference - (percentage / 100) * circumference;
            
            circle.style.strokeDashoffset = offset;
            percentElement.textContent = `${percentage}%`;
        }
    }

    /**
     * Update elapsed time
     */
    updateElapsedTime() {
        const elapsed = (Date.now() - this.startTime) / 1000;
        const minutes = Math.floor(elapsed / 60);
        const seconds = Math.round(elapsed % 60);
        
        const elapsedElement = document.getElementById('elapsedTime');
        if (elapsedElement) {
            elapsedElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    /**
     * Update status display
     */
    updateStatusDisplay(status) {
        const statusElement = document.getElementById('processingStatus');
        if (statusElement) {
            const statusMap = {
                'UPLOADING': 'UPLOADING',
                'PENDING': 'QUEUED',
                'PROCESSING_SIDE': 'PROCESSING',
                'PROCESSING_REAR': 'PROCESSING',
                'COMPLETED': 'COMPLETED',
                'ERROR': 'ERROR'
            };
            statusElement.textContent = statusMap[status] || status;
        }
    }

    /**
     * Set step as active
     */
    setStepActive(stepId) {
        const step = document.getElementById(stepId);
        if (step) {
            step.classList.add('active');
        }
    }

    /**
     * Set step as completed
     */
    setStepCompleted(stepId) {
        const step = document.getElementById(stepId);
        if (step) {
            step.classList.add('completed');
            const icon = step.querySelector('.step-icon');
            if (icon) {
                icon.textContent = '✓';
                icon.classList.add('completed');
                icon.classList.remove('processing');
            }
        }
    }

    /**
     * Set all steps as completed
     */
    setAllStepsCompleted() {
        const steps = ['step-upload', 'step-processing', 'step-smoothing', 'step-metrics', 'step-complete'];
        steps.forEach(stepId => this.setStepCompleted(stepId));
    }

    /**
     * Handle analysis completion
     */
    handleCompletion(analysis) {
        // Update to 100% and show success
        this.animateProgress(100);
        
        // Show completion message
        setTimeout(() => {
            this.showCompletionMessage();
        }, 1000);
    }

    /**
     * Handle analysis error
     */
    handleError(analysis) {
        const errorMessage = analysis.error_message || 'Unknown error occurred';
        this.showErrorMessage(errorMessage);
    }

    /**
     * Show completion message
     */
    showCompletionMessage() {
        const modal = document.querySelector('.processing-modal');
        if (modal) {
            modal.innerHTML += `
                <div class="completion-overlay">
                    <div class="completion-content">
                        <div class="completion-icon">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M9 11l3 3L22 4"></path>
                                <path d="M21 12v7a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2h11"></path>
                            </svg>
                        </div>
                        <h2>Analysis Complete!</h2>
                        <p>Your running biomechanics have been analyzed</p>
                        <button class="btn btn-primary" onclick="window.processingScreen.viewResults()">
                            View Results
                        </button>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Show error message
     */
    showErrorMessage(errorMessage) {
        const modal = document.querySelector('.processing-modal');
        if (modal) {
            modal.innerHTML += `
                <div class="completion-overlay error">
                    <div class="completion-content">
                        <div class="completion-icon error">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="15" y1="9" x2="9" y2="15"></line>
                                <line x1="9" y1="9" x2="15" y2="15"></line>
                            </svg>
                        </div>
                        <h2>Processing Failed</h2>
                        <p>${errorMessage}</p>
                        <div class="error-actions">
                            <button class="btn btn-secondary" onclick="window.processingScreen.tryAgain()">
                                Try Again
                            </button>
                            <button class="btn btn-primary" onclick="window.processingScreen.contactSupport()">
                                Contact Support
                            </button>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Show estimated time based on video count and size
     */
    showEstimatedTime() {
        // This would be called initially to set expectations
        const progressTextElement = document.getElementById('progressText');
        if (progressTextElement) {
            progressTextElement.textContent = 'Initializing 3D pose analysis...';
        }
    }

    /**
     * View results
     */
    viewResults() {
        this.close();
        // Navigate to results page
        window.location.hash = `#analysis/${this.analysisId}`;
    }

    /**
     * Try again
     */
    tryAgain() {
        this.close();
        // Reset to upload page
        window.location.hash = '#upload';
    }

    /**
     * Contact support
     */
    contactSupport() {
        // Open support modal or email
        window.open('mailto:<EMAIL>?subject=Processing Error&body=Analysis ID: ' + this.analysisId);
    }

    /**
     * Close processing screen
     */
    close() {
        // Clean up subscriptions
        if (this.subscription) {
            supabaseService.unsubscribe(this.subscription);
        }
        
        if (this.pollInterval) {
            clearInterval(this.pollInterval);
        }
        
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
        }
        
        // Remove from DOM
        const container = document.getElementById('processingScreen');
        if (container) {
            container.style.display = 'none';
            container.remove();
        }
    }

    /**
     * Add processing screen styles
     */
    addProcessingStyles() {
        if (document.getElementById('processing-screen-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'processing-screen-styles';
        styles.textContent = `
            .processing-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                color: white;
            }
            
            .processing-modal {
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 24px;
                padding: 40px;
                max-width: 600px;
                width: 90%;
                text-align: center;
                box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            }
            
            .processing-header {
                margin-bottom: 40px;
            }
            
            .processing-logo .logo-container {
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 12px;
                margin-bottom: 20px;
            }
            
            .processing-logo .logo-image {
                width: 48px;
                height: 48px;
                border-radius: 12px;
            }
            
            .processing-logo h2 {
                font-size: 28px;
                font-weight: 700;
                margin: 0;
                color: white;
            }
            
            .processing-title h1 {
                font-size: 32px;
                font-weight: 800;
                margin: 0 0 8px 0;
                color: white;
            }
            
            .processing-subtitle {
                font-size: 16px;
                color: rgba(255, 255, 255, 0.8);
                margin: 0;
            }
            
            #processingStatus {
                font-weight: 600;
                color: #4ade80;
            }
            
            .processing-main {
                margin-bottom: 40px;
            }
            
            .progress-circle-container {
                position: relative;
                display: inline-block;
                margin-bottom: 30px;
            }
            
            .progress-circle {
                width: 200px;
                height: 200px;
                transform: rotate(-90deg);
            }
            
            .progress-circle circle {
                transition: stroke-dashoffset 0.5s ease;
            }
            
            .progress-content {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                text-align: center;
            }
            
            .progress-percentage {
                font-size: 32px;
                font-weight: 800;
                color: white;
                line-height: 1;
            }
            
            .progress-text {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                margin-top: 8px;
            }
            
            .status-info {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 16px;
                margin-bottom: 30px;
                max-width: 400px;
                margin-left: auto;
                margin-right: auto;
            }
            
            .status-row {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 12px 16px;
                background: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                font-size: 14px;
            }
            
            .status-label {
                color: rgba(255, 255, 255, 0.8);
            }
            
            .status-value {
                font-weight: 600;
                color: white;
            }
            
            .processing-steps {
                display: flex;
                justify-content: space-between;
                align-items: center;
                max-width: 500px;
                margin: 0 auto;
            }
            
            .step-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 8px;
                flex: 1;
                position: relative;
            }
            
            .step-item:not(:last-child)::after {
                content: '';
                position: absolute;
                top: 20px;
                left: 60%;
                right: -40%;
                height: 2px;
                background: rgba(255, 255, 255, 0.2);
                z-index: 0;
            }
            
            .step-item.completed:not(:last-child)::after {
                background: #4ade80;
            }
            
            .step-icon {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: rgba(255, 255, 255, 0.2);
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                font-size: 14px;
                color: rgba(255, 255, 255, 0.8);
                position: relative;
                z-index: 1;
            }
            
            .step-item.active .step-icon {
                background: #4ade80;
                color: white;
            }
            
            .step-item.completed .step-icon {
                background: #4ade80;
                color: white;
            }
            
            .step-icon.processing {
                background: #4ade80;
            }
            
            .spinner {
                width: 20px;
                height: 20px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-top: 2px solid white;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .step-text {
                font-size: 12px;
                color: rgba(255, 255, 255, 0.8);
                text-align: center;
                max-width: 80px;
                line-height: 1.2;
            }
            
            .step-item.active .step-text,
            .step-item.completed .step-text {
                color: white;
                font-weight: 600;
            }
            
            .processing-footer {
                border-top: 1px solid rgba(255, 255, 255, 0.2);
                padding-top: 30px;
            }
            
            .processing-note {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                line-height: 1.5;
                margin-bottom: 20px;
            }
            
            .processing-actions {
                display: flex;
                gap: 12px;
                justify-content: center;
            }
            
            .processing-actions .btn {
                padding: 12px 24px;
                border-radius: 8px;
                font-weight: 600;
                border: none;
                cursor: pointer;
                transition: all 0.2s;
            }
            
            .processing-actions .btn-secondary {
                background: rgba(255, 255, 255, 0.1);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            
            .processing-actions .btn-secondary:hover {
                background: rgba(255, 255, 255, 0.2);
            }
            
            .processing-actions .btn-primary {
                background: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.4);
            }
            
            .processing-actions .btn-primary:hover {
                background: rgba(255, 255, 255, 0.3);
            }
            
            .completion-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.8);
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 24px;
            }
            
            .completion-content {
                text-align: center;
                padding: 40px;
            }
            
            .completion-icon {
                width: 80px;
                height: 80px;
                margin: 0 auto 20px;
                background: #4ade80;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            
            .completion-icon.error {
                background: #ef4444;
            }
            
            .completion-icon svg {
                width: 40px;
                height: 40px;
                color: white;
            }
            
            .completion-content h2 {
                font-size: 24px;
                font-weight: 700;
                margin: 0 0 8px 0;
                color: white;
            }
            
            .completion-content p {
                color: rgba(255, 255, 255, 0.8);
                margin-bottom: 24px;
            }
            
            .error-actions {
                display: flex;
                gap: 12px;
                justify-content: center;
            }
        `;
        
        document.head.appendChild(styles);
    }
}

// Export singleton instance
export const processingScreen = new ProcessingScreen();

// Make available globally for button handlers
window.processingScreen = processingScreen;