/**
 * MaxWattz - Side View Analysis
 * Handles side view running analysis
 */

class SideViewAnalysis {
    constructor() {
        this.video = null;
        this.canvas = null;
        this.ctx = null;
        this.isProcessing = false;
        this.animationId = null;
        this.lastFrameTime = 0;
        this.frameRate = 30; // Target 30fps
        
        // Analysis state
        this.currentPose = null;
        this.poseHistory = [];
        this.runnerData = null;
        
        this.init();
    }

    /**
     * Initialize side view analysis
     */
    init() {
        // Wait for page to load
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
        
        // Also check if DOM is already ready
        if (document.readyState !== 'loading') {
            this.setupEventListeners();
        }
    }

    /**
     * Setup event listeners for side view analysis
     */
    setupEventListeners() {
        // Listen for navigation to side analysis page
        window.addEventListener('appNavigate', (e) => {
            if (e.detail.page === 'side-analysis') {
                this.initializePage();
            }
        });

        // Listen for file upload completion
        window.addEventListener('fileUploaded', (e) => {
            if (e.detail.view === 'side') {
                this.handleFileUpload(e.detail);
            }
        });
    }

    /**
     * Initialize side analysis page
     */
    async initializePage() {
        console.log('🎬 Initializing side view analysis...');
        
        try {
            // Get DOM elements
            this.video = document.getElementById('analysisVideo');
            this.canvas = document.getElementById('analysisCanvas');
            
            if (!this.video || !this.canvas) {
                console.error('❌ Required DOM elements not found');
                return;
            }
            
            this.ctx = this.canvas.getContext('2d');
            
            // Setup video and canvas
            this.setupVideoCanvas();
            
            // Setup control handlers
            this.setupControlHandlers();
            
            // Load uploaded video if available
            await this.loadUploadedVideo();
            
            // Initialize analysis system
            await this.initializeAnalysis();
            
            console.log('✅ Side view analysis initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize side view analysis:', error);
            this.showError('Failed to initialize analysis. Please try again.');
        }
    }

    /**
     * Setup video and canvas
     */
    setupVideoCanvas() {
        // Video event listeners
        this.video.addEventListener('loadedmetadata', () => {
            this.updateCanvasSize();
            this.updateUI();
        });
        
        this.video.addEventListener('play', () => {
            this.startProcessing();
        });
        
        this.video.addEventListener('pause', () => {
            this.stopProcessing();
        });
        
        this.video.addEventListener('ended', () => {
            this.stopProcessing();
        });
        
        // Canvas setup
        this.canvas.style.position = 'absolute';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.pointerEvents = 'none';
    }

    /**
     * Update canvas size to match video
     */
    updateCanvasSize() {
        if (!this.video || !this.canvas) return;
        
        const rect = this.video.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        console.log(`📐 Canvas size: ${rect.width}x${rect.height}`);
    }

    /**
     * Setup control button handlers
     */
    setupControlHandlers() {
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        const processBtn = document.getElementById('processBtn');
        
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                this.video?.play();
            });
        }
        
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                this.video?.pause();
            });
        }
        
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetVideo();
            });
        }
        
        if (processBtn) {
            processBtn.addEventListener('click', () => {
                this.processCurrentFrame();
            });
        }
    }

    /**
     * Load uploaded video
     */
    async loadUploadedVideo() {
        if (!window.uploadedFile) {
            console.log('ℹ️ No uploaded file found');
            return;
        }
        
        const fileURL = URL.createObjectURL(window.uploadedFile);
        this.video.src = fileURL;
        
        // Store runner data
        this.runnerData = window.runnerData;
        
        console.log('📁 Video loaded:', window.uploadedFile.name);
        this.updateUI();
    }

    /**
     * Initialize analysis system
     */
    async initializeAnalysis() {
        try {
            // Ensure analysis common is initialized
            if (!analysisCommon.isModelLoaded) {
                this.updateModelStatus('Loading...');
                await analysisCommon.init();
            }
            
            // Initialize coordinate scaler if runner data available
            if (this.runnerData) {
                analysisCommon.initializeCoordinateScaler(this.runnerData);
            }
            
            this.updateModelStatus('Loaded & Verified (Lite - 33 keypoints)');
            
        } catch (error) {
            console.error('❌ Analysis initialization failed:', error);
            this.updateModelStatus('Failed to Load');
            throw error;
        }
    }

    /**
     * Start processing video frames
     */
    startProcessing() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        this.updateDetectionStage('Processing');
        console.log('▶️ Started frame processing');
        
        this.processLoop();
    }

    /**
     * Stop processing video frames
     */
    stopProcessing() {
        this.isProcessing = false;
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        this.updateDetectionStage('Idle');
        console.log('⏸️ Stopped frame processing');
    }

    /**
     * Main processing loop
     */
    async processLoop() {
        if (!this.isProcessing || this.video.paused || this.video.ended) {
            return;
        }
        
        // Limit frame rate to ~30fps for better visibility
        const now = performance.now();
        if (now - this.lastFrameTime < (1000 / this.frameRate)) {
            this.animationId = requestAnimationFrame(() => this.processLoop());
            return;
        }
        
        await this.processFrame();
        this.lastFrameTime = now;
        
        if (this.isProcessing) {
            this.animationId = requestAnimationFrame(() => this.processLoop());
        }
    }

    /**
     * Process single frame
     */
    async processFrame() {
        try {
            if (!this.video || this.video.paused) return;
            
            const startTime = performance.now();
            
            // Estimate poses
            const poses = await analysisCommon.estimatePoses(this.video);
            
            // Process pose data for running analysis
            const poseData = analysisCommon.processPoseData(poses);
            
            // Update current pose
            this.currentPose = poseData;
            
            // Add to history
            if (poseData.hasValidPose) {
                this.poseHistory.push({
                    timestamp: this.video.currentTime,
                    pose: poseData,
                    frameTime: performance.now() - startTime
                });
                
                // Keep only last 100 frames
                if (this.poseHistory.length > 100) {
                    this.poseHistory.shift();
                }
            }
            
            // Draw skeleton
            this.drawFrame(poseData);
            
            // Update UI
            this.updateAnalysisUI(poseData, performance.now() - startTime);
            
        } catch (error) {
            console.error('❌ Frame processing error:', error);
            this.updateDetectionStage('Error');
        }
    }

    /**
     * Process current frame manually
     */
    async processCurrentFrame() {
        if (!this.video) return;
        
        console.log('🔬 Processing current frame manually');
        this.updateDetectionStage('Processing Frame');
        
        try {
            await this.processFrame();
            this.updateDetectionStage('Complete');
            
            setTimeout(() => {
                this.updateDetectionStage('Idle');
            }, 2000);
            
        } catch (error) {
            console.error('❌ Manual frame processing failed:', error);
            this.updateDetectionStage('Error');
        }
    }

    /**
     * Draw frame with pose overlay
     */
    drawFrame(poseData) {
        if (!this.ctx || !this.canvas) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (!poseData.hasValidPose) return;
        
        // Draw skeleton with side view specific styling
        const drawOptions = {
            lineColor: '#00ff41',      // Bright green for high visibility
            pointColor = '#ff1744',    // Bright red for keypoints
            lineWidth: 2,
            pointRadius: 4,
            showConfidence: false
        };
        
        analysisCommon.drawSkeleton(this.canvas, poseData, drawOptions);
        
        // Draw side view specific indicators
        this.drawSideViewIndicators(poseData);
    }

    /**
     * Draw side view specific indicators
     */
    drawSideViewIndicators(poseData) {
        const ctx = this.ctx;
        if (!ctx) return;
        
        // Draw facing direction
        ctx.save();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = 'bold 14px Arial';
        
        const direction = poseData.facingDirection;
        const text = direction === 'facing-left' ? '← Left' : 
                    direction === 'facing-right' ? '→ Right' : '?';
        
        ctx.fillText(`Facing: ${text}`, 10, 25);
        
        // Draw confidence indicator
        const confidence = Math.round(poseData.confidence * 100);
        const confidenceColor = confidence > 80 ? '#00ff41' : 
                               confidence > 60 ? '#ffeb3b' : '#ff5722';
        
        ctx.fillStyle = confidenceColor;
        ctx.fillText(`Confidence: ${confidence}%`, 10, 45);
        
        ctx.restore();
    }

    /**
     * Reset video to beginning
     */
    resetVideo() {
        if (!this.video) return;
        
        this.stopProcessing();
        this.video.currentTime = 0;
        
        // Clear canvas
        if (this.ctx) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Reset state
        this.currentPose = null;
        this.poseHistory = [];
        
        // Reset UI
        this.updateDetectionStage('Idle');
        this.updatePersonDetected('No');
        this.updateConfidence('0%');
        
        console.log('↻ Video reset');
    }

    /**
     * Update analysis UI with current data
     */
    updateAnalysisUI(poseData, processingTime) {
        // Update detection metrics
        this.updatePersonDetected(poseData.hasValidPose ? 'Yes' : 'No');
        this.updateConfidence(Math.round(poseData.confidence * 100) + '%');
        
        // Update debug output
        this.updateDebugOutput(poseData, processingTime);
    }

    /**
     * Update model status display
     */
    updateModelStatus(status) {
        const element = document.getElementById('modelStatus');
        if (element) element.textContent = status;
    }

    /**
     * Update detection stage display
     */
    updateDetectionStage(stage) {
        const element = document.getElementById('detectionStage');
        if (element) element.textContent = stage;
    }

    /**
     * Update person detected display
     */
    updatePersonDetected(detected) {
        const element = document.getElementById('personDetected');
        if (element) element.textContent = detected;
    }

    /**
     * Update confidence display
     */
    updateConfidence(confidence) {
        const element = document.getElementById('confidence');
        if (element) {
            element.textContent = confidence;
            
            // Color code confidence
            const value = parseInt(confidence);
            if (value > 80) {
                element.className = 'metric-value success';
            } else if (value > 60) {
                element.className = 'metric-value warning';
            } else {
                element.className = 'metric-value error';
            }
        }
    }

    /**
     * Update debug output
     */
    updateDebugOutput(poseData, processingTime) {
        const element = document.getElementById('debugOutput');
        if (!element) return;
        
        const debugInfo = {
            timestamp: new Date().toLocaleTimeString(),
            videoTime: this.video?.currentTime?.toFixed(2) || 0,
            processingTime: processingTime.toFixed(1) + 'ms',
            facingDirection: poseData.facingDirection,
            keypointsDetected: poseData.keypoints.length,
            confidence: (poseData.confidence * 100).toFixed(1) + '%'
        };
        
        element.textContent = JSON.stringify(debugInfo, null, 2);
    }

    /**
     * Update general UI elements
     */
    updateUI() {
        // Enable/disable controls based on video state
        const hasVideo = !!this.video?.src;
        
        const playBtn = document.getElementById('playBtn');
        const pauseBtn = document.getElementById('pauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        const processBtn = document.getElementById('processBtn');
        
        [playBtn, pauseBtn, resetBtn, processBtn].forEach(btn => {
            if (btn) {
                btn.disabled = !hasVideo;
            }
        });
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ Side View Error:', message);
        
        const debugOutput = document.getElementById('debugOutput');
        if (debugOutput) {
            debugOutput.textContent = `Error: ${message}`;
        }
    }

    /**
     * Get analysis results
     */
    getAnalysisResults() {
        return {
            currentPose: this.currentPose,
            poseHistory: this.poseHistory,
            runnerData: this.runnerData,
            performance: analysisCommon.getPerformanceMetrics()
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        console.log('🧹 Cleaning up side view analysis...');
        
        this.stopProcessing();
        
        if (this.video?.src) {
            URL.revokeObjectURL(this.video.src);
        }
        
        // Clear canvas
        if (this.ctx && this.canvas) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Reset state
        this.currentPose = null;
        this.poseHistory = [];
        this.runnerData = null;
    }
}

// Initialize side view analysis
const sideViewAnalysis = new SideViewAnalysis();