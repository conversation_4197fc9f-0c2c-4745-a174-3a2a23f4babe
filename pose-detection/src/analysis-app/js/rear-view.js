/**
 * MaxWattz - Rear View Analysis
 * Handles rear view running analysis focused on symmetry and gait patterns
 */

class RearViewAnalysis {
    constructor() {
        this.video = null;
        this.canvas = null;
        this.ctx = null;
        this.isProcessing = false;
        this.animationId = null;
        this.lastFrameTime = 0;
        this.frameRate = 30; // Target 30fps
        
        // Analysis state
        this.currentPose = null;
        this.poseHistory = [];
        this.runnerData = null;
        
        // Rear view specific metrics
        this.symmetryAnalysis = {
            leftRightBalance: 0,
            shoulderLevel: 0,
            hipLevel: 0,
            footStrike: 'neutral'
        };
        
        this.init();
    }

    /**
     * Initialize rear view analysis
     */
    init() {
        // Wait for page to load
        document.addEventListener('DOMContentLoaded', () => {
            this.setupEventListeners();
        });
        
        // Also check if DOM is already ready
        if (document.readyState !== 'loading') {
            this.setupEventListeners();
        }
    }

    /**
     * Setup event listeners for rear view analysis
     */
    setupEventListeners() {
        // Listen for navigation to rear analysis page
        window.addEventListener('appNavigate', (e) => {
            if (e.detail.page === 'rear-analysis') {
                this.initializePage();
            }
        });

        // Listen for file upload completion
        window.addEventListener('fileUploaded', (e) => {
            if (e.detail.view === 'rear') {
                this.handleFileUpload(e.detail);
            }
        });
    }

    /**
     * Initialize rear analysis page
     */
    async initializePage() {
        console.log('👀 Initializing rear view analysis...');
        
        try {
            // Get DOM elements
            this.video = document.getElementById('rearAnalysisVideo');
            this.canvas = document.getElementById('rearAnalysisCanvas');
            
            if (!this.video || !this.canvas) {
                console.error('❌ Required DOM elements not found');
                return;
            }
            
            this.ctx = this.canvas.getContext('2d');
            
            // Setup video and canvas
            this.setupVideoCanvas();
            
            // Setup control handlers
            this.setupControlHandlers();
            
            // Load uploaded video if available
            await this.loadUploadedVideo();
            
            // Initialize analysis system
            await this.initializeAnalysis();
            
            console.log('✅ Rear view analysis initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize rear view analysis:', error);
            this.showError('Failed to initialize analysis. Please try again.');
        }
    }

    /**
     * Setup video and canvas
     */
    setupVideoCanvas() {
        // Video event listeners
        this.video.addEventListener('loadedmetadata', () => {
            this.updateCanvasSize();
            this.updateUI();
        });
        
        this.video.addEventListener('play', () => {
            this.startProcessing();
        });
        
        this.video.addEventListener('pause', () => {
            this.stopProcessing();
        });
        
        this.video.addEventListener('ended', () => {
            this.stopProcessing();
        });
        
        // Canvas setup
        this.canvas.style.position = 'absolute';
        this.canvas.style.top = '0';
        this.canvas.style.left = '0';
        this.canvas.style.pointerEvents = 'none';
    }

    /**
     * Update canvas size to match video
     */
    updateCanvasSize() {
        if (!this.video || !this.canvas) return;
        
        const rect = this.video.getBoundingClientRect();
        this.canvas.width = rect.width;
        this.canvas.height = rect.height;
        this.canvas.style.width = rect.width + 'px';
        this.canvas.style.height = rect.height + 'px';
        
        console.log(`📐 Canvas size: ${rect.width}x${rect.height}`);
    }

    /**
     * Setup control button handlers
     */
    setupControlHandlers() {
        const playBtn = document.getElementById('rearPlayBtn');
        const pauseBtn = document.getElementById('rearPauseBtn');
        const resetBtn = document.getElementById('rearResetBtn');
        const processBtn = document.getElementById('rearProcessBtn');
        
        if (playBtn) {
            playBtn.addEventListener('click', () => {
                this.video?.play();
            });
        }
        
        if (pauseBtn) {
            pauseBtn.addEventListener('click', () => {
                this.video?.pause();
            });
        }
        
        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetVideo();
            });
        }
        
        if (processBtn) {
            processBtn.addEventListener('click', () => {
                this.processCurrentFrame();
            });
        }
    }

    /**
     * Load uploaded video
     */
    async loadUploadedVideo() {
        if (!window.uploadedFile) {
            console.log('ℹ️ No uploaded file found');
            return;
        }
        
        const fileURL = URL.createObjectURL(window.uploadedFile);
        this.video.src = fileURL;
        
        // Store runner data
        this.runnerData = window.runnerData;
        
        console.log('📁 Video loaded:', window.uploadedFile.name);
        this.updateUI();
    }

    /**
     * Initialize analysis system
     */
    async initializeAnalysis() {
        try {
            // Ensure analysis common is initialized
            if (!analysisCommon.isModelLoaded) {
                this.updateModelStatus('Loading...');
                await analysisCommon.init();
            }
            
            // Initialize coordinate scaler if runner data available
            if (this.runnerData) {
                analysisCommon.initializeCoordinateScaler(this.runnerData);
            }
            
            this.updateModelStatus('Loaded & Verified (Lite - 33 keypoints)');
            
        } catch (error) {
            console.error('❌ Analysis initialization failed:', error);
            this.updateModelStatus('Failed to Load');
            throw error;
        }
    }

    /**
     * Start processing video frames
     */
    startProcessing() {
        if (this.isProcessing) return;
        
        this.isProcessing = true;
        console.log('▶️ Started rear view frame processing');
        
        this.processLoop();
    }

    /**
     * Stop processing video frames
     */
    stopProcessing() {
        this.isProcessing = false;
        
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        
        console.log('⏸️ Stopped rear view frame processing');
    }

    /**
     * Main processing loop
     */
    async processLoop() {
        if (!this.isProcessing || this.video.paused || this.video.ended) {
            return;
        }
        
        // Limit frame rate to ~30fps for better visibility
        const now = performance.now();
        if (now - this.lastFrameTime < (1000 / this.frameRate)) {
            this.animationId = requestAnimationFrame(() => this.processLoop());
            return;
        }
        
        await this.processFrame();
        this.lastFrameTime = now;
        
        if (this.isProcessing) {
            this.animationId = requestAnimationFrame(() => this.processLoop());
        }
    }

    /**
     * Process single frame with rear view analysis
     */
    async processFrame() {
        try {
            if (!this.video || this.video.paused) return;
            
            const startTime = performance.now();
            
            // Estimate poses
            const poses = await analysisCommon.estimatePoses(this.video);
            
            // Process pose data for running analysis
            const poseData = analysisCommon.processPoseData(poses);
            
            // Perform rear view specific analysis
            if (poseData.hasValidPose) {
                this.analyzeRearViewSymmetry(poseData);
            }
            
            // Update current pose
            this.currentPose = poseData;
            
            // Add to history
            if (poseData.hasValidPose) {
                this.poseHistory.push({
                    timestamp: this.video.currentTime,
                    pose: poseData,
                    symmetry: { ...this.symmetryAnalysis },
                    frameTime: performance.now() - startTime
                });
                
                // Keep only last 100 frames
                if (this.poseHistory.length > 100) {
                    this.poseHistory.shift();
                }
            }
            
            // Draw skeleton
            this.drawFrame(poseData);
            
            // Update UI
            this.updateAnalysisUI(poseData, performance.now() - startTime);
            
        } catch (error) {
            console.error('❌ Rear view frame processing error:', error);
        }
    }

    /**
     * Analyze rear view symmetry
     */
    analyzeRearViewSymmetry(poseData) {
        const keypoints = poseData.keypoints;
        
        // Find key body landmarks
        const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');
        const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');
        const leftHip = keypoints.find(kp => kp.name === 'left_hip');
        const rightHip = keypoints.find(kp => kp.name === 'right_hip');
        const leftAnkle = keypoints.find(kp => kp.name === 'left_ankle');
        const rightAnkle = keypoints.find(kp => kp.name === 'right_ankle');
        
        // Calculate shoulder level
        if (leftShoulder && rightShoulder) {
            this.symmetryAnalysis.shoulderLevel = Math.abs(leftShoulder.y - rightShoulder.y);
        }
        
        // Calculate hip level
        if (leftHip && rightHip) {
            this.symmetryAnalysis.hipLevel = Math.abs(leftHip.y - rightHip.y);
        }
        
        // Calculate left-right balance
        if (leftAnkle && rightAnkle && leftHip && rightHip) {
            const leftSide = Math.abs(leftAnkle.x - leftHip.x);
            const rightSide = Math.abs(rightAnkle.x - rightHip.x);
            this.symmetryAnalysis.leftRightBalance = Math.abs(leftSide - rightSide);
        }
        
        // Analyze foot strike pattern from rear view
        this.analyzeFootStrike(keypoints);
    }

    /**
     * Analyze foot strike pattern from rear view
     */
    analyzeFootStrike(keypoints) {
        const leftFoot = keypoints.find(kp => kp.name === 'left_foot_index');
        const rightFoot = keypoints.find(kp => kp.name === 'right_foot_index');
        const leftHeel = keypoints.find(kp => kp.name === 'left_heel');
        const rightHeel = keypoints.find(kp => kp.name === 'right_heel');
        
        if (leftFoot && leftHeel && rightFoot && rightHeel) {
            // Calculate angle of foot relative to ground plane
            const leftAngle = Math.atan2(leftFoot.y - leftHeel.y, leftFoot.x - leftHeel.x);
            const rightAngle = Math.atan2(rightFoot.y - rightHeel.y, rightFoot.x - rightHeel.x);
            
            const avgAngle = (leftAngle + rightAngle) / 2;
            
            // Classify foot strike
            if (avgAngle > 0.2) {
                this.symmetryAnalysis.footStrike = 'heel-strike';
            } else if (avgAngle < -0.2) {
                this.symmetryAnalysis.footStrike = 'forefoot-strike';
            } else {
                this.symmetryAnalysis.footStrike = 'midfoot-strike';
            }
        }
    }

    /**
     * Process current frame manually
     */
    async processCurrentFrame() {
        if (!this.video) return;
        
        console.log('🔬 Processing current rear view frame manually');
        
        try {
            await this.processFrame();
        } catch (error) {
            console.error('❌ Manual rear view frame processing failed:', error);
        }
    }

    /**
     * Draw frame with rear view pose overlay
     */
    drawFrame(poseData) {
        if (!this.ctx || !this.canvas) return;
        
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        if (!poseData.hasValidPose) return;
        
        // Draw skeleton with rear view specific styling
        const drawOptions = {
            lineColor: '#ff6b35',      // Orange for rear view
            pointColor: '#4ecdc4',     // Teal for keypoints
            lineWidth: 2,
            pointRadius: 4,
            showConfidence: false
        };
        
        analysisCommon.drawSkeleton(this.canvas, poseData, drawOptions);
        
        // Draw rear view specific indicators
        this.drawRearViewIndicators(poseData);
        
        // Draw symmetry analysis
        this.drawSymmetryIndicators();
    }

    /**
     * Draw rear view specific indicators
     */
    drawRearViewIndicators(poseData) {
        const ctx = this.ctx;
        if (!ctx) return;
        
        ctx.save();
        
        // Draw view indicator
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = 'bold 14px Arial';
        ctx.fillText('Rear View Analysis', 10, 25);
        
        // Draw confidence
        const confidence = Math.round(poseData.confidence * 100);
        const confidenceColor = confidence > 80 ? '#4ecdc4' : 
                               confidence > 60 ? '#ffe66d' : '#ff6b6b';
        
        ctx.fillStyle = confidenceColor;
        ctx.fillText(`Confidence: ${confidence}%`, 10, 45);
        
        ctx.restore();
    }

    /**
     * Draw symmetry analysis indicators
     */
    drawSymmetryIndicators() {
        const ctx = this.ctx;
        if (!ctx) return;
        
        ctx.save();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.font = '12px Arial';
        
        const startY = 70;
        const lineHeight = 18;
        
        // Shoulder level indicator
        const shoulderLevel = this.symmetryAnalysis.shoulderLevel;
        const shoulderColor = shoulderLevel < 10 ? '#4ecdc4' : shoulderLevel < 20 ? '#ffe66d' : '#ff6b6b';
        ctx.fillStyle = shoulderColor;
        ctx.fillText(`Shoulder Level: ${shoulderLevel.toFixed(1)}px`, 10, startY);
        
        // Hip level indicator
        const hipLevel = this.symmetryAnalysis.hipLevel;
        const hipColor = hipLevel < 10 ? '#4ecdc4' : hipLevel < 20 ? '#ffe66d' : '#ff6b6b';
        ctx.fillStyle = hipColor;
        ctx.fillText(`Hip Level: ${hipLevel.toFixed(1)}px`, 10, startY + lineHeight);
        
        // Foot strike pattern
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        ctx.fillText(`Foot Strike: ${this.symmetryAnalysis.footStrike}`, 10, startY + lineHeight * 2);
        
        ctx.restore();
    }

    /**
     * Reset video to beginning
     */
    resetVideo() {
        if (!this.video) return;
        
        this.stopProcessing();
        this.video.currentTime = 0;
        
        // Clear canvas
        if (this.ctx) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Reset state
        this.currentPose = null;
        this.poseHistory = [];
        this.symmetryAnalysis = {
            leftRightBalance: 0,
            shoulderLevel: 0,
            hipLevel: 0,
            footStrike: 'neutral'
        };
        
        // Reset UI
        this.updateSymmetryScore('N/A');
        
        console.log('↻ Rear view video reset');
    }

    /**
     * Update analysis UI with current data
     */
    updateAnalysisUI(poseData, processingTime) {
        // Calculate overall symmetry score
        const symmetryScore = this.calculateSymmetryScore();
        this.updateSymmetryScore(symmetryScore + '%');
        
        // Update debug output
        this.updateDebugOutput(poseData, processingTime);
    }

    /**
     * Calculate overall symmetry score
     */
    calculateSymmetryScore() {
        const shoulderScore = Math.max(0, 100 - (this.symmetryAnalysis.shoulderLevel * 2));
        const hipScore = Math.max(0, 100 - (this.symmetryAnalysis.hipLevel * 2));
        const balanceScore = Math.max(0, 100 - (this.symmetryAnalysis.leftRightBalance * 1.5));
        
        return Math.round((shoulderScore + hipScore + balanceScore) / 3);
    }

    /**
     * Update model status display
     */
    updateModelStatus(status) {
        const element = document.getElementById('rearModelStatus');
        if (element) element.textContent = status;
    }

    /**
     * Update symmetry score display
     */
    updateSymmetryScore(score) {
        const element = document.getElementById('symmetryScore');
        if (element) {
            element.textContent = score;
            
            // Color code symmetry score
            const value = parseInt(score);
            if (value > 80) {
                element.className = 'metric-value success';
            } else if (value > 60) {
                element.className = 'metric-value warning';
            } else {
                element.className = 'metric-value error';
            }
        }
    }

    /**
     * Update debug output
     */
    updateDebugOutput(poseData, processingTime) {
        const element = document.getElementById('rearDebugOutput');
        if (!element) return;
        
        const debugInfo = {
            timestamp: new Date().toLocaleTimeString(),
            videoTime: this.video?.currentTime?.toFixed(2) || 0,
            processingTime: processingTime.toFixed(1) + 'ms',
            confidence: (poseData.confidence * 100).toFixed(1) + '%',
            symmetryAnalysis: this.symmetryAnalysis,
            symmetryScore: this.calculateSymmetryScore() + '%'
        };
        
        element.textContent = JSON.stringify(debugInfo, null, 2);
    }

    /**
     * Update general UI elements
     */
    updateUI() {
        // Enable/disable controls based on video state
        const hasVideo = !!this.video?.src;
        
        const playBtn = document.getElementById('rearPlayBtn');
        const pauseBtn = document.getElementById('rearPauseBtn');
        const resetBtn = document.getElementById('rearResetBtn');
        const processBtn = document.getElementById('rearProcessBtn');
        
        [playBtn, pauseBtn, resetBtn, processBtn].forEach(btn => {
            if (btn) {
                btn.disabled = !hasVideo;
            }
        });
    }

    /**
     * Show error message
     */
    showError(message) {
        console.error('❌ Rear View Error:', message);
        
        const debugOutput = document.getElementById('rearDebugOutput');
        if (debugOutput) {
            debugOutput.textContent = `Error: ${message}`;
        }
    }

    /**
     * Get analysis results
     */
    getAnalysisResults() {
        return {
            currentPose: this.currentPose,
            poseHistory: this.poseHistory,
            runnerData: this.runnerData,
            symmetryAnalysis: this.symmetryAnalysis,
            symmetryScore: this.calculateSymmetryScore(),
            performance: analysisCommon.getPerformanceMetrics()
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        console.log('🧹 Cleaning up rear view analysis...');
        
        this.stopProcessing();
        
        if (this.video?.src) {
            URL.revokeObjectURL(this.video.src);
        }
        
        // Clear canvas
        if (this.ctx && this.canvas) {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
        
        // Reset state
        this.currentPose = null;
        this.poseHistory = [];
        this.runnerData = null;
        this.symmetryAnalysis = {
            leftRightBalance: 0,
            shoulderLevel: 0,
            hipLevel: 0,
            footStrike: 'neutral'
        };
    }
}

// Initialize rear view analysis
const rearViewAnalysis = new RearViewAnalysis();