/**
 * MaxWattz - Analysis Common Utilities
 * Shared functions for pose detection and analysis
 */

class AnalysisCommon {
    constructor() {
        this.detector = null;
        this.isModelLoaded = false;
        this.coordinateScaler = null;
        
        // Performance tracking
        this.performanceMetrics = {
            detectionTimes: [],
            avgFPS: 0,
            totalFrames: 0
        };
        
        // Keypoints to skip for running analysis (eyes, mouth, fingers)
        this.skipKeypoints = [1, 2, 3, 4, 5, 6, 9, 10, 17, 18, 19, 20, 21, 22];
        
        this.init();
    }

    /**
     * Initialize analysis system
     */
    async init() {
        try {
            console.log('🧠 Initializing BlazePose analysis system...');
            await this.initializeDetector();
            console.log('✅ Analysis system ready');
        } catch (error) {
            console.error('❌ Failed to initialize analysis system:', error);
            throw error;
        }
    }

    /**
     * Initialize BlazePose detector
     */
    async initializeDetector() {
        if (this.isModelLoaded) {
            console.log('✅ BlazePose model already loaded');
            return this.detector;
        }

        console.log('📥 Loading BlazePose model...');
        
        const modelConfig = {
            runtime: 'tfjs',
            modelType: 'full', // Note: CDN might serve Lite model
            enableSmoothing: true,
            enableSegmentation: false
        };

        try {
            this.detector = await poseDetection.createDetector(
                poseDetection.SupportedModels.BlazePose,
                modelConfig
            );
            
            this.isModelLoaded = true;
            console.log('✅ BlazePose model loaded successfully');
            
            // Run test detection to verify model
            await this.testDetector();
            
            return this.detector;
            
        } catch (error) {
            console.error('❌ Failed to load BlazePose model:', error);
            throw new Error(`Model loading failed: ${error.message}`);
        }
    }

    /**
     * Test detector with empty canvas
     */
    async testDetector() {
        const testCanvas = document.createElement('canvas');
        testCanvas.width = 100;
        testCanvas.height = 100;
        
        try {
            const poses = await this.detector.estimatePoses(testCanvas);
            console.log('🧪 Detector test passed - ready for analysis');
        } catch (error) {
            console.warn('⚠️ Detector test failed:', error);
        }
    }

    /**
     * Initialize coordinate scaler with runner data
     */
    initializeCoordinateScaler(runnerData) {
        if (!runnerData.heightInches) {
            console.warn('⚠️ No height data provided - scaling disabled');
            return;
        }

        this.coordinateScaler = new CoordinateScaler(runnerData.heightInches);
        console.log('📏 Coordinate scaler initialized with height:', runnerData.height, `(${runnerData.heightInches} inches)`);
    }

    /**
     * Estimate poses from video frame
     */
    async estimatePoses(videoElement, options = {}) {
        if (!this.isModelLoaded) {
            throw new Error('BlazePose model not loaded');
        }

        const startTime = performance.now();

        try {
            const estimationConfig = {
                maxPoses: 1,
                flipHorizontal: false,
                ...options
            };

            const poses = await this.detector.estimatePoses(videoElement, estimationConfig);
            
            // Track performance
            const detectionTime = performance.now() - startTime;
            this.updatePerformanceMetrics(detectionTime);
            
            return poses;
            
        } catch (error) {
            console.error('❌ Pose estimation failed:', error);
            throw error;
        }
    }

    /**
     * Process pose data for running analysis
     */
    processPoseData(poses, facingDirection = null) {
        if (!poses || poses.length === 0) {
            return {
                hasValidPose: false,
                keypoints: [],
                confidence: 0,
                facingDirection: null,
                scaledKeypoints: []
            };
        }

        const pose = poses[0];
        const filteredKeypoints = this.filterKeypoints(pose.keypoints);
        const confidence = this.calculateAverageConfidence(filteredKeypoints);
        
        // Determine facing direction if not provided
        const direction = facingDirection || this.determineFacingDirection(pose.keypoints);
        
        // Scale coordinates if scaler is available
        const scaledKeypoints = this.coordinateScaler ? 
            this.coordinateScaler.scaleKeypoints(filteredKeypoints) : 
            filteredKeypoints;

        return {
            hasValidPose: confidence > 0.5,
            keypoints: filteredKeypoints,
            confidence: confidence,
            facingDirection: direction,
            scaledKeypoints: scaledKeypoints,
            rawPose: pose
        };
    }

    /**
     * Filter keypoints for running analysis
     */
    filterKeypoints(keypoints) {
        return keypoints.filter((_, index) => !this.skipKeypoints.includes(index));
    }

    /**
     * Calculate average confidence score
     */
    calculateAverageConfidence(keypoints) {
        if (keypoints.length === 0) return 0;
        
        const totalScore = keypoints.reduce((sum, kp) => sum + (kp.score || 0), 0);
        return totalScore / keypoints.length;
    }

    /**
     * Determine facing direction using foot positions
     */
    determineFacingDirection(keypoints) {
        try {
            // Use ankle positions to determine facing direction
            const leftAnkle = keypoints[27]; // left_ankle
            const rightAnkle = keypoints[28]; // right_ankle
            const leftHeel = keypoints[29]; // left_heel  
            const rightHeel = keypoints[30]; // right_heel
            
            if (!leftAnkle || !rightAnkle || !leftHeel || !rightHeel) {
                return 'unknown';
            }

            // Calculate which foot is closer to camera (higher z-value in BlazePose)
            const leftFootZ = (leftAnkle.z || 0) + (leftHeel.z || 0);
            const rightFootZ = (rightAnkle.z || 0) + (rightHeel.z || 0);
            
            // Higher z-value means closer to camera
            if (leftFootZ > rightFootZ) {
                return 'facing-right'; // Left foot closer = facing right
            } else {
                return 'facing-left'; // Right foot closer = facing left
            }
            
        } catch (error) {
            console.warn('⚠️ Failed to determine facing direction:', error);
            return 'unknown';
        }
    }

    /**
     * Draw skeleton on canvas
     */
    drawSkeleton(canvas, poseData, options = {}) {
        const ctx = canvas.getContext('2d');
        if (!ctx || !poseData.hasValidPose) return;

        const {
            lineColor = '#00ff00',
            pointColor = '#ff0000',
            lineWidth = 2,
            pointRadius = 3,
            showConfidence = false
        } = options;

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        const keypoints = poseData.keypoints;
        
        // Draw connections
        this.drawConnections(ctx, keypoints, lineColor, lineWidth);
        
        // Draw keypoints
        this.drawKeypoints(ctx, keypoints, pointColor, pointRadius, showConfidence);
        
        // Draw facing direction indicator
        this.drawFacingIndicator(ctx, poseData.facingDirection, canvas.width, canvas.height);
    }

    /**
     * Draw skeleton connections
     */
    drawConnections(ctx, keypoints, color, lineWidth) {
        ctx.strokeStyle = color;
        ctx.lineWidth = lineWidth;

        // Define connections for filtered keypoints (running analysis focus)
        const connections = [
            // Torso
            [11, 12], // left_shoulder - right_shoulder
            [11, 23], // left_shoulder - left_hip  
            [12, 24], // right_shoulder - right_hip
            [23, 24], // left_hip - right_hip
            
            // Left arm
            [11, 13], // left_shoulder - left_elbow
            [13, 15], // left_elbow - left_wrist
            
            // Right arm  
            [12, 14], // right_shoulder - right_elbow
            [14, 16], // right_elbow - right_wrist
            
            // Left leg
            [23, 25], // left_hip - left_knee
            [25, 27], // left_knee - left_ankle
            [27, 29], // left_ankle - left_heel
            [27, 31], // left_ankle - left_foot_index
            
            // Right leg
            [24, 26], // right_hip - right_knee  
            [26, 28], // right_knee - right_ankle
            [28, 30], // right_ankle - right_heel
            [28, 32]  // right_ankle - right_foot_index
        ];

        connections.forEach(([startIdx, endIdx]) => {
            const startPoint = keypoints[startIdx];
            const endPoint = keypoints[endIdx];
            
            if (startPoint && endPoint && 
                startPoint.score > 0.5 && endPoint.score > 0.5) {
                
                ctx.beginPath();
                ctx.moveTo(startPoint.x, startPoint.y);
                ctx.lineTo(endPoint.x, endPoint.y);
                ctx.stroke();
            }
        });
    }

    /**
     * Draw keypoints
     */
    drawKeypoints(ctx, keypoints, color, radius, showConfidence) {
        ctx.fillStyle = color;

        keypoints.forEach((point, index) => {
            if (point && point.score > 0.5) {
                ctx.beginPath();
                ctx.arc(point.x, point.y, radius, 0, 2 * Math.PI);
                ctx.fill();
                
                if (showConfidence) {
                    ctx.fillStyle = 'white';
                    ctx.font = '10px Arial';
                    ctx.fillText(
                        Math.round(point.score * 100) + '%', 
                        point.x + 5, 
                        point.y - 5
                    );
                    ctx.fillStyle = color;
                }
            }
        });
    }

    /**
     * Draw facing direction indicator
     */
    drawFacingIndicator(ctx, direction, canvasWidth, canvasHeight) {
        if (direction === 'unknown') return;

        ctx.save();
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.font = 'bold 16px Arial';
        
        const text = direction === 'facing-left' ? '← Facing Left' : '→ Facing Right';
        const metrics = ctx.measureText(text);
        
        // Position in top-right corner
        const x = canvasWidth - metrics.width - 10;
        const y = 25;
        
        ctx.fillText(text, x, y);
        ctx.restore();
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics(detectionTime) {
        this.performanceMetrics.detectionTimes.push(detectionTime);
        this.performanceMetrics.totalFrames++;
        
        // Keep only last 30 measurements for rolling average
        if (this.performanceMetrics.detectionTimes.length > 30) {
            this.performanceMetrics.detectionTimes.shift();
        }
        
        // Calculate average FPS
        const avgTime = this.performanceMetrics.detectionTimes.reduce((a, b) => a + b, 0) / 
                        this.performanceMetrics.detectionTimes.length;
        this.performanceMetrics.avgFPS = 1000 / avgTime;
    }

    /**
     * Get current performance metrics
     */
    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            currentFPS: this.performanceMetrics.avgFPS,
            totalFrames: this.performanceMetrics.totalFrames
        };
    }

    /**
     * Reset performance metrics
     */
    resetPerformanceMetrics() {
        this.performanceMetrics = {
            detectionTimes: [],
            avgFPS: 0,
            totalFrames: 0
        };
    }

    /**
     * Get model info
     */
    getModelInfo() {
        return {
            isLoaded: this.isModelLoaded,
            modelType: 'BlazePose',
            runtime: 'tfjs',
            backend: tf?.getBackend() || 'unknown',
            memory: tf?.memory() || null
        };
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        console.log('🧹 Cleaning up analysis resources...');
        
        if (this.detector) {
            // BlazePose detector cleanup if available
            if (typeof this.detector.dispose === 'function') {
                this.detector.dispose();
            }
        }
        
        this.resetPerformanceMetrics();
        this.isModelLoaded = false;
        this.detector = null;
    }
}

/**
 * Coordinate Scaler - Converts pixel coordinates to real-world measurements
 */
class CoordinateScaler {
    constructor(heightInches) {
        this.userHeight = typeof heightInches === 'number' ? heightInches : this.parseHeight(heightInches);
        this.pixelToRealRatio = null;
        this.isCalibrated = false;
        
        console.log(`📏 CoordinateScaler initialized with height: ${this.userHeight} inches`);
    }

    /**
     * Parse height string to inches
     */
    parseHeight(heightString) {
        const str = heightString.trim().toLowerCase();
        
        // Handle feet'inches" format (5'10", 5'10, etc.)
        const feetInchesMatch = str.match(/(\d+)'(\d+)"?/);
        if (feetInchesMatch) {
            const feet = parseInt(feetInchesMatch[1]);
            const inches = parseInt(feetInchesMatch[2]);
            return feet * 12 + inches;
        }
        
        // Handle cm format
        const cmMatch = str.match(/(\d+)cm/);
        if (cmMatch) {
            const cm = parseInt(cmMatch[1]);
            return cm * 0.393701; // Convert cm to inches
        }
        
        // Handle decimal feet format (5.8ft)
        const decimalFeetMatch = str.match(/(\d+\.?\d*)ft/);
        if (decimalFeetMatch) {
            const feet = parseFloat(decimalFeetMatch[1]);
            return feet * 12;
        }
        
        // Default assumption: already in inches
        const numericValue = parseFloat(str);
        return isNaN(numericValue) ? 70 : numericValue; // Default to 5'10" if parsing fails
    }

    /**
     * Calibrate using pose data
     */
    calibrate(keypoints) {
        try {
            // Use nose to hip distance as reference (approximately 40% of height)
            const nose = keypoints.find(kp => kp.name === 'nose');
            const leftHip = keypoints.find(kp => kp.name === 'left_hip');
            const rightHip = keypoints.find(kp => kp.name === 'right_hip');
            
            if (!nose || !leftHip || !rightHip) {
                console.warn('⚠️ Required keypoints not found for calibration');
                return false;
            }
            
            // Calculate hip center
            const hipCenterX = (leftHip.x + rightHip.x) / 2;
            const hipCenterY = (leftHip.y + rightHip.y) / 2;
            
            // Calculate nose to hip distance in pixels
            const pixelDistance = Math.sqrt(
                Math.pow(nose.x - hipCenterX, 2) + 
                Math.pow(nose.y - hipCenterY, 2)
            );
            
            // Nose to hip is approximately 40% of total height
            const referenceRealDistance = this.userHeight * 0.4;
            
            this.pixelToRealRatio = referenceRealDistance / pixelDistance;
            this.isCalibrated = true;
            
            console.log(`✅ Calibration complete - Ratio: ${this.pixelToRealRatio.toFixed(4)} inches/pixel`);
            return true;
            
        } catch (error) {
            console.error('❌ Calibration failed:', error);
            return false;
        }
    }

    /**
     * Scale keypoints to real-world coordinates
     */
    scaleKeypoints(keypoints) {
        if (!this.isCalibrated) {
            // Attempt calibration
            if (!this.calibrate(keypoints)) {
                return keypoints; // Return original if calibration fails
            }
        }
        
        return keypoints.map(kp => ({
            ...kp,
            realX: kp.x * this.pixelToRealRatio,
            realY: kp.y * this.pixelToRealRatio,
            realZ: (kp.z || 0) * this.pixelToRealRatio
        }));
    }

    /**
     * Convert pixel distance to real distance
     */
    pixelsToInches(pixels) {
        if (!this.isCalibrated) return null;
        return pixels * this.pixelToRealRatio;
    }

    /**
     * Get calibration status
     */
    getCalibrationInfo() {
        return {
            isCalibrated: this.isCalibrated,
            userHeight: this.userHeight,
            ratio: this.pixelToRealRatio
        };
    }
}

// Create global analysis instance
const analysisCommon = new AnalysisCommon();