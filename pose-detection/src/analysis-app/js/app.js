/**
 * MaxWattz - Main Application Controller
 * Handles app initialization and global state management
 */

import { ENV, validateEnv } from '../config/env.js';

class MaxWattzApp {
    constructor() {
        this.currentPage = 'insights';
        this.isInitialized = false;
        this.tfBackend = null;
        this.sidebarCollapsed = false;
        this.currentTheme = localStorage.getItem('theme') || 'light';
        
        // Initialize app when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    /**
     * Initialize the application
     */
    async init() {
        try {
            console.log('🚀 Initializing MaxWattz App...');
            
            // Validate environment variables
            if (!validateEnv()) {
                throw new Error('Missing required environment variables');
            }
            
            // Apply saved theme
            this.applyTheme(this.currentTheme);
            
            // Initialize TensorFlow.js backend
            await this.initTensorFlow();
            
            // Initialize navigation
            this.initNavigation();
            
            // Initialize page content
            this.initPages();
            
            // Initialize sidebar and theme controls
            this.initUIControls();
            
            // Initialize global event listeners
            this.initEventListeners();
            
            // Initialize form controls
            this.initFormControls();
            
            // Initialize upload workflow
            await this.initUploadWorkflow();
            
            this.isInitialized = true;
            console.log('✅ MaxWattz App initialized successfully');
            
            // Show welcome message
            this.showWelcomeMessage();
            
        } catch (error) {
            console.error('❌ Failed to initialize MaxWattz App:', error);
            this.showErrorMessage('Failed to initialize application. Please refresh the page.');
        }
    }

    /**
     * Initialize TensorFlow.js backend
     */
    async initTensorFlow() {
        console.log('⚙️ Initializing TensorFlow.js...');
        
        try {
            // Set backend to WebGL for optimal performance
            await tf.setBackend('webgl');
            this.tfBackend = tf.getBackend();
            
            console.log(`✅ TensorFlow.js backend: ${this.tfBackend}`);
            console.log(`📊 Memory info:`, tf.memory());
            
        } catch (error) {
            console.warn('⚠️ WebGL backend failed, falling back to CPU:', error);
            await tf.setBackend('cpu');
            this.tfBackend = tf.getBackend();
        }
    }

    /**
     * Initialize navigation system
     */
    initNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                const page = item.dataset.page;
                if (page) {
                    this.navigateToPage(page);
                }
            });
        });

        // Initialize new analysis button
        const newAnalysisBtn = document.getElementById('newAnalysisBtn');
        if (newAnalysisBtn) {
            newAnalysisBtn.addEventListener('click', () => {
                this.navigateToPage('upload');
            });
        }
    }

    /**
     * Initialize page content
     */
    initPages() {
        // Load upload page content by default
        this.loadUploadPage();
        
        // Set initial page
        this.showPage('uploadPage');
    }

    /**
     * Initialize UI controls (sidebar toggle, theme switcher)
     */
    initUIControls() {
        // Sidebar toggle button
        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleSidebar();
            });
        }

        // Click on collapsed sidebar to expand
        const sidebar = document.querySelector('.sidebar');
        if (sidebar) {
            sidebar.addEventListener('click', (e) => {
                if (this.sidebarCollapsed && !e.target.closest('.sidebar-toggle')) {
                    this.toggleSidebar();
                }
            });
        }

        // Theme toggle
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
        }

        // Update theme label
        this.updateThemeLabel();

        // Load saved sidebar state
        const savedSidebarState = localStorage.getItem('sidebarCollapsed');
        if (savedSidebarState === 'true') {
            this.sidebarCollapsed = true;
            sidebar?.classList.add('collapsed');
        }
    }

    /**
     * Initialize form controls
     */
    initFormControls() {
        // Height system toggle
        document.addEventListener('change', (e) => {
            if (e.target.id === 'heightSystem') {
                this.toggleHeightSystem(e.target.value);
            }
        });
    }

    /**
     * Toggle sidebar collapsed state
     */
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        if (!sidebar) return;

        this.sidebarCollapsed = !this.sidebarCollapsed;
        
        if (this.sidebarCollapsed) {
            sidebar.classList.add('collapsed');
        } else {
            sidebar.classList.remove('collapsed');
        }

        // Save state
        localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
        console.log(`📱 Sidebar ${this.sidebarCollapsed ? 'collapsed' : 'expanded'}`);
    }

    /**
     * Toggle theme between light and dark
     */
    toggleTheme() {
        this.currentTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(this.currentTheme);
        this.updateThemeLabel();
        
        // Save theme preference
        localStorage.setItem('theme', this.currentTheme);
        console.log(`🎨 Theme switched to: ${this.currentTheme}`);
    }

    /**
     * Apply theme to document
     */
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
    }

    /**
     * Update theme label text
     */
    updateThemeLabel() {
        const themeLabel = document.querySelector('.theme-label');
        if (themeLabel) {
            themeLabel.textContent = this.currentTheme === 'light' ? 'Light' : 'Dark';
        }
    }

    /**
     * Toggle height input system between imperial and metric
     */
    toggleHeightSystem(system) {
        const imperialHeight = document.querySelector('.imperial-height');
        const metricHeight = document.querySelector('.metric-height');

        if (system === 'metric') {
            if (imperialHeight) imperialHeight.style.display = 'none';
            if (metricHeight) metricHeight.style.display = 'flex';
        } else {
            if (imperialHeight) imperialHeight.style.display = 'flex';
            if (metricHeight) metricHeight.style.display = 'none';
        }

        console.log(`📏 Height system switched to: ${system}`);
    }

    /**
     * Initialize global event listeners
     */
    initEventListeners() {
        // Handle window resize
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));

        // Handle visibility change (for performance optimization)
        document.addEventListener('visibilitychange', () => {
            this.handleVisibilityChange();
        });

        // Handle before unload (cleanup)
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * Navigate to a specific page
     */
    navigateToPage(pageId) {
        console.log(`🧭 Navigating to: ${pageId}`);
        
        // Update navigation active state
        this.updateNavigation(pageId);
        
        // Update header title
        this.updatePageHeader(pageId);
        
        // Show appropriate page
        switch (pageId) {
            case 'insights':
                this.showPage('uploadPage'); // Default to upload for now
                break;
            case 'dashboard':
                this.showPage('uploadPage'); // Placeholder
                break;
            case 'biometrics':
                this.showPage('uploadPage'); // Placeholder
                break;
            case 'calendar':
                this.showPage('uploadPage'); // Placeholder
                break;
            case 'upload':
                this.showPage('uploadPage');
                break;
            case 'side-analysis':
                this.showPage('sideAnalysisPage');
                break;
            case 'rear-analysis':
                this.showPage('rearAnalysisPage');
                break;
            default:
                this.showPage('uploadPage');
        }
        
        this.currentPage = pageId;
    }

    /**
     * Update navigation active state
     */
    updateNavigation(activePageId) {
        const navItems = document.querySelectorAll('.nav-item');
        
        navItems.forEach(item => {
            if (item.dataset.page === activePageId) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }

    /**
     * Update page header based on current page
     */
    updatePageHeader(pageId) {
        const headerTitle = document.querySelector('.header-content h1');
        const headerSubtitle = document.querySelector('.subtitle');
        
        const pageConfig = {
            'insights': {
                title: "Max's Insights",
                subtitle: "AI-Powered Running Analysis"
            },
            'dashboard': {
                title: "Dashboard",
                subtitle: "Performance Overview"
            },
            'biometrics': {
                title: "BioMetrics",
                subtitle: "Biomechanical Analysis"
            },
            'calendar': {
                title: "Calendar",
                subtitle: "Training Schedule"
            },
            'upload': {
                title: "New Analysis",
                subtitle: "Upload Running Video"
            },
            'side-analysis': {
                title: "Side View Analysis",
                subtitle: "Processing Running Form"
            },
            'rear-analysis': {
                title: "Rear View Analysis",
                subtitle: "Processing Running Form"
            }
        };

        const config = pageConfig[pageId] || pageConfig['insights'];
        
        if (headerTitle) headerTitle.textContent = config.title;
        if (headerSubtitle) headerSubtitle.textContent = config.subtitle;
    }

    /**
     * Show specific page and hide others
     */
    showPage(pageId) {
        const pages = document.querySelectorAll('.page');
        
        pages.forEach(page => {
            if (page.id === pageId) {
                page.classList.add('active');
                page.hidden = false;
                page.classList.add('fade-in');
            } else {
                page.classList.remove('active');
                page.hidden = true;
                page.classList.remove('fade-in');
            }
        });
    }

    /**
     * Load upload page content
     */
    loadUploadPage() {
        const uploadPage = document.getElementById('uploadPage');
        if (!uploadPage) return;

        uploadPage.innerHTML = `
            <div class="upload-container">
                <div class="upload-header">
                    <h2>Upload Running Video</h2>
                    <p>Select your view type and upload a video for AI-powered biomechanical analysis</p>
                </div>
                
                
                <div class="upload-area" id="uploadArea">
                    <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <div class="upload-text">
                        <h3>Drop video file here or click to select</h3>
                        <p>Supports MP4, MOV, AVI, WebM</p>
                        <p class="file-types">Recommended: 1080p or higher, 30fps</p>
                    </div>
                    <input type="file" id="videoInput" accept="video/*" style="display: none;">
                </div>
                
                <div class="runner-info">
                    <h3>Runner Information</h3>
                    <div class="form-row">
                        <div class="form-group height-group">
                            <label for="heightSystem">Height</label>
                            <div class="height-inputs">
                                <select id="heightSystem" class="form-select height-system">
                                    <option value="imperial">Ft/In</option>
                                    <option value="metric">M/Cm</option>
                                </select>
                                <div class="height-values imperial-height">
                                    <select id="heightFeet" class="form-select">
                                        <option value="">Ft</option>
                                        <option value="4">4</option>
                                        <option value="5">5</option>
                                        <option value="6">6</option>
                                        <option value="7">7</option>
                                    </select>
                                    <select id="heightInches" class="form-select">
                                        <option value="">In</option>
                                        ${Array.from({length: 12}, (_, i) => `<option value="${i}">${i}</option>`).join('')}
                                    </select>
                                </div>
                                <div class="height-values metric-height" style="display: none;">
                                    <select id="heightMeters" class="form-select">
                                        <option value="">M</option>
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                    </select>
                                    <select id="heightCentimeters" class="form-select">
                                        <option value="">Cm</option>
                                        ${Array.from({length: 100}, (_, i) => `<option value="${i}">${i}</option>`).join('')}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group gender-group">
                            <label for="gender">Gender</label>
                            <select id="gender" class="form-select">
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                            </select>
                        </div>
                        <div class="form-group weight-group">
                            <label for="weight">Weight</label>
                            <div class="weight-inputs">
                                <input type="number" id="weight" class="form-input weight-value" placeholder="150" min="30" max="500">
                                <select id="weightUnit" class="form-select weight-unit">
                                    <option value="lbs">lbs</option>
                                    <option value="kg">kg</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Handle window resize
     */
    handleResize() {
        // Trigger resize event for other components
        window.dispatchEvent(new CustomEvent('appResize'));
    }

    /**
     * Handle visibility change
     */
    handleVisibilityChange() {
        if (document.hidden) {
            // Page is hidden - pause processing if needed
            window.dispatchEvent(new CustomEvent('appPause'));
        } else {
            // Page is visible - resume processing if needed
            window.dispatchEvent(new CustomEvent('appResume'));
        }
    }

    /**
     * Show welcome message
     */
    showWelcomeMessage() {
        console.log(`
🏃‍♂️ Welcome to MaxWattz - AI Running Analysis
✅ TensorFlow.js Backend: ${this.tfBackend}
🎨 Theme: ${this.currentTheme}
📱 Sidebar: ${this.sidebarCollapsed ? 'collapsed' : 'expanded'}
📊 Ready for AI-powered biomechanical analysis
        `);
    }

    /**
     * Show error message to user
     */
    showErrorMessage(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        // Insert at top of page container
        const pageContainer = document.querySelector('.page-container');
        if (pageContainer) {
            pageContainer.insertBefore(errorDiv, pageContainer.firstChild);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.parentNode.removeChild(errorDiv);
                }
            }, 5000);
        }
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        console.log('🧹 Cleaning up MaxWattz App...');
        
        // Dispose TensorFlow.js tensors
        if (typeof tf !== 'undefined') {
            tf.disposeVariables();
        }
    }

    /**
     * Initialize upload workflow
     */
    async initUploadWorkflow() {
        try {
            // Dynamically import upload workflow
            const { uploadWorkflow } = await import('./services/upload-workflow.js');
            await uploadWorkflow.initialize();
            console.log('✅ Upload workflow initialized');
        } catch (error) {
            console.error('❌ Failed to initialize upload workflow:', error);
        }
    }

    /**
     * Utility: Debounce function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize app
const app = new MaxWattzApp();