/**
 * MaxWattz - Navigation Controller
 * Handles page navigation and view management
 */

class NavigationController {
    constructor() {
        this.currentView = 'side'; // side, rear, front
        this.currentPage = 'upload';
        this.history = [];
        
        this.init();
    }

    /**
     * Initialize navigation handlers
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }

    /**
     * Setup all navigation event listeners
     */
    setupEventListeners() {
        // View toggle handlers
        this.setupViewToggle();
        
        // Upload area handlers
        this.setupUploadHandlers();
        
        // Form handlers
        this.setupFormHandlers();
        
        // Back button handlers
        this.setupBackButtonHandlers();
    }

    /**
     * Setup view toggle functionality
     */
    setupViewToggle() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('view-option') && !e.target.disabled) {
                const view = e.target.dataset.view;
                this.switchView(view);
            }
        });
    }

    /**
     * Switch between view types (side, rear, front)
     */
    switchView(view) {
        console.log(`🔄 Switching to ${view} view`);
        
        // Update active view option
        const viewOptions = document.querySelectorAll('.view-option');
        viewOptions.forEach(option => {
            if (option.dataset.view === view) {
                option.classList.add('active');
            } else {
                option.classList.remove('active');
            }
        });
        
        this.currentView = view;
        
        // Update UI based on view
        this.updateViewSpecificUI(view);
    }

    /**
     * Update UI elements specific to the current view
     */
    updateViewSpecificUI(view) {
        const uploadArea = document.getElementById('uploadArea');
        const uploadText = uploadArea?.querySelector('.upload-text h3');
        
        if (uploadText) {
            switch (view) {
                case 'side':
                    uploadText.textContent = 'Upload side view running video';
                    break;
                case 'rear':
                    uploadText.textContent = 'Upload rear view running video';
                    break;
                case 'front':
                    uploadText.textContent = 'Upload front view running video (Coming Soon)';
                    break;
            }
        }
        
        // Update runner info requirements based on view
        this.updateRunnerInfoRequirements(view);
    }

    /**
     * Update runner information requirements based on view type
     */
    updateRunnerInfoRequirements(view) {
        const heightGroup = document.querySelector('#height')?.closest('.form-group');
        const heightLabel = heightGroup?.querySelector('label');
        
        if (heightLabel) {
            switch (view) {
                case 'side':
                    heightLabel.innerHTML = 'Height <span style="color: #e53e3e;">*</span>';
                    break;
                case 'rear':
                    heightLabel.innerHTML = 'Height <span style="color: #e53e3e;">*</span>';
                    break;
                case 'front':
                    heightLabel.innerHTML = 'Height';
                    break;
            }
        }
    }

    /**
     * Setup upload area drag and drop handlers
     */
    setupUploadHandlers() {
        const uploadArea = document.getElementById('uploadArea');
        const videoInput = document.getElementById('videoInput');
        
        if (!uploadArea || !videoInput) return;

        // Click to upload
        uploadArea.addEventListener('click', () => {
            videoInput.click();
        });

        // File input change
        videoInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                this.handleFileUpload(file);
            }
        });

        // Drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const file = e.dataTransfer.files[0];
            if (file && file.type.startsWith('video/')) {
                this.handleFileUpload(file);
            }
        });
    }

    /**
     * Handle file upload
     */
    handleFileUpload(file) {
        console.log('📁 File uploaded:', file.name, file.type, file.size);
        
        // Validate file
        if (!this.validateVideoFile(file)) {
            return;
        }
        
        // Validate runner information
        if (!this.validateRunnerInfo()) {
            return;
        }
        
        // Store file and proceed to analysis
        this.proceedToAnalysis(file);
    }

    /**
     * Validate video file
     */
    validateVideoFile(file) {
        const maxSize = 500 * 1024 * 1024; // 500MB
        const allowedTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/webm'];
        
        if (!allowedTypes.includes(file.type)) {
            this.showError('Please upload a valid video file (MP4, MOV, AVI, WebM)');
            return false;
        }
        
        if (file.size > maxSize) {
            this.showError('File size too large. Please upload a video smaller than 500MB.');
            return false;
        }
        
        return true;
    }

    /**
     * Validate runner information
     */
    validateRunnerInfo() {
        const heightData = this.getHeightData();
        const gender = document.getElementById('gender')?.value;
        const weight = document.getElementById('weight')?.value;
        
        const errors = [];
        
        // Height is required for side and rear views
        if ((this.currentView === 'side' || this.currentView === 'rear') && !heightData.isValid) {
            errors.push('Height is required for biomechanical analysis');
        }
        
        // Weight validation
        if (weight && (isNaN(weight) || weight < 30 || weight > 500)) {
            errors.push('Please enter a valid weight between 30-500');
        }
        
        if (errors.length > 0) {
            this.showError(errors.join('<br>'));
            return false;
        }
        
        return true;
    }

    /**
     * Get height data from form
     */
    getHeightData() {
        const heightSystem = document.getElementById('heightSystem')?.value;
        
        if (heightSystem === 'metric') {
            const meters = document.getElementById('heightMeters')?.value;
            const centimeters = document.getElementById('heightCentimeters')?.value;
            
            if (meters && centimeters) {
                return {
                    isValid: true,
                    system: 'metric',
                    value: `${meters}m${centimeters}cm`,
                    inches: (parseInt(meters) * 100 + parseInt(centimeters)) * 0.393701
                };
            }
        } else {
            const feet = document.getElementById('heightFeet')?.value;
            const inches = document.getElementById('heightInches')?.value;
            
            if (feet && inches !== '') {
                return {
                    isValid: true,
                    system: 'imperial',
                    value: `${feet}'${inches}"`,
                    inches: parseInt(feet) * 12 + parseInt(inches)
                };
            }
        }
        
        return { isValid: false, system: heightSystem };
    }

    /**
     * Validate height format
     */
    validateHeightFormat(height) {
        // Accept formats: 5'10", 5ft 10in, 5'10, etc.
        const patterns = [
            /^\d+'\d+"?$/,           // 5'10" or 5'10
            /^\d+ft\s*\d+in$/,       // 5ft 10in
            /^\d+'\s*\d+"?$/,        // 5' 10" or 5' 10
            /^\d+\.\d+ft$/,          // 5.8ft
            /^\d+cm$/                // 175cm
        ];
        
        return patterns.some(pattern => pattern.test(height.trim()));
    }

    /**
     * Proceed to analysis page
     */
    proceedToAnalysis(file) {
        console.log(`🚀 Proceeding to ${this.currentView} view analysis`);
        
        // Get form data
        const heightData = this.getHeightData();
        const weightUnit = document.getElementById('weightUnit')?.value || 'lbs';
        const weight = document.getElementById('weight')?.value;
        
        // Store file and runner data
        window.uploadedFile = file;
        window.runnerData = {
            height: heightData.value,
            heightInches: heightData.inches,
            gender: document.getElementById('gender')?.value,
            weight: weight,
            weightUnit: weightUnit,
            view: this.currentView
        };
        
        // Add to history
        this.history.push({
            page: 'upload',
            view: this.currentView,
            timestamp: Date.now()
        });
        
        // Navigate to appropriate analysis page
        if (this.currentView === 'side') {
            this.navigateToSideAnalysis();
        } else if (this.currentView === 'rear') {
            this.navigateToRearAnalysis();
        }
    }

    /**
     * Navigate to side view analysis
     */
    navigateToSideAnalysis() {
        // Load side analysis content
        this.loadSideAnalysisPage();
        
        // Update navigation
        if (window.app) {
            window.app.navigateToPage('side-analysis');
        }
    }

    /**
     * Navigate to rear view analysis
     */
    navigateToRearAnalysis() {
        // Load rear analysis content
        this.loadRearAnalysisPage();
        
        // Update navigation
        if (window.app) {
            window.app.navigateToPage('rear-analysis');
        }
    }

    /**
     * Load side analysis page content
     */
    loadSideAnalysisPage() {
        const page = document.getElementById('sideAnalysisPage');
        if (!page) return;

        page.innerHTML = `
            <div class="analysis-container">
                <div class="video-section">
                    <div class="video-container">
                        <video id="analysisVideo" class="video-element" controls></video>
                        <canvas id="analysisCanvas" class="canvas-overlay"></canvas>
                    </div>
                    <div class="video-controls">
                        <button class="control-btn play" id="playBtn">
                            <span>▶</span> Play
                        </button>
                        <button class="control-btn pause" id="pauseBtn">
                            <span>⏸</span> Pause
                        </button>
                        <button class="control-btn reset" id="resetBtn">
                            <span>↻</span> Reset
                        </button>
                        <button class="control-btn process" id="processBtn">
                            <span>🔬</span> Process Frame
                        </button>
                    </div>
                </div>
                <div class="results-section">
                    <div class="metrics-panel">
                        <h3>Detection Metrics</h3>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-label">Model Status:</span>
                                <span class="metric-value" id="modelStatus">Loading...</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Detection Stage:</span>
                                <span class="metric-value" id="detectionStage">Idle</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Person Detected:</span>
                                <span class="metric-value" id="personDetected">No</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Confidence:</span>
                                <span class="metric-value" id="confidence">0%</span>
                            </div>
                        </div>
                    </div>
                    <div class="debug-panel">
                        <pre id="debugOutput">Ready for analysis...</pre>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Load rear analysis page content
     */
    loadRearAnalysisPage() {
        const page = document.getElementById('rearAnalysisPage');
        if (!page) return;

        page.innerHTML = `
            <div class="analysis-container">
                <div class="video-section">
                    <div class="video-container">
                        <video id="rearAnalysisVideo" class="video-element" controls></video>
                        <canvas id="rearAnalysisCanvas" class="canvas-overlay"></canvas>
                    </div>
                    <div class="video-controls">
                        <button class="control-btn play" id="rearPlayBtn">
                            <span>▶</span> Play
                        </button>
                        <button class="control-btn pause" id="rearPauseBtn">
                            <span>⏸</span> Pause
                        </button>
                        <button class="control-btn reset" id="rearResetBtn">
                            <span>↻</span> Reset
                        </button>
                        <button class="control-btn process" id="rearProcessBtn">
                            <span>🔬</span> Process Frame
                        </button>
                    </div>
                </div>
                <div class="results-section">
                    <div class="metrics-panel">
                        <h3>Rear View Analysis</h3>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-label">Model Status:</span>
                                <span class="metric-value" id="rearModelStatus">Loading...</span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">Symmetry Score:</span>
                                <span class="metric-value" id="symmetryScore">N/A</span>
                            </div>
                        </div>
                    </div>
                    <div class="debug-panel">
                        <pre id="rearDebugOutput">Ready for rear view analysis...</pre>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup form handlers
     */
    setupFormHandlers() {
        // Height input formatting
        document.addEventListener('input', (e) => {
            if (e.target.id === 'height') {
                this.formatHeightInput(e.target);
            }
        });
        
        // Weight input validation
        document.addEventListener('input', (e) => {
            if (e.target.id === 'weight') {
                this.validateWeightInput(e.target);
            }
        });
    }

    /**
     * Format height input as user types
     */
    formatHeightInput(input) {
        let value = input.value.replace(/[^\d]/g, '');
        
        if (value.length >= 1) {
            const feet = value.substring(0, 1);
            const inches = value.substring(1, 3);
            
            if (inches) {
                input.value = `${feet}'${inches}"`;
            } else {
                input.value = `${feet}'`;
            }
        }
    }

    /**
     * Validate weight input
     */
    validateWeightInput(input) {
        const value = parseInt(input.value);
        
        if (value && (value < 50 || value > 400)) {
            input.style.borderColor = '#e53e3e';
        } else {
            input.style.borderColor = '#e2e8f0';
        }
    }

    /**
     * Setup back button handlers
     */
    setupBackButtonHandlers() {
        // Listen for browser back button
        window.addEventListener('popstate', (e) => {
            this.handleBackNavigation();
        });
    }

    /**
     * Handle back navigation
     */
    handleBackNavigation() {
        if (this.history.length > 0) {
            const lastState = this.history.pop();
            console.log('⬅️ Navigating back to:', lastState);
            
            // Navigate to previous page
            if (window.app) {
                window.app.navigateToPage(lastState.page);
            }
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        // Remove any existing error messages
        const existingError = document.querySelector('.error-message');
        if (existingError) {
            existingError.remove();
        }
        
        // Create new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = message;
        
        // Insert before upload area
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.parentNode.insertBefore(errorDiv, uploadArea);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorDiv.parentNode) {
                    errorDiv.remove();
                }
            }, 5000);
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        const successDiv = document.createElement('div');
        successDiv.className = 'success-message';
        successDiv.textContent = message;
        
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea) {
            uploadArea.parentNode.insertBefore(successDiv, uploadArea);
            
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.remove();
                }
            }, 3000);
        }
    }
}

// Initialize navigation controller
const navigation = new NavigationController();