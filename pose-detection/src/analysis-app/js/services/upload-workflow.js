/**
 * Upload Workflow Service
 * Orchestrates the complete side + rear video upload and processing flow
 */

import { ENV } from '../../config/env.js';
import { supabaseService } from './supabase-client.js';
import { s3UploadService } from './s3-upload.js';
import { videoValidator } from './video-validator.js';
import { processingScreen } from '../components/processing-screen.js';

class UploadWorkflowService {
    constructor() {
        this.currentSession = null;
        this.uploadState = {
            sideVideo: null,
            rearVideo: null,
            userInfo: null,
            validation: {
                side: null,
                rear: null
            },
            uploads: {
                side: null,
                rear: null
            },
            isUploading: false,
            isProcessing: false
        };
        this.eventHandlers = new Map();
    }

    /**
     * Initialize upload workflow
     */
    async initialize() {
        console.log('🚀 Initializing Upload Workflow');
        
        // Add upload styles first
        this.addUploadStyles();
        
        // Initialize dual upload areas immediately
        this.createDualUploadZones();
        
        // Bind form events
        this.bindFormEvents();
    }

    /**
     * Bind upload drag/drop events
     */
    bindUploadEvents() {
        const uploadArea = document.getElementById('uploadArea');
        const videoInput = document.getElementById('videoInput');
        
        if (uploadArea && videoInput) {
            // Drag and drop events
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                const files = Array.from(e.dataTransfer.files);
                this.handleFileSelection(files);
            });

            // Click to select
            uploadArea.addEventListener('click', () => {
                videoInput.click();
            });

            // File input change
            videoInput.addEventListener('change', (e) => {
                const files = Array.from(e.target.files);
                this.handleFileSelection(files);
            });
        }
    }

    /**
     * Bind form events
     */
    bindFormEvents() {
        // View type toggle
        const viewOptions = document.querySelectorAll('.view-option:not(.coming-soon)');
        viewOptions.forEach(option => {
            option.addEventListener('click', (e) => {
                this.handleViewTypeChange(e.target.dataset.view);
            });
        });

        // Form validation
        const formInputs = document.querySelectorAll('#heightFeet, #heightInches, #heightMeters, #heightCentimeters, #gender, #weight');
        formInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.validateForm();
            });
        });
    }

    /**
     * Initialize upload areas
     */
    initializeUploadAreas() {
        // Add upload area styles
        this.addUploadStyles();
        
        // Create side and rear upload zones
        this.createDualUploadZones();
    }

    /**
     * Handle file selection from drag/drop or file picker
     */
    async handleFileSelection(files) {
        if (files.length === 0) return;
        
        const videoFiles = files.filter(file => file.type.startsWith('video/'));
        if (videoFiles.length === 0) {
            this.showError('Please select video files only');
            return;
        }

        // Handle single or multiple videos
        if (videoFiles.length === 1) {
            await this.handleSingleVideoUpload(videoFiles[0]);
        } else {
            await this.handleMultipleVideoUpload(videoFiles);
        }
    }

    /**
     * Handle single video upload - let user specify view type
     */
    async handleSingleVideoUpload(file) {
        const activeViewType = this.getActiveViewType();
        
        if (!activeViewType) {
            this.showError('Please select Side View or Rear View first');
            return;
        }

        await this.processVideoFile(file, activeViewType);
    }

    /**
     * Handle multiple video upload - auto-detect or prompt
     */
    async handleMultipleVideoUpload(files) {
        console.log(`📁 Processing ${files.length} video files`);
        
        // Try to auto-detect view types from filenames
        const detectedFiles = this.autoDetectViewTypes(files);
        
        if (detectedFiles.side && detectedFiles.rear) {
            // Process both videos
            await Promise.all([
                this.processVideoFile(detectedFiles.side, 'side'),
                this.processVideoFile(detectedFiles.rear, 'rear')
            ]);
        } else {
            // Show selection modal for ambiguous files
            this.showViewTypeSelectionModal(files);
        }
    }

    /**
     * Auto-detect view types from filenames
     */
    autoDetectViewTypes(files) {
        const detected = { side: null, rear: null };
        
        files.forEach(file => {
            const filename = file.name.toLowerCase();
            
            if (filename.includes('side') && !detected.side) {
                detected.side = file;
            } else if ((filename.includes('rear') || filename.includes('back')) && !detected.rear) {
                detected.rear = file;
            }
        });
        
        return detected;
    }

    /**
     * Process individual video file
     */
    async processVideoFile(file, viewType) {
        try {
            console.log(`🎬 Processing ${viewType} video: ${file.name}`);
            
            // Show processing indicator
            this.showVideoProcessing(viewType);
            
            // Validate video
            const validation = await videoValidator.validateVideo(file, viewType);
            
            if (!validation.isValid) {
                this.showVideoError(viewType, validation);
                return false;
            }

            // Store video and validation
            this.uploadState[`${viewType}Video`] = file;
            this.uploadState.validation[viewType] = validation;
            
            // Show video preview
            await this.showVideoPreview(file, validation, viewType);
            
            // Update UI state
            this.updateUploadProgress();
            
            // Check if ready to proceed
            this.checkReadyToUpload();
            
            return true;
            
        } catch (error) {
            console.error(`❌ Error processing ${viewType} video:`, error);
            this.showVideoError(viewType, { errors: [{ message: error.message }] });
            return false;
        }
    }

    /**
     * Show video processing state
     */
    showVideoProcessing(viewType) {
        const container = document.getElementById(`${viewType}VideoContainer`);
        if (container) {
            container.innerHTML = `
                <div class="video-processing">
                    <div class="processing-spinner"></div>
                    <p>Validating ${viewType} video...</p>
                </div>
            `;
        }
    }

    /**
     * Show video error state
     */
    showVideoError(viewType, validation) {
        const container = document.getElementById(`${viewType}VideoContainer`);
        if (container) {
            const errorMessages = validation.errors.map(e => e.message).join(', ');
            container.innerHTML = `
                <div class="video-error">
                    <div class="error-icon">⚠️</div>
                    <h4>Invalid ${viewType} Video</h4>
                    <p>${errorMessages}</p>
                    <button class="btn btn-secondary btn-sm" onclick="window.uploadWorkflow.retryVideo('${viewType}')">
                        Try Another Video
                    </button>
                </div>
            `;
        }
    }

    /**
     * Show video preview using VideoPreview component
     */
    async showVideoPreview(file, validation, viewType) {
        const containerId = `${viewType}VideoContainer`;
        
        // Import and use VideoPreview component
        const { VideoPreview } = await import('../components/video-preview.js');
        const preview = new VideoPreview(containerId);
        await preview.createPreview(file, validation);
        
        // Store preview reference
        if (!this.videoPreviews) this.videoPreviews = {};
        this.videoPreviews[viewType] = preview;
    }

    /**
     * Get currently active view type
     */
    getActiveViewType() {
        const activeOption = document.querySelector('.view-option.active');
        return activeOption ? activeOption.dataset.view : null;
    }

    /**
     * Handle view type change
     */
    handleViewTypeChange(viewType) {
        // Update UI
        document.querySelectorAll('.view-option').forEach(option => {
            option.classList.remove('active');
        });
        document.querySelector(`[data-view="${viewType}"]`).classList.add('active');
        
        // Update upload area text
        this.updateUploadAreaText(viewType);
    }

    /**
     * Update upload area text based on view type
     */
    updateUploadAreaText(viewType) {
        const uploadText = document.querySelector('.upload-text h3');
        if (uploadText) {
            const viewLabel = viewType === 'side' ? 'Side View' : 'Rear View';
            uploadText.textContent = `Drop ${viewLabel} video here or click to select`;
        }
    }

    /**
     * Create dual upload zones for side and rear
     */
    createDualUploadZones() {
        const uploadPage = document.getElementById('uploadPage');
        if (!uploadPage) {
            console.warn('⚠️ Upload page not found, retrying in 100ms...');
            setTimeout(() => this.createDualUploadZones(), 100);
            return;
        }

        // Replace single upload area with dual zones when both views are needed
        const currentUploadArea = uploadPage.querySelector('.upload-area');
        if (currentUploadArea) {
            const dualUploadHTML = `
                <div class="dual-upload-container">
                    <div class="upload-zone" id="sideUploadZone">
                        <div class="upload-zone-header">
                            <h3>Side View Video</h3>
                            <span class="zone-status" id="sideStatus">Required</span>
                        </div>
                        <div class="upload-zone-content" id="sideVideoContainer">
                            <div class="upload-placeholder">
                                <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p>Drop side view video here</p>
                                <span class="file-hint">Runner from the side (profile view)</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="upload-zone" id="rearUploadZone">
                        <div class="upload-zone-header">
                            <h3>Rear View Video</h3>
                            <span class="zone-status required" id="rearStatus">Required</span>
                        </div>
                        <div class="upload-zone-content" id="rearVideoContainer">
                            <div class="upload-placeholder">
                                <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                </svg>
                                <p>Drop rear view video here</p>
                                <span class="file-hint">Runner from behind</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <input type="file" id="sideVideoInput" accept="video/*" style="display: none;">
                <input type="file" id="rearVideoInput" accept="video/*" style="display: none;">
            `;
            
            currentUploadArea.outerHTML = dualUploadHTML;
            
            console.log('✅ Dual upload zones created successfully');
            
            // Bind events to new upload zones
            this.bindDualUploadEvents();
        } else {
            console.warn('⚠️ Upload area not found, retrying in 100ms...');
            setTimeout(() => this.createDualUploadZones(), 100);
        }
    }

    /**
     * Bind events to dual upload zones
     */
    bindDualUploadEvents() {
        ['side', 'rear'].forEach(viewType => {
            const zone = document.getElementById(`${viewType}UploadZone`);
            const input = document.getElementById(`${viewType}VideoInput`);
            
            if (zone && input) {
                // Drag and drop
                zone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    zone.classList.add('drag-over');
                });

                zone.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    zone.classList.remove('drag-over');
                });

                zone.addEventListener('drop', async (e) => {
                    e.preventDefault();
                    zone.classList.remove('drag-over');
                    const files = Array.from(e.dataTransfer.files);
                    const videoFile = files.find(f => f.type.startsWith('video/'));
                    if (videoFile) {
                        await this.processVideoFile(videoFile, viewType);
                    }
                });

                // Click to select
                zone.addEventListener('click', () => {
                    input.click();
                });

                // File input change
                input.addEventListener('change', async (e) => {
                    const file = e.target.files[0];
                    if (file) {
                        await this.processVideoFile(file, viewType);
                    }
                });
            }
        });
    }

    /**
     * Validate form data
     */
    validateForm() {
        const validation = this.getFormValidation();
        const isValid = validation.height.isValid && validation.gender && validation.weight;
        
        // Update proceed button state
        const proceedButton = document.getElementById('proceedToAnalysis');
        if (proceedButton) {
            proceedButton.disabled = !isValid || !this.canProceedToAnalysis();
        }
        
        return isValid;
    }

    /**
     * Get form validation data
     */
    getFormValidation() {
        // Import navigation helper for height validation
        const heightSystem = document.getElementById('heightSystem')?.value || 'imperial';
        let heightData = { isValid: false };
        
        if (heightSystem === 'metric') {
            const meters = document.getElementById('heightMeters')?.value;
            const centimeters = document.getElementById('heightCentimeters')?.value;
            
            if (meters && centimeters !== '') {
                const totalInches = (parseInt(meters) * 100 + parseInt(centimeters)) * 0.393701;
                heightData = {
                    isValid: true,
                    system: 'metric',
                    display: `${meters}m${centimeters}cm`,
                    inches: totalInches
                };
            }
        } else {
            const feet = document.getElementById('heightFeet')?.value;
            const inches = document.getElementById('heightInches')?.value;
            
            if (feet && inches !== '') {
                const totalInches = parseInt(feet) * 12 + parseInt(inches);
                heightData = {
                    isValid: true,
                    system: 'imperial',
                    display: `${feet}'${inches}"`,
                    inches: totalInches
                };
            }
        }
        
        const gender = document.getElementById('gender')?.value;
        const weight = document.getElementById('weight')?.value;
        const weightUnit = document.getElementById('weightUnit')?.value || 'lbs';
        
        let weightData = null;
        if (weight) {
            const weightLbs = weightUnit === 'kg' ? parseFloat(weight) * 2.20462 : parseFloat(weight);
            weightData = {
                lbs: weightLbs,
                display: `${weight} ${weightUnit}`
            };
        }
        
        return {
            height: heightData,
            gender: gender,
            weight: weightData
        };
    }

    /**
     * Check if can proceed to analysis
     */
    canProceedToAnalysis() {
        const hasSideVideo = this.uploadState.sideVideo && this.uploadState.validation.side?.isValid;
        const hasRearVideo = this.uploadState.rearVideo && this.uploadState.validation.rear?.isValid;
        const hasValidForm = this.validateForm();
        return hasSideVideo && hasRearVideo && hasValidForm;
    }

    /**
     * Update upload progress UI
     */
    updateUploadProgress() {
        const hasSide = this.uploadState.sideVideo && this.uploadState.validation.side?.isValid;
        const hasRear = this.uploadState.rearVideo && this.uploadState.validation.rear?.isValid;
        
        // Update status indicators
        if (hasSide) {
            this.updateStatusIndicator('side', 'complete', '✓ Ready');
        }
        if (hasRear) {
            this.updateStatusIndicator('rear', 'complete', '✓ Ready');
        }
        
        // Update proceed button
        this.updateProceedButton();
    }

    /**
     * Update status indicator
     */
    updateStatusIndicator(viewType, status, text) {
        const indicator = document.getElementById(`${viewType}Status`);
        if (indicator) {
            indicator.textContent = text;
            indicator.className = `zone-status ${status}`;
        }
    }

    /**
     * Update proceed button
     */
    updateProceedButton() {
        const canProceed = this.canProceedToAnalysis();
        
        // Create or update proceed button
        let proceedButton = document.getElementById('proceedToAnalysis');
        if (!proceedButton) {
            const runnerInfo = document.querySelector('.runner-info');
            if (runnerInfo) {
                const buttonHTML = `
                    <div class="proceed-section">
                        <button class="btn btn-primary btn-lg" id="proceedToAnalysis" disabled>
                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                            Start Analysis
                        </button>
                        <p class="proceed-note">We'll analyze your running form and provide biomechanical insights</p>
                    </div>
                `;
                runnerInfo.insertAdjacentHTML('afterend', buttonHTML);
                
                // Bind click event
                document.getElementById('proceedToAnalysis').addEventListener('click', () => {
                    this.startAnalysis();
                });
            }
        }
        
        // Update button state
        proceedButton = document.getElementById('proceedToAnalysis');
        if (proceedButton) {
            proceedButton.disabled = !canProceed;
            proceedButton.textContent = canProceed ? 'Start Analysis' : 'Complete Required Fields';
        }
    }

    /**
     * Start the analysis workflow
     */
    async startAnalysis() {
        if (!this.canProceedToAnalysis()) {
            this.showError('Please complete all required fields and upload both side and rear view videos');
            return;
        }

        try {
            this.uploadState.isUploading = true;
            
            // Show upload progress
            this.showUploadProgress();
            
            // Get form data
            const formData = this.getFormValidation();
            
            // Create analysis session in Supabase
            const sessionData = await supabaseService.createAnalysisSession({
                email: '<EMAIL>', // Temporary until auth
                heightInches: formData.height.inches,
                heightDisplay: formData.height.display,
                gender: formData.gender,
                weightLbs: formData.weight.lbs,
                weightDisplay: formData.weight.display
            });
            
            this.currentSession = sessionData;
            console.log('✅ Analysis session created:', sessionData.analysis_id);
            
            // Upload videos to S3
            await this.uploadVideos();
            
            // Queue for processing
            await this.queueForProcessing();
            
            // Show processing screen
            processingScreen.show(sessionData.analysis_id, {
                side_video_id: this.uploadState.uploads.side?.videoId,
                rear_video_id: this.uploadState.uploads.rear?.videoId
            });
            
        } catch (error) {
            console.error('❌ Analysis start failed:', error);
            this.showError(`Failed to start analysis: ${error.message}`);
            this.uploadState.isUploading = false;
        }
    }

    /**
     * Upload videos to S3 and record in database
     */
    async uploadVideos() {
        const uploads = [];
        
        // Upload side video
        if (this.uploadState.sideVideo) {
            uploads.push(this.uploadSingleVideo(this.uploadState.sideVideo, 'side'));
        }
        
        // Upload rear video
        if (this.uploadState.rearVideo) {
            uploads.push(this.uploadSingleVideo(this.uploadState.rearVideo, 'rear'));
        }
        
        const results = await Promise.all(uploads);
        console.log('✅ All videos uploaded successfully');
        
        return results;
    }

    /**
     * Upload single video
     */
    async uploadSingleVideo(file, viewType) {
        try {
            const metadata = this.uploadState.validation[viewType].metadata;
            
            // Upload to S3
            const s3Result = await s3UploadService.uploadVideo(
                file,
                viewType,
                this.currentSession.session_token,
                (progress) => {
                    this.updateUploadProgress(viewType, progress);
                }
            );
            
            // Record in database
            const videoRecord = await supabaseService.recordVideoUpload({
                filename: `${Date.now()}_${file.name}`,
                originalFilename: file.name,
                fileSize: file.size,
                mimeType: file.type,
                s3Key: s3Result.s3Key,
                s3Url: s3Result.s3Url,
                duration: metadata.duration,
                width: metadata.width,
                height: metadata.height,
                fps: metadata.fps,
                codec: metadata.codec,
                bitrate: metadata.bitrate,
                viewType: viewType,
                sessionId: this.currentSession.session_token
            });
            
            // Update analysis with video reference
            await supabaseService.updateAnalysisVideo(
                this.currentSession.analysis_id,
                viewType,
                videoRecord.id
            );
            
            // Store upload result
            this.uploadState.uploads[viewType] = {
                videoId: videoRecord.id,
                s3Result: s3Result,
                metadata: metadata
            };
            
            return videoRecord;
            
        } catch (error) {
            console.error(`❌ Failed to upload ${viewType} video:`, error);
            throw error;
        }
    }

    /**
     * Queue videos for Modal processing
     */
    async queueForProcessing() {
        const queuePromises = [];
        
        if (this.uploadState.uploads.side) {
            queuePromises.push(
                supabaseService.queueVideoProcessing(
                    this.currentSession.analysis_id,
                    this.uploadState.uploads.side.videoId,
                    'side'
                )
            );
        }
        
        if (this.uploadState.uploads.rear) {
            queuePromises.push(
                supabaseService.queueVideoProcessing(
                    this.currentSession.analysis_id,
                    this.uploadState.uploads.rear.videoId,
                    'rear'
                )
            );
        }
        
        await Promise.all(queuePromises);
        console.log('✅ Videos queued for processing');
    }

    /**
     * Show upload progress
     */
    showUploadProgress() {
        // This could show a progress modal or update the existing UI
        console.log('📤 Starting video uploads...');
    }

    /**
     * Update upload progress for specific video
     */
    updateUploadProgress(viewType, progress) {
        console.log(`📤 ${viewType} upload: ${progress.percentage}%`);
        // Update UI progress indicators
    }

    /**
     * Show error message
     */
    showError(message) {
        // Create error notification
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-notification';
        errorDiv.innerHTML = `
            <div class="error-content">
                <svg class="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <span>${message}</span>
                <button class="error-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
        `;
        
        document.body.appendChild(errorDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    /**
     * Add upload workflow styles
     */
    addUploadStyles() {
        if (document.getElementById('upload-workflow-styles')) return;
        
        const styles = document.createElement('style');
        styles.id = 'upload-workflow-styles';
        styles.textContent = `
            .dual-upload-container {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 24px;
                margin-bottom: 32px;
            }
            
            .upload-zone {
                border: 2px dashed var(--border-color);
                border-radius: 12px;
                padding: 24px;
                transition: all 0.3s ease;
                cursor: pointer;
            }
            
            .upload-zone:hover {
                border-color: var(--accent-primary);
                background: var(--bg-tertiary);
            }
            
            .upload-zone.drag-over {
                border-color: var(--accent-primary);
                background: rgba(102, 126, 234, 0.05);
            }
            
            .upload-zone-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 16px;
            }
            
            .upload-zone-header h3 {
                font-size: 18px;
                font-weight: 600;
                color: var(--text-primary);
                margin: 0;
            }
            
            .zone-status {
                padding: 4px 12px;
                border-radius: 16px;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
            }
            
            .zone-status.required {
                background: rgba(229, 62, 62, 0.1);
                color: var(--error);
                border: 1px solid rgba(229, 62, 62, 0.3);
            }
            
            .zone-status.optional {
                background: rgba(160, 174, 192, 0.1);
                color: var(--text-muted);
                border: 1px solid rgba(160, 174, 192, 0.3);
            }
            
            .zone-status.complete {
                background: rgba(56, 161, 105, 0.1);
                color: var(--success);
                border: 1px solid rgba(56, 161, 105, 0.3);
            }
            
            .upload-placeholder {
                text-align: center;
                padding: 40px 20px;
            }
            
            .upload-placeholder .upload-icon {
                width: 48px;
                height: 48px;
                color: var(--text-muted);
                margin-bottom: 16px;
            }
            
            .upload-placeholder p {
                font-size: 16px;
                font-weight: 500;
                color: var(--text-secondary);
                margin-bottom: 8px;
            }
            
            .file-hint {
                font-size: 14px;
                color: var(--text-muted);
                font-style: italic;
            }
            
            .video-processing {
                text-align: center;
                padding: 40px 20px;
            }
            
            .processing-spinner {
                width: 32px;
                height: 32px;
                border: 3px solid rgba(102, 126, 234, 0.3);
                border-top: 3px solid var(--accent-primary);
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 16px;
            }
            
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
            
            .video-error {
                text-align: center;
                padding: 40px 20px;
            }
            
            .error-icon {
                font-size: 48px;
                margin-bottom: 16px;
            }
            
            .video-error h4 {
                color: var(--error);
                margin-bottom: 8px;
            }
            
            .video-error p {
                color: var(--text-muted);
                margin-bottom: 16px;
            }
            
            .proceed-section {
                text-align: center;
                margin-top: 32px;
                padding-top: 32px;
                border-top: 1px solid var(--border-color);
            }
            
            .btn-lg {
                padding: 16px 32px;
                font-size: 16px;
                font-weight: 600;
            }
            
            .btn-icon {
                width: 20px;
                height: 20px;
                margin-left: 8px;
            }
            
            .proceed-note {
                font-size: 14px;
                color: var(--text-muted);
                margin-top: 12px;
            }
            
            .error-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--bg-primary);
                border: 1px solid var(--error);
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                z-index: 1000;
                max-width: 400px;
            }
            
            .error-content {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;
                color: var(--error);
            }
            
            .error-content .error-icon {
                width: 20px;
                height: 20px;
                flex-shrink: 0;
            }
            
            .error-close {
                background: none;
                border: none;
                color: var(--error);
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
        `;
        
        document.head.appendChild(styles);
    }

    /**
     * Retry video upload
     */
    retryVideo(viewType) {
        // Clear current video state
        this.uploadState[`${viewType}Video`] = null;
        this.uploadState.validation[viewType] = null;
        
        // Reset UI
        const container = document.getElementById(`${viewType}VideoContainer`);
        if (container) {
            container.innerHTML = `
                <div class="upload-placeholder">
                    <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p>Drop ${viewType} view video here</p>
                    <span class="file-hint">${viewType === 'side' ? 'Runner from the side (profile view)' : 'Runner from behind'}</span>
                </div>
            `;
        }
        
        // Update status
        this.updateStatusIndicator(viewType, 'required', 'Required');
        this.updateProceedButton();
    }

    /**
     * Reset workflow state
     */
    reset() {
        this.currentSession = null;
        this.uploadState = {
            sideVideo: null,
            rearVideo: null,
            userInfo: null,
            validation: { side: null, rear: null },
            uploads: { side: null, rear: null },
            isUploading: false,
            isProcessing: false
        };
        
        // Clean up video previews
        if (this.videoPreviews) {
            Object.values(this.videoPreviews).forEach(preview => preview.remove());
            this.videoPreviews = {};
        }
    }
}

// Export singleton instance
export const uploadWorkflow = new UploadWorkflowService();

// Make available globally
window.uploadWorkflow = uploadWorkflow;