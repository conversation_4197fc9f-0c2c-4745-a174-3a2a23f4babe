/**
 * S3 Upload Service
 * Handles direct browser-to-S3 multipart uploads with presigned URLs
 */

import { ENV } from '../../config/env.js';
import { supabaseService } from './supabase-client.js';

// Import local mock for development
if (ENV.APP_ENV === 'development' && !ENV.ENABLE_S3_UPLOADS) {
    await import('./s3-local-mock.js');
}

class S3UploadService {
    constructor() {
        this.uploadProgress = new Map();
        this.activeUploads = new Map();
    }

    /**
     * Initialize AWS S3 client
     */
    async initS3() {
        if (this.s3) return;

        try {
            // For browser-based uploads, we'll use presigned URLs from a backend service
            // This avoids exposing AWS credentials in the browser
            console.log('✅ S3 upload service initialized');
        } catch (error) {
            console.error('❌ Failed to initialize S3:', error);
            throw error;
        }
    }

    /**
     * Generate unique S3 key for video
     */
    generateS3Key(viewType, filename) {
        const timestamp = Date.now();
        const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
        const prefix = viewType === 'side' ? ENV.AWS_S3_SIDE_PREFIX : ENV.AWS_S3_REAR_PREFIX;
        
        return `${prefix}${timestamp}_${sanitizedFilename}`;
    }

    /**
     * Get presigned upload URL from backend
     */
    async getPresignedUploadUrl(key, contentType) {
        // In production, this would call your Supabase Edge Function
        // For now, we'll simulate the response
        const response = await fetch('/api/s3/presigned-upload', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                key,
                contentType,
                bucket: ENV.AWS_S3_BUCKET
            })
        });

        if (!response.ok) {
            throw new Error('Failed to get presigned URL');
        }

        return response.json();
    }

    /**
     * Upload video file to S3 with progress tracking
     */
    async uploadVideo(file, viewType, sessionId, onProgress) {
        await this.initS3();

        const uploadId = `${sessionId}-${viewType}`;
        const s3Key = this.generateS3Key(viewType, file.name);

        try {
            // Initialize upload progress
            this.uploadProgress.set(uploadId, {
                bytesUploaded: 0,
                totalBytes: file.size,
                percentage: 0,
                status: 'preparing'
            });

            // Update progress callback
            const updateProgress = (bytesUploaded) => {
                const progress = {
                    bytesUploaded,
                    totalBytes: file.size,
                    percentage: Math.round((bytesUploaded / file.size) * 100),
                    status: 'uploading'
                };
                this.uploadProgress.set(uploadId, progress);
                if (onProgress) onProgress(progress);
            };

            // For files > 100MB, use multipart upload
            if (file.size > 100 * 1024 * 1024) {
                return await this.multipartUpload(file, s3Key, uploadId, updateProgress);
            } else {
                return await this.singlePartUpload(file, s3Key, uploadId, updateProgress);
            }

        } catch (error) {
            this.uploadProgress.set(uploadId, {
                bytesUploaded: 0,
                totalBytes: file.size,
                percentage: 0,
                status: 'error',
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Single part upload for smaller files
     */
    async singlePartUpload(file, s3Key, uploadId, updateProgress) {
        // Get presigned URL
        const { uploadUrl, fileUrl } = await this.getPresignedUploadUrl(s3Key, file.type);

        // Create XMLHttpRequest for progress tracking
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            // Track upload progress
            xhr.upload.addEventListener('progress', (event) => {
                if (event.lengthComputable) {
                    updateProgress(event.loaded);
                }
            });

            // Handle completion
            xhr.addEventListener('load', () => {
                if (xhr.status === 200 || xhr.status === 204) {
                    this.uploadProgress.set(uploadId, {
                        bytesUploaded: file.size,
                        totalBytes: file.size,
                        percentage: 100,
                        status: 'completed'
                    });
                    resolve({
                        s3Key,
                        s3Url: fileUrl,
                        etag: xhr.getResponseHeader('ETag')
                    });
                } else {
                    reject(new Error(`Upload failed: ${xhr.statusText}`));
                }
            });

            // Handle errors
            xhr.addEventListener('error', () => {
                reject(new Error('Network error during upload'));
            });

            // Handle abort
            xhr.addEventListener('abort', () => {
                reject(new Error('Upload cancelled'));
            });

            // Store xhr for potential cancellation
            this.activeUploads.set(uploadId, xhr);

            // Start upload
            xhr.open('PUT', uploadUrl);
            xhr.setRequestHeader('Content-Type', file.type);
            xhr.send(file);
        });
    }

    /**
     * Multipart upload for larger files
     */
    async multipartUpload(file, s3Key, uploadId, updateProgress) {
        const chunkSize = 10 * 1024 * 1024; // 10MB chunks
        const numParts = Math.ceil(file.size / chunkSize);
        
        // Initialize multipart upload
        const { uploadId: multipartUploadId } = await this.initializeMultipartUpload(s3Key, file.type);
        
        const parts = [];
        let uploadedBytes = 0;

        try {
            // Upload each part
            for (let partNumber = 1; partNumber <= numParts; partNumber++) {
                const start = (partNumber - 1) * chunkSize;
                const end = Math.min(start + chunkSize, file.size);
                const chunk = file.slice(start, end);

                // Get presigned URL for this part
                const { uploadUrl } = await this.getPresignedPartUrl(s3Key, multipartUploadId, partNumber);

                // Upload part
                const etag = await this.uploadPart(chunk, uploadUrl, uploadId);
                parts.push({ PartNumber: partNumber, ETag: etag });

                // Update progress
                uploadedBytes += chunk.size;
                updateProgress(uploadedBytes);
            }

            // Complete multipart upload
            const result = await this.completeMultipartUpload(s3Key, multipartUploadId, parts);
            
            this.uploadProgress.set(uploadId, {
                bytesUploaded: file.size,
                totalBytes: file.size,
                percentage: 100,
                status: 'completed'
            });

            return {
                s3Key,
                s3Url: result.fileUrl,
                etag: result.etag
            };

        } catch (error) {
            // Abort multipart upload on error
            await this.abortMultipartUpload(s3Key, multipartUploadId);
            throw error;
        }
    }

    /**
     * Initialize multipart upload
     */
    async initializeMultipartUpload(key, contentType) {
        const response = await fetch('/api/s3/multipart/init', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                key,
                contentType,
                bucket: ENV.AWS_S3_BUCKET
            })
        });

        if (!response.ok) {
            throw new Error('Failed to initialize multipart upload');
        }

        return response.json();
    }

    /**
     * Get presigned URL for part upload
     */
    async getPresignedPartUrl(key, uploadId, partNumber) {
        const response = await fetch('/api/s3/multipart/part-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                key,
                uploadId,
                partNumber,
                bucket: ENV.AWS_S3_BUCKET
            })
        });

        if (!response.ok) {
            throw new Error('Failed to get part upload URL');
        }

        return response.json();
    }

    /**
     * Upload individual part
     */
    async uploadPart(chunk, uploadUrl, uploadId) {
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();

            xhr.addEventListener('load', () => {
                if (xhr.status === 200 || xhr.status === 204) {
                    resolve(xhr.getResponseHeader('ETag'));
                } else {
                    reject(new Error(`Part upload failed: ${xhr.statusText}`));
                }
            });

            xhr.addEventListener('error', () => {
                reject(new Error('Network error during part upload'));
            });

            xhr.open('PUT', uploadUrl);
            xhr.send(chunk);
        });
    }

    /**
     * Complete multipart upload
     */
    async completeMultipartUpload(key, uploadId, parts) {
        const response = await fetch('/api/s3/multipart/complete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                key,
                uploadId,
                parts,
                bucket: ENV.AWS_S3_BUCKET
            })
        });

        if (!response.ok) {
            throw new Error('Failed to complete multipart upload');
        }

        return response.json();
    }

    /**
     * Abort multipart upload
     */
    async abortMultipartUpload(key, uploadId) {
        try {
            await fetch('/api/s3/multipart/abort', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    key,
                    uploadId,
                    bucket: ENV.AWS_S3_BUCKET
                })
            });
        } catch (error) {
            console.error('Failed to abort multipart upload:', error);
        }
    }

    /**
     * Cancel an active upload
     */
    cancelUpload(uploadId) {
        const xhr = this.activeUploads.get(uploadId);
        if (xhr) {
            xhr.abort();
            this.activeUploads.delete(uploadId);
        }

        this.uploadProgress.set(uploadId, {
            bytesUploaded: 0,
            totalBytes: 0,
            percentage: 0,
            status: 'cancelled'
        });
    }

    /**
     * Get upload progress
     */
    getUploadProgress(uploadId) {
        return this.uploadProgress.get(uploadId) || {
            bytesUploaded: 0,
            totalBytes: 0,
            percentage: 0,
            status: 'idle'
        };
    }

    /**
     * Format bytes for display
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Calculate upload speed
     */
    calculateUploadSpeed(startTime, bytesUploaded) {
        const elapsedSeconds = (Date.now() - startTime) / 1000;
        const bytesPerSecond = bytesUploaded / elapsedSeconds;
        return this.formatBytes(bytesPerSecond) + '/s';
    }

    /**
     * Estimate time remaining
     */
    estimateTimeRemaining(bytesUploaded, totalBytes, startTime) {
        const elapsedSeconds = (Date.now() - startTime) / 1000;
        const uploadSpeed = bytesUploaded / elapsedSeconds;
        const remainingBytes = totalBytes - bytesUploaded;
        const remainingSeconds = remainingBytes / uploadSpeed;
        
        if (remainingSeconds < 60) {
            return `${Math.round(remainingSeconds)}s`;
        } else {
            return `${Math.round(remainingSeconds / 60)}m`;
        }
    }
}

// Export singleton instance
export const s3UploadService = new S3UploadService();