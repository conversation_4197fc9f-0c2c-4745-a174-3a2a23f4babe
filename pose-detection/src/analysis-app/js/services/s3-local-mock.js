/**
 * S3 Local Mock Service
 * Simulates S3 uploads for local development without AWS credentials
 */

import { ENV } from '../../config/env.js';

class S3LocalMockService {
    constructor() {
        this.mockUploads = new Map();
        this.mockDelay = 100; // Simulate network delay
    }

    /**
     * <PERSON><PERSON> presigned URL generation
     */
    async getMockPresignedUrl(key, contentType) {
        await this.simulateDelay();
        
        // In local dev, we'll store files in IndexedDB or memory
        const mockUrl = `blob:mock-upload-${Date.now()}`;
        const fileUrl = `https://${ENV.AWS_S3_BUCKET}.s3.amazonaws.com/${key}`;
        
        return {
            uploadUrl: mockUrl,
            fileUrl: fileUrl
        };
    }

    /**
     * Mock file upload
     */
    async mockUpload(file, uploadId, onProgress) {
        const reader = new FileReader();
        const totalSize = file.size;
        let uploaded = 0;

        return new Promise((resolve, reject) => {
            reader.onprogress = (event) => {
                if (event.lengthComputable) {
                    uploaded = event.loaded;
                    const progress = {
                        bytesUploaded: uploaded,
                        totalBytes: totalSize,
                        percentage: Math.round((uploaded / totalSize) * 100),
                        status: 'uploading'
                    };
                    if (onProgress) onProgress(progress);
                }
            };

            reader.onload = async () => {
                // Simulate upload time based on file size
                const uploadTime = Math.min(file.size / 1024 / 10, 5000); // Max 5 seconds
                await this.simulateDelay(uploadTime);

                // Store in mock storage
                this.mockUploads.set(uploadId, {
                    data: reader.result,
                    metadata: {
                        filename: file.name,
                        size: file.size,
                        type: file.type,
                        uploadedAt: new Date().toISOString()
                    }
                });

                resolve({
                    etag: `"mock-etag-${Date.now()}"`,
                    location: `mock://${uploadId}`
                });
            };

            reader.onerror = () => {
                reject(new Error('Failed to read file'));
            };

            // Read file as data URL for mock storage
            reader.readAsDataURL(file);
        });
    }

    /**
     * Simulate network delay
     */
    simulateDelay(ms = null) {
        const delay = ms || this.mockDelay + Math.random() * 100;
        return new Promise(resolve => setTimeout(resolve, delay));
    }

    /**
     * Get mock upload data
     */
    getMockUpload(uploadId) {
        return this.mockUploads.get(uploadId);
    }

    /**
     * Clear mock uploads
     */
    clearMockUploads() {
        this.mockUploads.clear();
    }
}

// Export singleton instance
export const s3LocalMock = new S3LocalMockService();

/**
 * Override fetch for local development
 * Intercepts S3 presigned URL requests and returns mock responses
 */
if (ENV.APP_ENV === 'development' && !ENV.ENABLE_S3_UPLOADS) {
    const originalFetch = window.fetch;
    
    window.fetch = async function(url, options) {
        // Intercept S3 presigned URL requests
        if (typeof url === 'string' && url.includes('/api/s3/')) {
            console.log('🔧 Intercepting S3 request for local mock:', url);
            
            const path = new URL(url, window.location.origin).pathname;
            const body = options?.body ? JSON.parse(options.body) : {};
            
            // Mock responses based on path
            if (path === '/api/s3/presigned-upload') {
                const mockResponse = await s3LocalMock.getMockPresignedUrl(body.key, body.contentType);
                return new Response(JSON.stringify(mockResponse), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            } else if (path === '/api/s3/multipart/init') {
                return new Response(JSON.stringify({ uploadId: `mock-upload-${Date.now()}` }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            } else if (path === '/api/s3/multipart/part-url') {
                return new Response(JSON.stringify({ uploadUrl: `blob:mock-part-${body.partNumber}` }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            } else if (path === '/api/s3/multipart/complete') {
                return new Response(JSON.stringify({
                    fileUrl: `https://${body.bucket}.s3.amazonaws.com/${body.key}`,
                    etag: `"mock-complete-${Date.now()}"`
                }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            } else if (path === '/api/s3/multipart/abort') {
                return new Response(JSON.stringify({ success: true }), {
                    status: 200,
                    headers: { 'Content-Type': 'application/json' }
                });
            }
        }
        
        // For all other requests, use original fetch
        return originalFetch.call(this, url, options);
    };
    
    console.log('🔧 S3 Local Mock Service enabled for development');
}