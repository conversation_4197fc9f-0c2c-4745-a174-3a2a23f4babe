/**
 * Supabase Client Service
 * Handles all database operations for MaxWattz
 */

import { ENV } from '../../config/env.js';

class SupabaseService {
    constructor() {
        this.client = null;
        this.initialized = false;
    }

    /**
     * Initialize Supabase client
     */
    async init() {
        if (this.initialized) return;

        try {
            // Dynamically import Supabase client
            const { createClient } = await import('https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2/+esm');
            
            this.client = createClient(ENV.SUPABASE_URL, ENV.SUPABASE_ANON_KEY);
            this.initialized = true;
            
            console.log('✅ Supabase client initialized');
        } catch (error) {
            console.error('❌ Failed to initialize Supabase:', error);
            throw error;
        }
    }

    /**
     * Create a new analysis session
     */
    async createAnalysisSession(userData) {
        await this.init();

        const { data, error } = await this.client
            .rpc('create_bio_run_analysis', {
                p_user_email: userData.email || '<EMAIL>',
                p_height_inches: userData.heightInches,
                p_height_display: userData.heightDisplay,
                p_gender: userData.gender,
                p_weight_lbs: userData.weightLbs,
                p_weight_display: userData.weightDisplay
            });

        if (error) throw error;
        return data[0]; // Returns {analysis_id, upload_session_id, session_token}
    }

    /**
     * Record video upload in database
     */
    async recordVideoUpload(videoData) {
        await this.init();

        const { data, error } = await this.client
            .from('bio_run_videos')
            .insert({
                filename: videoData.filename,
                original_filename: videoData.originalFilename,
                file_size_bytes: videoData.fileSize,
                mime_type: videoData.mimeType,
                s3_bucket: ENV.AWS_S3_BUCKET,
                s3_key: videoData.s3Key,
                s3_url: videoData.s3Url,
                s3_upload_completed: true,
                duration_seconds: videoData.duration,
                width: videoData.width,
                height: videoData.height,
                fps: videoData.fps,
                codec: videoData.codec,
                bitrate: videoData.bitrate,
                view_type: videoData.viewType,
                user_email: videoData.userEmail || '<EMAIL>',
                session_id: videoData.sessionId
            })
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * Update analysis with video reference
     */
    async updateAnalysisVideo(analysisId, viewType, videoId) {
        await this.init();

        const updateData = {};
        updateData[`${viewType}_video_id`] = videoId;

        const { data, error } = await this.client
            .from('bio_run_analysis')
            .update(updateData)
            .eq('id', analysisId)
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * Update upload session progress
     */
    async updateUploadProgress(sessionId, viewType, completed = false) {
        await this.init();

        const updateData = {
            [`${viewType}_video_uploaded`]: true,
            [`${viewType}_upload_completed_at`]: new Date().toISOString()
        };

        if (completed) {
            updateData.upload_completed = true;
        }

        const { data, error } = await this.client
            .from('bio_run_upload_sessions')
            .update(updateData)
            .eq('session_token', sessionId)
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * Queue video for Modal processing
     */
    async queueVideoProcessing(analysisId, videoId, viewType) {
        await this.init();

        const { data, error } = await this.client
            .rpc('queue_video_processing', {
                p_analysis_id: analysisId,
                p_video_id: videoId,
                p_view_type: viewType
            });

        if (error) throw error;
        return data;
    }

    /**
     * Get analysis status
     */
    async getAnalysisStatus(analysisId) {
        await this.init();

        const { data, error } = await this.client
            .from('bio_run_analysis')
            .select(`
                *,
                side_video:bio_run_videos!bio_run_analysis_side_video_id_fkey(*),
                rear_video:bio_run_videos!bio_run_analysis_rear_video_id_fkey(*),
                processing_queue:bio_modal_processing_queue(*)
            `)
            .eq('id', analysisId)
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * Update analysis status
     */
    async updateAnalysisStatus(analysisId, status, additionalData = {}) {
        await this.init();

        const updateData = {
            status,
            ...additionalData
        };

        if (status === 'processing_side' || status === 'processing_rear') {
            updateData.processing_started_at = new Date().toISOString();
        } else if (status === 'completed' || status === 'error') {
            updateData.processing_completed_at = new Date().toISOString();
        }

        const { data, error } = await this.client
            .from('bio_run_analysis')
            .update(updateData)
            .eq('id', analysisId)
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * Store processed results from Modal
     */
    async storeProcessedResults(analysisId, viewType, results) {
        await this.init();

        const updateData = {
            [`${viewType}_analysis_json`]: results.smoothedKeypoints,
            [`${viewType}_metrics_s3_url`]: results.s3MetricsUrl,
            [`${viewType}_detection_confidence`]: results.avgConfidence,
            [`${viewType}_frames_processed`]: results.framesProcessed
        };

        // Update summary metrics if this is the last view to process
        if (results.summaryMetrics) {
            Object.assign(updateData, {
                cadence_avg: results.summaryMetrics.cadence,
                stride_length_avg: results.summaryMetrics.strideLength,
                vertical_oscillation_avg: results.summaryMetrics.verticalOscillation,
                ground_contact_time_avg: results.summaryMetrics.groundContactTime,
                lean_angle_avg: results.summaryMetrics.leanAngle,
                analysis_summary: results.summaryMetrics.summary,
                key_findings: results.summaryMetrics.keyFindings
            });
        }

        const { data, error } = await this.client
            .from('bio_run_analysis')
            .update(updateData)
            .eq('id', analysisId)
            .select()
            .single();

        if (error) throw error;
        return data;
    }

    /**
     * Get recent analyses for user
     */
    async getUserAnalyses(userEmail, limit = 10) {
        await this.init();

        const { data, error } = await this.client
            .from('bio_run_active_analyses')
            .select('*')
            .eq('user_email', userEmail)
            .order('created_at', { ascending: false })
            .limit(limit);

        if (error) throw error;
        return data;
    }

    /**
     * Subscribe to analysis status changes
     */
    subscribeToAnalysisUpdates(analysisId, callback) {
        if (!this.initialized) {
            console.error('Supabase not initialized');
            return null;
        }

        const subscription = this.client
            .channel(`analysis-${analysisId}`)
            .on(
                'postgres_changes',
                {
                    event: 'UPDATE',
                    schema: 'public',
                    table: 'bio_run_analysis',
                    filter: `id=eq.${analysisId}`
                },
                (payload) => {
                    callback(payload.new);
                }
            )
            .subscribe();

        return subscription;
    }

    /**
     * Unsubscribe from updates
     */
    unsubscribe(subscription) {
        if (subscription) {
            this.client.removeChannel(subscription);
        }
    }

    /**
     * Health check
     */
    async healthCheck() {
        await this.init();

        try {
            const { data, error } = await this.client
                .from('bio_run_analysis')
                .select('count')
                .limit(1);

            return !error;
        } catch {
            return false;
        }
    }
}

// Export singleton instance
export const supabaseService = new SupabaseService();