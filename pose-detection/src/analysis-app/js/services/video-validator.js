/**
 * Video Validation Service
 * Validates video files before upload to ensure they meet requirements
 */

import { ENV } from '../../config/env.js';

class VideoValidatorService {
    constructor() {
        this.validationResults = new Map();
    }

    /**
     * Validate video file
     * Returns validation result with metadata
     */
    async validateVideo(file, viewType) {
        const validationId = `${file.name}-${Date.now()}`;
        
        try {
            console.log(`🎬 Validating ${viewType} video: ${file.name}`);
            
            // Basic file validation
            const basicValidation = this.validateBasicRequirements(file);
            if (!basicValidation.isValid) {
                return basicValidation;
            }

            // Extract video metadata
            const metadata = await this.extractVideoMetadata(file);
            
            // Detailed validation
            const validation = this.validateVideoRequirements(file, metadata);
            
            // Store validation result
            this.validationResults.set(validationId, {
                ...validation,
                metadata,
                timestamp: new Date().toISOString()
            });

            return validation;

        } catch (error) {
            console.error('❌ Video validation error:', error);
            return {
                isValid: false,
                errors: [{
                    type: 'extraction_error',
                    message: 'Failed to analyze video file',
                    details: error.message
                }],
                metadata: null
            };
        }
    }

    /**
     * Basic file validation (size, type)
     */
    validateBasicRequirements(file) {
        const errors = [];

        // Check file size
        const maxSizeBytes = ENV.MAX_VIDEO_SIZE_MB * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            errors.push({
                type: 'file_size',
                message: `File size exceeds ${ENV.MAX_VIDEO_SIZE_MB}MB limit`,
                details: `File size: ${this.formatFileSize(file.size)}, Limit: ${ENV.MAX_VIDEO_SIZE_MB}MB`
            });
        }

        // Check file type
        if (!ENV.SUPPORTED_VIDEO_FORMATS.includes(file.type)) {
            errors.push({
                type: 'file_type',
                message: 'Unsupported video format',
                details: `File type: ${file.type}, Supported: ${ENV.SUPPORTED_VIDEO_FORMATS.join(', ')}`
            });
        }

        return {
            isValid: errors.length === 0,
            errors,
            fileSize: file.size,
            fileType: file.type
        };
    }

    /**
     * Extract video metadata using HTML5 video element
     */
    async extractVideoMetadata(file) {
        return new Promise((resolve, reject) => {
            const video = document.createElement('video');
            const url = URL.createObjectURL(file);
            
            video.preload = 'metadata';
            video.muted = true;

            const cleanup = () => {
                URL.revokeObjectURL(url);
                video.remove();
            };

            video.onloadedmetadata = () => {
                const metadata = {
                    duration: video.duration,
                    width: video.videoWidth,
                    height: video.videoHeight,
                    aspectRatio: video.videoWidth / video.videoHeight,
                    // Estimate FPS (will be refined server-side)
                    fps: this.estimateFPS(video),
                    codec: this.detectCodec(file),
                    bitrate: this.estimateBitrate(file.size, video.duration),
                    // Additional metadata
                    fileSize: file.size,
                    fileName: file.name,
                    mimeType: file.type,
                    lastModified: new Date(file.lastModified).toISOString()
                };

                cleanup();
                resolve(metadata);
            };

            video.onerror = (error) => {
                cleanup();
                reject(new Error(`Failed to load video: ${error.message || 'Unknown error'}`));
            };

            // Set timeout for metadata extraction
            setTimeout(() => {
                cleanup();
                reject(new Error('Video metadata extraction timeout'));
            }, 10000); // 10 second timeout

            video.src = url;
        });
    }

    /**
     * Validate video against requirements
     */
    validateVideoRequirements(file, metadata) {
        const errors = [];
        const warnings = [];

        // Duration validation
        if (metadata.duration > ENV.MAX_VIDEO_DURATION_SECONDS) {
            errors.push({
                type: 'duration',
                message: `Video exceeds ${ENV.MAX_VIDEO_DURATION_SECONDS} second limit`,
                details: `Duration: ${metadata.duration.toFixed(1)}s, Limit: ${ENV.MAX_VIDEO_DURATION_SECONDS}s`
            });
        } else if (metadata.duration < 5) {
            warnings.push({
                type: 'duration',
                message: 'Video is shorter than recommended 5 seconds',
                details: `Duration: ${metadata.duration.toFixed(1)}s`
            });
        }

        // Resolution validation
        const is4K = metadata.width >= 3840 || metadata.height >= 2160;
        const is1080p = metadata.width >= 1920 || metadata.height >= 1080;
        const is720p = metadata.width >= 1280 || metadata.height >= 720;

        if (is4K && !ENV.ENABLE_4K) {
            errors.push({
                type: 'resolution',
                message: '4K videos are not currently supported',
                details: `Resolution: ${metadata.width}x${metadata.height}`
            });
        } else if (!is720p) {
            warnings.push({
                type: 'resolution',
                message: 'Video resolution is below recommended 720p',
                details: `Resolution: ${metadata.width}x${metadata.height}`
            });
        }

        // FPS validation
        if (metadata.fps > 60 && !ENV.ENABLE_60FPS) {
            errors.push({
                type: 'fps',
                message: 'Videos above 60fps are not currently supported',
                details: `Estimated FPS: ${metadata.fps}`
            });
        } else if (metadata.fps < 24) {
            warnings.push({
                type: 'fps',
                message: 'Video FPS is below recommended 24fps',
                details: `Estimated FPS: ${metadata.fps}`
            });
        }

        // Aspect ratio validation (for running videos, portrait is often better)
        if (metadata.aspectRatio > 2 || metadata.aspectRatio < 0.5) {
            warnings.push({
                type: 'aspect_ratio',
                message: 'Unusual aspect ratio detected',
                details: `Aspect ratio: ${metadata.aspectRatio.toFixed(2)}`
            });
        }

        // Bitrate validation
        const minBitrate = 2; // 2 Mbps minimum for quality
        const maxBitrate = 50; // 50 Mbps maximum to prevent huge files
        if (metadata.bitrate < minBitrate) {
            warnings.push({
                type: 'bitrate',
                message: 'Video bitrate is below recommended quality',
                details: `Bitrate: ${metadata.bitrate.toFixed(1)} Mbps`
            });
        } else if (metadata.bitrate > maxBitrate) {
            warnings.push({
                type: 'bitrate',
                message: 'Video bitrate is very high',
                details: `Bitrate: ${metadata.bitrate.toFixed(1)} Mbps`
            });
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            metadata,
            summary: {
                duration: `${metadata.duration.toFixed(1)}s`,
                resolution: `${metadata.width}x${metadata.height}`,
                fps: metadata.fps,
                size: this.formatFileSize(file.size),
                quality: this.assessVideoQuality(metadata)
            }
        };
    }

    /**
     * Estimate FPS from video
     */
    estimateFPS(video) {
        // This is a rough estimate, actual FPS will be determined server-side
        // Most modern videos are 30 or 60 fps
        const duration = video.duration;
        if (duration > 0) {
            // Assume standard frame rates
            if (video.videoHeight >= 1080) {
                return 30; // HD usually 30fps
            }
            return 30; // Default to 30fps
        }
        return ENV.DEFAULT_FPS;
    }

    /**
     * Detect video codec from file
     */
    detectCodec(file) {
        const type = file.type.toLowerCase();
        if (type.includes('mp4')) return 'H.264';
        if (type.includes('webm')) return 'VP8/VP9';
        if (type.includes('quicktime')) return 'H.264/HEVC';
        if (type.includes('x-msvideo')) return 'Various';
        return 'Unknown';
    }

    /**
     * Estimate bitrate from file size and duration
     */
    estimateBitrate(fileSize, duration) {
        if (duration <= 0) return 0;
        // Convert to Mbps
        const bits = fileSize * 8;
        const megabits = bits / (1024 * 1024);
        return megabits / duration;
    }

    /**
     * Assess overall video quality
     */
    assessVideoQuality(metadata) {
        let score = 0;
        let maxScore = 0;

        // Resolution score
        maxScore += 30;
        if (metadata.width >= 3840 || metadata.height >= 2160) score += 30; // 4K
        else if (metadata.width >= 1920 || metadata.height >= 1080) score += 25; // 1080p
        else if (metadata.width >= 1280 || metadata.height >= 720) score += 20; // 720p
        else score += 10;

        // FPS score
        maxScore += 20;
        if (metadata.fps >= 60) score += 20;
        else if (metadata.fps >= 30) score += 15;
        else if (metadata.fps >= 24) score += 10;
        else score += 5;

        // Bitrate score
        maxScore += 20;
        if (metadata.bitrate >= 8) score += 20;
        else if (metadata.bitrate >= 5) score += 15;
        else if (metadata.bitrate >= 2) score += 10;
        else score += 5;

        // Duration score (closer to 10s is better)
        maxScore += 30;
        const durationScore = Math.max(0, 30 - Math.abs(10 - metadata.duration) * 3);
        score += durationScore;

        const percentage = Math.round((score / maxScore) * 100);
        
        if (percentage >= 90) return 'Excellent';
        if (percentage >= 75) return 'Good';
        if (percentage >= 60) return 'Fair';
        return 'Poor';
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Get validation result
     */
    getValidationResult(validationId) {
        return this.validationResults.get(validationId);
    }

    /**
     * Clear validation results
     */
    clearValidationResults() {
        this.validationResults.clear();
    }

    /**
     * Create validation summary message
     */
    createValidationSummary(validation) {
        if (!validation.isValid) {
            const errorMessages = validation.errors.map(e => e.message).join(', ');
            return `❌ Validation failed: ${errorMessages}`;
        }

        const warnings = validation.warnings || [];
        if (warnings.length > 0) {
            const warningMessages = warnings.map(w => w.message).join(', ');
            return `⚠️ Video accepted with warnings: ${warningMessages}`;
        }

        return `✅ Video validated successfully - Quality: ${validation.summary.quality}`;
    }
}

// Export singleton instance
export const videoValidator = new VideoValidatorService();