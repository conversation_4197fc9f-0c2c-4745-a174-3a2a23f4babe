/**
 * MaxWattz - Upload Controller
 * Handles file upload, validation, and processing
 */

class UploadController {
    constructor() {
        this.uploadedFile = null;
        this.runnerData = null;
        this.previewURL = null;
        
        this.init();
    }

    /**
     * Initialize upload controller
     */
    init() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupEventListeners());
        } else {
            this.setupEventListeners();
        }
    }

    /**
     * Setup upload event listeners
     */
    setupEventListeners() {
        // File input change handler
        document.addEventListener('change', (e) => {
            if (e.target.id === 'videoInput') {
                this.handleFileSelect(e.target.files[0]);
            }
        });

        // Form validation handlers
        this.setupFormValidation();
        
        // Preview handlers
        this.setupPreviewHandlers();
    }

    /**
     * Handle file selection
     */
    handleFileSelect(file) {
        if (!file) return;

        console.log('📁 File selected:', {
            name: file.name,
            type: file.type,
            size: this.formatFileSize(file.size),
            lastModified: new Date(file.lastModified)
        });

        // Validate file
        if (!this.validateFile(file)) {
            return;
        }

        // Store file
        this.uploadedFile = file;
        this.createPreviewURL(file);
        
        // Update UI
        this.updateUploadAreaUI(file);
        
        // Enable form if not already enabled
        this.enableForm();
    }

    /**
     * Validate uploaded file
     */
    validateFile(file) {
        const validationResults = {
            type: this.validateFileType(file),
            size: this.validateFileSize(file),
            name: this.validateFileName(file)
        };

        const errors = [];

        if (!validationResults.type.valid) {
            errors.push(validationResults.type.message);
        }

        if (!validationResults.size.valid) {
            errors.push(validationResults.size.message);
        }

        if (!validationResults.name.valid) {
            errors.push(validationResults.name.message);
        }

        if (errors.length > 0) {
            this.showValidationErrors(errors);
            return false;
        }

        console.log('✅ File validation passed');
        return true;
    }

    /**
     * Validate file type
     */
    validateFileType(file) {
        const allowedTypes = [
            'video/mp4',
            'video/mov',
            'video/avi',
            'video/webm',
            'video/quicktime'
        ];

        const allowedExtensions = ['.mp4', '.mov', '.avi', '.webm'];
        const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));

        if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
            return {
                valid: false,
                message: 'Invalid file type. Please upload MP4, MOV, AVI, or WebM videos.'
            };
        }

        return { valid: true };
    }

    /**
     * Validate file size
     */
    validateFileSize(file) {
        const maxSize = 500 * 1024 * 1024; // 500MB
        const minSize = 1024 * 1024; // 1MB

        if (file.size > maxSize) {
            return {
                valid: false,
                message: `File too large (${this.formatFileSize(file.size)}). Maximum size is 500MB.`
            };
        }

        if (file.size < minSize) {
            return {
                valid: false,
                message: `File too small (${this.formatFileSize(file.size)}). Minimum size is 1MB.`
            };
        }

        return { valid: true };
    }

    /**
     * Validate file name
     */
    validateFileName(file) {
        const maxLength = 100;
        const invalidChars = /[<>:"/\\|?*]/;

        if (file.name.length > maxLength) {
            return {
                valid: false,
                message: 'File name too long. Please rename the file.'
            };
        }

        if (invalidChars.test(file.name)) {
            return {
                valid: false,
                message: 'File name contains invalid characters.'
            };
        }

        return { valid: true };
    }

    /**
     * Create preview URL for video
     */
    createPreviewURL(file) {
        // Revoke previous URL if exists
        if (this.previewURL) {
            URL.revokeObjectURL(this.previewURL);
        }

        this.previewURL = URL.createObjectURL(file);
        console.log('🎬 Preview URL created');
    }

    /**
     * Update upload area UI after file selection
     */
    updateUploadAreaUI(file) {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        uploadArea.innerHTML = `
            <div class="upload-success">
                <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="color: #38a169;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div class="upload-text">
                    <h3 style="color: #38a169;">Video Ready for Analysis</h3>
                    <p><strong>${file.name}</strong></p>
                    <p>Size: ${this.formatFileSize(file.size)} | Type: ${file.type}</p>
                    <button class="btn btn-secondary" id="changeVideoBtn" style="margin-top: 12px;">
                        Change Video
                    </button>
                </div>
            </div>
        `;

        // Add change video handler
        const changeBtn = document.getElementById('changeVideoBtn');
        if (changeBtn) {
            changeBtn.addEventListener('click', () => {
                this.resetUploadArea();
            });
        }
    }

    /**
     * Reset upload area to initial state
     */
    resetUploadArea() {
        const uploadArea = document.getElementById('uploadArea');
        if (!uploadArea) return;

        // Revoke preview URL
        if (this.previewURL) {
            URL.revokeObjectURL(this.previewURL);
            this.previewURL = null;
        }

        // Reset file data
        this.uploadedFile = null;
        
        // Reset file input
        const fileInput = document.getElementById('videoInput');
        if (fileInput) {
            fileInput.value = '';
        }

        // Restore original upload area HTML
        uploadArea.innerHTML = `
            <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            <div class="upload-text">
                <h3>Drop video file here or click to select</h3>
                <p>Supports MP4, MOV, AVI, WebM</p>
                <p class="file-types">Recommended: 1080p or higher, 30fps</p>
            </div>
            <input type="file" id="videoInput" accept="video/*" style="display: none;">
        `;
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        // Real-time validation for required fields
        const requiredFields = ['height'];
        
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => {
                    this.validateField(fieldId);
                });
                
                field.addEventListener('input', () => {
                    this.clearFieldError(fieldId);
                });
            }
        });
    }

    /**
     * Validate individual form field
     */
    validateField(fieldId) {
        const field = document.getElementById(fieldId);
        if (!field) return true;

        let isValid = true;
        let message = '';

        switch (fieldId) {
            case 'height':
                isValid = this.validateHeightField(field.value);
                message = 'Please enter height in format: 5\'10"';
                break;
            case 'weight':
                isValid = this.validateWeightField(field.value);
                message = 'Weight must be between 50-400 lbs';
                break;
        }

        if (!isValid) {
            this.showFieldError(field, message);
        } else {
            this.clearFieldError(fieldId);
        }

        return isValid;
    }

    /**
     * Validate height field
     */
    validateHeightField(value) {
        if (!value.trim()) return false;

        const patterns = [
            /^\d+'\d+"?$/,           // 5'10" or 5'10
            /^\d+ft\s*\d+in$/,       // 5ft 10in
            /^\d+'\s*\d+"?$/,        // 5' 10" or 5' 10
            /^\d+\.\d+ft$/,          // 5.8ft
            /^\d+cm$/                // 175cm
        ];

        return patterns.some(pattern => pattern.test(value.trim()));
    }

    /**
     * Validate weight field
     */
    validateWeightField(value) {
        if (!value) return true; // Optional field
        
        const weight = parseInt(value);
        return !isNaN(weight) && weight >= 50 && weight <= 400;
    }

    /**
     * Show field validation error
     */
    showFieldError(field, message) {
        field.style.borderColor = '#e53e3e';
        
        // Remove existing error
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = 'color: #e53e3e; font-size: 12px; margin-top: 4px;';
        errorDiv.textContent = message;
        
        field.parentNode.appendChild(errorDiv);
    }

    /**
     * Clear field validation error
     */
    clearFieldError(fieldId) {
        const field = document.getElementById(fieldId);
        if (!field) return;

        field.style.borderColor = '#e2e8f0';
        
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Setup preview handlers
     */
    setupPreviewHandlers() {
        // Video preview modal or embedded preview could go here
    }

    /**
     * Enable form after successful file upload
     */
    enableForm() {
        const form = document.querySelector('.runner-info');
        if (form) {
            form.style.opacity = '1';
            form.style.pointerEvents = 'auto';
        }
    }

    /**
     * Get upload data for analysis
     */
    getUploadData() {
        const height = document.getElementById('height')?.value;
        const gender = document.getElementById('gender')?.value;
        const weight = document.getElementById('weight')?.value;

        return {
            file: this.uploadedFile,
            previewURL: this.previewURL,
            runner: {
                height: height,
                gender: gender,
                weight: weight ? parseInt(weight) : null
            },
            timestamp: Date.now()
        };
    }

    /**
     * Check if upload is ready for processing
     */
    isReadyForProcessing() {
        return !!(this.uploadedFile && this.validateAllRequiredFields());
    }

    /**
     * Validate all required fields
     */
    validateAllRequiredFields() {
        const height = document.getElementById('height')?.value;
        
        // Height is required
        if (!height || !this.validateHeightField(height)) {
            return false;
        }

        return true;
    }

    /**
     * Show validation errors
     */
    showValidationErrors(errors) {
        // Remove existing errors
        const existingErrors = document.querySelectorAll('.upload-error');
        existingErrors.forEach(error => error.remove());

        // Create error container
        const errorContainer = document.createElement('div');
        errorContainer.className = 'upload-error error-message';
        errorContainer.innerHTML = errors.join('<br>');

        // Insert before upload area
        const uploadArea = document.getElementById('uploadArea');
        if (uploadArea && uploadArea.parentNode) {
            uploadArea.parentNode.insertBefore(errorContainer, uploadArea);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (errorContainer.parentNode) {
                    errorContainer.remove();
                }
            }, 5000);
        }
    }

    /**
     * Format file size for display
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.previewURL) {
            URL.revokeObjectURL(this.previewURL);
            this.previewURL = null;
        }
    }
}

// Initialize upload controller
const uploadController = new UploadController();

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    uploadController.cleanup();
});