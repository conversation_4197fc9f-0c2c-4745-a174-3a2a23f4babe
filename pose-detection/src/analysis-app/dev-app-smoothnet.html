<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal SmoothNet Analysis - Pre-processed Results</title>
    
    <!-- No TensorFlow.js needed - using pre-processed Modal results -->
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .video-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        }
        
        @media (max-width: 1024px) {
            .video-section {
                flex-direction: column;
            }
        }
        
        .video-container {
            flex: 0 0 calc(33.333% - 6.67px); /* Take exactly 1/3 minus proportional gap */
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 0;
        }
        
        .metrics-container {
            flex: 1;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        #videoElement {
            width: 100%;
            height: auto;
            display: block;
            margin: 0;
            border-radius: 8px;
        }
        
        #canvasElement {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            border-radius: 8px;
            /* Canvas size is set programmatically to match video exactly */
        }
        
        @media (max-width: 768px) {
            #videoElement {
                max-width: 90vw;
            }
        }
        
        .video-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            text-align: center;
        }
        
        .status.loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status.ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .metric-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-radius: 5px;
        }
        
        .controls button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
            font-size: 16px;
        }
        
        .controls button:hover {
            background: #0056b3;
        }
        
        .controls button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        
        .height-input-section {
            margin-bottom: 20px;
            padding: 15px;
            background: #f0f8ff;
            border-radius: 8px;
            border: 1px solid #007bff;
        }
        
        .height-input-section h3 {
            margin: 0 0 15px 0;
            color: #0056b3;
            font-size: 18px;
        }
        
        .height-input-wrapper {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .height-input-wrapper label {
            font-weight: 600;
            color: #333;
        }
        
        .imperial-inputs, .metric-inputs {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .imperial-inputs input, .metric-inputs input {
            width: 60px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
            text-align: center;
        }
        
        .imperial-inputs span, .metric-inputs span {
            font-size: 14px;
            color: #666;
            min-width: 20px;
        }
        
        .unit-toggle {
            padding: 8px 16px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        }
        
        .unit-toggle:hover {
            background: #5a6268;
        }
        
        .height-info {
            margin-top: 10px;
            padding: 8px;
            background: white;
            border-radius: 4px;
        }
        
        .height-info small {
            color: #666;
            font-size: 14px;
        }
        
        .height-info.set {
            background: #d4edda;
        }
        
        .height-info.set small {
            color: #155724;
            font-weight: 600;
        }
        
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .upload-area:hover {
            background: #f0f8ff;
            border-color: #0056b3;
        }
        
        .upload-area.dragover {
            background: #e7f3ff;
            border-color: #0056b3;
        }
        
        #debugInfo {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .keypoint-info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }
        
        .keypoint-card {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        
        .keypoint-card.high-confidence {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .keypoint-card.medium-confidence {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .keypoint-card.low-confidence {
            background: #f8d7da;
            border-color: #dc3545;
        }
        
        .pose-overlay-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏃‍♂️ Modal SmoothNet Analysis - Pre-processed Results</h1>
            <p>Displaying pre-processed pose data from Modal GPU pipeline with SmoothNet temporal smoothing</p>
        </div>
        
        <div class="controls">
            <div class="height-input-section">
                <h3>Runner Information</h3>
                <div class="height-input-wrapper">
                    <label>Height:</label>
                    <div class="imperial-inputs" id="imperialInputs">
                        <input type="number" id="heightFeet" min="3" max="8" placeholder="5">
                        <span>ft</span>
                        <input type="number" id="heightInches" min="0" max="11" placeholder="10">
                        <span>in</span>
                    </div>
                    <div class="metric-inputs" id="metricInputs" style="display: none;">
                        <input type="number" id="heightCm" min="90" max="250" placeholder="178">
                        <span>cm</span>
                    </div>
                    <button id="unitToggle" class="unit-toggle">Switch to Metric</button>
                </div>
                <div class="height-info" id="heightInfo">
                    <small id="heightDisplay">Height: Not set</small>
                </div>
            </div>
            
            <h3>Modal SmoothNet Results</h3>
            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #2196f3;">
                <p><strong>Pre-processed Data:</strong> S3: d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json (UPDATED SmoothNet - All 10 Issues Fixed)</p>
                <p><strong>Modal Video:</strong> <span id="modalVideoName">Loading...</span></p>
                <p><strong>Model:</strong> <span id="modalModelType">Loading...</span></p>
                <button onclick="loadVideoForModal()" style="background: #2196f3; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; margin-top: 8px;">Load Test Video</button>
            </div>
            
            <details style="margin-bottom: 15px;">
                <summary style="cursor: pointer; color: #666; font-size: 14px;">Upload Different Video (Optional)</summary>
                <div class="upload-area" id="uploadArea" style="margin-top: 10px;">
                    <p>📹 Drop video file here or click to select</p>
                    <p style="font-size: 14px; color: #666;">Supports MP4, MOV, AVI, WebM</p>
                    <input type="file" id="fileInput" accept="video/*" style="display: none;">
                </div>
            </details>
            
            <div id="videoControls" style="display: none;">
                <button id="playBtn">▶️ Play</button>
                <button id="pauseBtn">⏸️ Pause</button>
                <button id="resetBtn">🔄 Reset</button>
                <button id="processBtn">🎯 Process Frame</button>
            </div>
            
            <div class="status" id="status">Ready to load video...</div>
        </div>
        
        <div class="video-section">
            <div class="video-container">
                <div class="video-wrapper">
                    <video id="videoElement" style="display: none;"></video>
                    <canvas id="canvasElement"></canvas>
                    <div class="pose-overlay-info" id="poseInfo" style="display: none;">
                        <div>FPS: <span id="fps">0</span></div>
                        <div>Frame: <span id="currentFrame">0</span></div>
                        <div>Pose Score: <span id="poseScore">0</span>%</div>
                        <div>Keypoints: <span id="keypointCount">0</span>/33</div>
                        <div>SmoothNet: 3DPW-SPIN-3D</div>
                    </div>
                </div>
            </div>
            
            <div class="metrics-container">
                <h3>Modal SmoothNet Metrics</h3>
                <div class="metric-item">
                    <span>Modal Data Status:</span>
                    <span id="modelStatus">Not Loaded</span>
                </div>
                <div class="metric-item">
                    <span>Playback Stage:</span>
                    <span id="detectionStage">Idle</span>
                </div>
                <div class="metric-item">
                    <span>Frame Data:</span>
                    <span id="personDetected">No</span>
                </div>
                <div class="metric-item">
                    <span>Current Frame:</span>
                    <span id="currentFrameInfo">N/A</span>
                </div>
                <div class="metric-item">
                    <span>ROI Center:</span>
                    <span id="roiCenter">N/A</span>
                </div>
                <div class="metric-item">
                    <span>Avg Confidence:</span>
                    <span id="confidence">0%</span>
                </div>
                <div class="metric-item">
                    <span>Render Time:</span>
                    <span id="processingTime">0ms</span>
                </div>
                <div class="metric-item">
                    <span>Video Dimensions:</span>
                    <span id="videoDimensions">N/A</span>
                </div>
                
                <h4 style="margin-top: 20px;">Debug Information</h4>
                <div id="debugInfo"></div>
            </div>
        </div>
        
        <div class="video-container" style="margin-top: 20px;">
            <h3>Modal SmoothNet Keypoints (Filtered per SPEC)</h3>
            <p style="font-size: 14px; color: #666; margin-bottom: 15px;">
                <strong>Showing:</strong> Nose, Ears, Shoulders, Elbows, Wrists, Hips, Knees, Ankles, Heels, Toes<br>
                <strong>Hidden per SPEC:</strong> Eyes (1-6), Mouth (9-10), Fingers (17-22)<br>
                <strong>Source:</strong> Pre-processed Modal GPU pipeline with SmoothNet temporal smoothing
            </p>
            <div class="keypoint-info" id="keypointInfo">
                <!-- Keypoints will be populated here -->
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let modalData = null;  // Pre-processed Modal results
        let video = null;
        let canvas = null;
        let ctx = null;
        let isProcessing = false;
        let animationId = null;
        let lastFrameTime = 0;
        let lastFpsTime = 0;
        let frameCount = 0;
        let currentFrameData = null;
        let manualFrameIndex = 0;  // For manual frame advancement at 0.25x speed
        
        // User height configuration
        let userHeight = {
            cm: null,           // Always store in cm internally
            isSet: false,
            displayUnit: 'imperial'  // 'imperial' or 'metric'
        };
        
        // Coordinate scaler instance
        let coordinateScaler = null;
        
        // Conversion helpers
        const inchesToCm = (inches) => inches * 2.54;
        const feetInchesToCm = (feet, inches) => (feet * 12 + inches) * 2.54;
        const cmToFeetInches = (cm) => {
            const totalInches = cm / 2.54;
            const feet = Math.floor(totalInches / 12);
            const inches = Math.round(totalInches % 12);
            return { feet, inches };
        };
        const cmToInches = (cm) => cm / 2.54;
        const cmToFeet = (cm) => cm / 30.48;
        
        // Coordinate Scaler Class
        class CoordinateScaler {
            constructor(userHeightCm) {
                this.userHeightCm = userHeightCm;
                this.pixelsPerCm = null;
                this.calibrated = false;
                this.calibrationAttempts = 0;
                this.maxCalibrationAttempts = 150; // ~5 seconds at 30fps
            }
            
            calibrate(pose) {
                // Use nose (0) and hip midpoint (23,24) for calibration
                const nose = pose.keypoints[0];
                const leftHip = pose.keypoints[23];
                const rightHip = pose.keypoints[24];
                
                if (nose.score > 0.5 && leftHip.score > 0.5 && rightHip.score > 0.5) {
                    // Calculate hip midpoint
                    const hipMidpoint = {
                        x: (leftHip.x + rightHip.x) / 2,
                        y: (leftHip.y + rightHip.y) / 2
                    };
                    
                    // Vertical distance in pixels
                    const pixelDistance = Math.abs(nose.y - hipMidpoint.y);
                    
                    // Check if person is reasonably upright (nose above hips)
                    if (nose.y < hipMidpoint.y && pixelDistance > 50) {
                        // Assume nose-to-hip is ~38% of height
                        const estimatedDistanceCm = this.userHeightCm * 0.38;
                        
                        // Calculate scaling factor
                        this.pixelsPerCm = pixelDistance / estimatedDistanceCm;
                        this.calibrated = true;
                        
                        log(`Calibration successful: ${this.pixelsPerCm.toFixed(2)} pixels/cm`, 'info');
                        updateStatus('Height calibration complete', 'ready');
                        
                        // Update UI to show calibration status
                        this.updateCalibrationDisplay();
                        
                        return true;
                    }
                }
                
                this.calibrationAttempts++;
                if (this.calibrationAttempts >= this.maxCalibrationAttempts) {
                    log('Calibration failed after maximum attempts', 'error');
                    updateStatus('Height calibration failed - please ensure person is standing upright', 'error');
                }
                
                return false;
            }
            
            scaleDistance(pixelDistance) {
                if (!this.calibrated) return null;
                return pixelDistance / this.pixelsPerCm; // Returns cm
            }
            
            formatDistance(cm) {
                if (userHeight.displayUnit === 'imperial') {
                    const inches = cmToInches(cm);
                    if (inches < 12) {
                        return `${inches.toFixed(1)}"`;
                    } else {
                        const feet = Math.floor(inches / 12);
                        const remainingInches = (inches % 12).toFixed(1);
                        return `${feet}'${remainingInches}"`;
                    }
                } else {
                    return `${cm.toFixed(1)} cm`;
                }
            }
            
            getScaledKeypoints(keypoints) {
                if (!this.calibrated) return keypoints;
                
                // Return keypoints with real-world measurements
                return keypoints.map(kp => ({
                    ...kp,
                    realWorldX: this.scaleDistance(kp.x),
                    realWorldY: this.scaleDistance(kp.y)
                }));
            }
            
            updateCalibrationDisplay() {
                // Add calibration info to metrics
                const calibrationMetric = document.getElementById('calibrationStatus');
                if (calibrationMetric) {
                    calibrationMetric.textContent = this.calibrated ? 
                        `✅ Calibrated (1cm = ${this.pixelsPerCm.toFixed(1)}px)` : 
                        '🔄 Calibrating...';
                }
            }
            
            reset() {
                this.calibrated = false;
                this.calibrationAttempts = 0;
                this.pixelsPerCm = null;
            }
        }
        
        // DOM elements
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const videoElement = document.getElementById('videoElement');
        const canvasElement = document.getElementById('canvasElement');
        const status = document.getElementById('status');
        const videoControls = document.getElementById('videoControls');
        const debugInfo = document.getElementById('debugInfo');
        
        // Height input functions
        function validateAndSetHeight() {
            if (userHeight.displayUnit === 'imperial') {
                const feet = parseInt(document.getElementById('heightFeet').value) || 0;
                const inches = parseInt(document.getElementById('heightInches').value) || 0;
                
                if (feet < 3 || feet > 8 || inches < 0 || inches > 11) {
                    updateStatus('Please enter a valid height (3-8 feet)', 'error');
                    return false;
                }
                
                userHeight.cm = feetInchesToCm(feet, inches);
            } else {
                const cm = parseInt(document.getElementById('heightCm').value) || 0;
                
                if (cm < 90 || cm > 250) {
                    updateStatus('Please enter a valid height (90-250 cm)', 'error');
                    return false;
                }
                
                userHeight.cm = cm;
            }
            
            userHeight.isSet = true;
            updateHeightDisplay();
            log(`Height set: ${userHeight.cm} cm`, 'info');
            
            // Create new scaler instance
            coordinateScaler = new CoordinateScaler(userHeight.cm);
            
            return true;
        }
        
        function updateHeightDisplay() {
            const heightInfo = document.getElementById('heightInfo');
            const heightDisplay = document.getElementById('heightDisplay');
            
            if (userHeight.isSet) {
                if (userHeight.displayUnit === 'imperial') {
                    const { feet, inches } = cmToFeetInches(userHeight.cm);
                    heightDisplay.textContent = `Height: ${feet}'${inches}" (${Math.round(userHeight.cm)} cm)`;
                } else {
                    const { feet, inches } = cmToFeetInches(userHeight.cm);
                    heightDisplay.textContent = `Height: ${Math.round(userHeight.cm)} cm (${feet}'${inches}")`;
                }
                heightInfo.classList.add('set');
            } else {
                heightDisplay.textContent = 'Height: Not set';
                heightInfo.classList.remove('set');
            }
        }
        
        function toggleUnits() {
            const imperialInputs = document.getElementById('imperialInputs');
            const metricInputs = document.getElementById('metricInputs');
            const unitToggle = document.getElementById('unitToggle');
            
            if (userHeight.displayUnit === 'imperial') {
                userHeight.displayUnit = 'metric';
                imperialInputs.style.display = 'none';
                metricInputs.style.display = 'flex';
                unitToggle.textContent = 'Switch to Imperial';
                
                // If height is set, populate metric field
                if (userHeight.isSet) {
                    document.getElementById('heightCm').value = Math.round(userHeight.cm);
                }
            } else {
                userHeight.displayUnit = 'imperial';
                imperialInputs.style.display = 'flex';
                metricInputs.style.display = 'none';
                unitToggle.textContent = 'Switch to Metric';
                
                // If height is set, populate imperial fields
                if (userHeight.isSet) {
                    const { feet, inches } = cmToFeetInches(userHeight.cm);
                    document.getElementById('heightFeet').value = feet;
                    document.getElementById('heightInches').value = inches;
                }
            }
        }
        
        // Initialize height input event listeners
        function initializeHeightInputs() {
            // Unit toggle
            document.getElementById('unitToggle').addEventListener('click', toggleUnits);
            
            // Imperial inputs
            document.getElementById('heightFeet').addEventListener('change', validateAndSetHeight);
            document.getElementById('heightInches').addEventListener('change', validateAndSetHeight);
            
            // Metric input
            document.getElementById('heightCm').addEventListener('change', validateAndSetHeight);
            
            // Enter key support
            ['heightFeet', 'heightInches', 'heightCm'].forEach(id => {
                document.getElementById(id).addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        validateAndSetHeight();
                    }
                });
            });
        }
        
        // Load Modal processed data
        async function loadModalData() {
            try {
                updateStatus('Loading Modal SmoothNet processed data...', 'loading');
                log('Fetching Modal pre-processed results...');
                
                // Load the latest Modal results from local file (downloaded from S3)
                // Using results WITH UPDATED SmoothNet - Fixed coordinate issues + 3DPW-SPIN-3D model
                const localFile = './d9539cf3-e5ef-4989-b2e3-8021dfe1bea1_pose.json';
                log(`Loading Modal results from: ${localFile}`);
                const response = await fetch(localFile);
                if (!response.ok) {
                    throw new Error(`Failed to load JSON: ${response.status} ${response.statusText}`);
                }
                
                modalData = await response.json();
                
                log('Modal data loaded successfully', 'info', {
                    video: modalData.video,
                    dimensions: `${modalData.videoWidth}×${modalData.videoHeight}`,
                    fps: modalData.fps,
                    frames: modalData.frames.length,
                    modelType: modalData.modelType,
                    processingTime: modalData.processingTime
                });
                
                updateStatus('Modal SmoothNet data loaded successfully', 'ready');
                document.getElementById('modelStatus').textContent = `${modalData.modelType} - ${modalData.frames.length} frames processed`;
                
                // Update UI with Modal data info
                document.getElementById('modalVideoName').textContent = modalData.video;
                document.getElementById('modalModelType').textContent = modalData.modelType;
                
                return modalData;
                
            } catch (error) {
                updateStatus(`Failed to load Modal data: ${error.message}`, 'error');
                log(`Modal data loading error: ${error.message}`, 'error');
                document.getElementById('modelStatus').textContent = 'Failed to Load';
                throw error;
            }
        }
        
        // Find frame data for current video time
        function getValidFrames() {
            if (!modalData || !modalData.frames) return [];
            
            // Skip invalid frames (like frame 0 with all zeros)
            return modalData.frames.filter(frame => {
                // Check if frame has at least some valid coordinates (not all zeros)
                const validKeypoints = frame.keypoints.filter(kp => kp.x !== 0.0 || kp.y !== 0.0);
                return validKeypoints.length > 5; // Need at least 5 non-zero keypoints
            });
        }
        
        function findFrameForTime(currentTime) {
            const validFrames = getValidFrames();
            if (validFrames.length === 0) {
                log('No valid frames found in Modal data', 'error');
                return null;
            }
            
            // Find frame closest to current video time from valid frames only
            let closestFrame = null;
            let minDiff = Infinity;
            
            for (const frame of validFrames) {
                const diff = Math.abs(frame.timestamp - currentTime);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestFrame = frame;
                }
            }
            
            return closestFrame;
        }
        
        function findFrameByIndex(index) {
            const validFrames = getValidFrames();
            if (validFrames.length === 0) return null;
            
            // Clamp index to valid range
            const clampedIndex = Math.max(0, Math.min(index, validFrames.length - 1));
            return validFrames[clampedIndex];
        }
        
        // Filter out invalid keypoints (with coordinates at 0,0)
        function getValidKeypoints(keypoints) {
            return keypoints.map((kp, idx) => {
                // If both X and Y are 0, this is likely invalid data
                // Use interpolation or skip this keypoint
                if (kp.x === 0.0 && kp.y === 0.0) {
                    return {
                        ...kp,
                        x: 0.0,
                        y: 0.0,
                        score: 0.0, // Mark as invalid by setting score to 0
                        isInvalid: true
                    };
                }
                
                // If only X is 0 (but Y is valid), this might be edge detection issue
                if (kp.x === 0.0 && kp.y > 0.0) {
                    return {
                        ...kp,
                        score: Math.max(0.1, kp.score * 0.5), // Reduce confidence
                        isEdgeCase: true
                    };
                }
                
                // If only Y is 0 (but X is valid), this might be edge detection issue  
                if (kp.y === 0.0 && kp.x > 0.0) {
                    return {
                        ...kp,
                        score: Math.max(0.1, kp.score * 0.5), // Reduce confidence
                        isEdgeCase: true
                    };
                }
                
                // Valid coordinate
                return kp;
            });
        }
        
        // Initialize
        async function initialize() {
            video = videoElement;
            canvas = canvasElement;
            ctx = canvas.getContext('2d');
            
            // Initialize height input system
            initializeHeightInputs();
            
            try {
                // Load Modal pre-processed data instead of initializing TensorFlow
                await loadModalData();
                
                updateStatus('Ready to load video for playback', 'ready');
                log('Initialization complete - Modal data loaded');
                
            } catch (error) {
                updateStatus(`Initialization failed: ${error.message}`, 'error');
                log(`Initialization error: ${error.message}`, 'error');
            }
        }
        
        // Update status
        function updateStatus(message, type = 'info') {
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        // JSON-based logging for better debugging
        function log(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                type,
                message,
                data
            };
            
            // Console log as JSON for better parsing
            console.log(JSON.stringify(logEntry));
            
            // Update debug info with formatted output
            const displayEntry = data ? 
                `[${timestamp}] ${message} - ${JSON.stringify(data, null, 2)}` :
                `[${timestamp}] ${message}`;
                
            debugInfo.innerHTML = `<pre>${displayEntry}</pre>${debugInfo.innerHTML}`;
            
            // Keep only last 10 entries
            const entries = debugInfo.querySelectorAll('pre');
            if (entries.length > 10) {
                for (let i = 10; i < entries.length; i++) {
                    entries[i].remove();
                }
            }
        }
        
        // File handling
        uploadArea.addEventListener('click', () => fileInput.click());
        
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const file = e.dataTransfer.files[0];
            handleFile(file);
        });
        
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            handleFile(file);
        });
        
        function handleFile(file) {
            if (!file || !file.type.startsWith('video/')) {
                alert('Please select a valid video file');
                return;
            }
            
            const url = URL.createObjectURL(file);
            loadVideoForModal(url);
        }
        
        // Load video that matches Modal processing results
        function loadVideoForModal(videoUrl = null) {
            if (!modalData) {
                updateStatus('Modal data not loaded yet. Please wait...', 'error');
                return;
            }
            
            // Use the video file that matches the Modal processing
            // Based on test-results-2.json, this should be Michael_test_2.MOV (NEW - ALL 10 ISSUES FIXED)
            // But we need to find the corresponding video file
            let videoSrc;
            if (videoUrl) {
                videoSrc = videoUrl; // User uploaded file
            } else {
                // Try to find the matching video file
                // Modal processed "Michael_test_2.MOV" but we have "Michael_test_side.mp4"
                // For testing purposes, use the available video
                videoSrc = './Michael_test_side.mp4';
                
                log('Loading video for Modal results', 'info', {
                    modalVideo: modalData.video,
                    loadingVideo: videoSrc,
                    note: 'Using available video file - may not match perfectly'
                });
            }
            
            video.src = videoSrc;
            video.style.display = 'block';
            videoControls.style.display = 'block';
            
            video.addEventListener('loadedmetadata', () => {
                syncCanvasWithVideo();
                
                // Set video to 0.25x playback speed to match pose overlay timing
                video.playbackRate = 0.25;
                log('Video playback rate set to 0.25x to synchronize with pose overlay');
                
                // Validate video dimensions against Modal data
                const videoDimensions = `${video.videoWidth}×${video.videoHeight}`;
                const modalDimensions = `${modalData.videoWidth}×${modalData.videoHeight}`;
                
                if (video.videoWidth !== modalData.videoWidth || video.videoHeight !== modalData.videoHeight) {
                    log('Video dimensions mismatch', 'error', {
                        video: videoDimensions,
                        modal: modalDimensions,
                        issue: 'Coordinate scaling will be incorrect'
                    });
                    
                    // Show prominent warning
                    updateStatus(`❌ CRITICAL: Video dimensions (${videoDimensions}) don't match Modal data (${modalDimensions}). Coordinates will be wrong!`, 'error');
                    
                    // Add warning overlay on video
                    setTimeout(() => {
                        if (ctx) {
                            ctx.fillStyle = 'rgba(255, 0, 0, 0.9)';
                            ctx.fillRect(0, 0, canvas.width, 100);
                            ctx.fillStyle = 'white';
                            ctx.font = 'bold 16px Arial';
                            ctx.fillText('❌ DIMENSION MISMATCH WARNING', 10, 25);
                            ctx.font = '14px Arial';
                            ctx.fillText(`Video: ${videoDimensions} vs Modal: ${modalDimensions}`, 10, 45);
                            ctx.fillText('Need to use Michael_test_2.MOV for accurate results', 10, 65);
                            ctx.fillText('Current coordinates will be scaled incorrectly', 10, 85);
                        }
                    }, 100);
                } else {
                    updateStatus('✅ Video loaded and dimensions match Modal data. Click play to view SmoothNet results.', 'ready');
                }
            });
            
            // Also sync on resize
            window.addEventListener('resize', syncCanvasWithVideo);
        }
        
        function syncCanvasWithVideo() {
            if (!video || !canvas) return;
            
            // Get the actual displayed size of the video element
            const videoRect = video.getBoundingClientRect();
            
            // Set canvas to match video display size exactly
            canvas.width = videoRect.width;
            canvas.height = videoRect.height;
            
            // Set canvas CSS size to match video exactly
            canvas.style.width = videoRect.width + 'px';
            canvas.style.height = videoRect.height + 'px';
            
            log(`Video/Canvas synced: Display ${videoRect.width}x${videoRect.height}, Video res: ${video.videoWidth}x${video.videoHeight}`, 'info', {
                videoRes: { width: video.videoWidth, height: video.videoHeight },
                displaySize: { width: videoRect.width, height: videoRect.height }
            });
        }
        
        // Video controls
        document.getElementById('playBtn').addEventListener('click', () => {
            video.play();
            startProcessing();
        });
        
        document.getElementById('pauseBtn').addEventListener('click', () => {
            video.pause();
            stopProcessing();
        });
        
        document.getElementById('resetBtn').addEventListener('click', () => {
            video.currentTime = 0;
            stopProcessing();
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Reset calibration when video resets
            if (coordinateScaler) {
                coordinateScaler.reset();
                document.getElementById('calibrationStatus').textContent = 'Not started';
            }
        });
        
        document.getElementById('processBtn').addEventListener('click', async () => {
            await processFrame();
        });
        
        // Processing
        function startProcessing() {
            isProcessing = true;
            processLoop();
        }
        
        function stopProcessing() {
            isProcessing = false;
            if (animationId) {
                cancelAnimationFrame(animationId);
            }
        }
        
        async function processLoop() {
            if (!isProcessing || video.paused || video.ended) {
                return;
            }
            
            // Limit frame rate to ~30fps for smooth rendering
            const now = performance.now();
            if (now - lastFrameTime < 33) { // ~30fps = 33ms between frames
                animationId = requestAnimationFrame(processLoop);
                return;
            }
            
            await processFrame();
            lastFrameTime = now;
            
            if (isProcessing) {
                animationId = requestAnimationFrame(processLoop);
            }
        }
        
        async function processFrame() {
            const startTime = performance.now();
            
            try {
                // Check if Modal data is loaded
                if (!modalData) {
                    log('Modal data not loaded - skipping frame processing', 'error');
                    document.getElementById('detectionStage').textContent = 'No Modal Data';
                    return;
                }
                
                // Check if video is valid
                if (!video || video.readyState < 2) {
                    log('Video not ready - skipping frame processing');
                    document.getElementById('detectionStage').textContent = 'Video Not Ready';
                    return;
                }
                
                // Update detection stage
                document.getElementById('detectionStage').textContent = 'Loading Frame...';
                
                // Clear canvas and draw video frame
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
                
                // Find frame data for current video time (video is now at 0.25x speed)
                currentFrameData = findFrameForTime(video.currentTime);
                
                if (currentFrameData && currentFrameData.keypoints) {
                    // Filter out invalid keypoints first
                    const processedKeypoints = getValidKeypoints(currentFrameData.keypoints);
                    const frameDataWithValidKps = {
                        ...currentFrameData,
                        keypoints: processedKeypoints
                    };
                    
                    // Count valid keypoints (not the invalid ones we marked)
                    const validKeypoints = processedKeypoints.filter(kp => !kp.isInvalid && kp.score > 0.3);
                    const invalidKeypoints = processedKeypoints.filter(kp => kp.isInvalid).length;
                    
                    if (validKeypoints.length < 5) {
                        document.getElementById('personDetected').textContent = `Invalid (${invalidKeypoints} bad coordinates)`;
                        document.getElementById('confidence').textContent = '0%';
                        document.getElementById('roiCenter').textContent = 'Invalid Data';
                        
                        // Draw error message on canvas
                        ctx.fillStyle = 'rgba(255, 0, 0, 0.8)';
                        ctx.fillRect(10, 10, 300, 60);
                        ctx.fillStyle = 'white';
                        ctx.font = '16px Arial';
                        ctx.fillText('⚠️ Invalid Modal Data', 20, 30);
                        ctx.fillText(`${invalidKeypoints} keypoints at (0,0)`, 20, 50);
                        
                        return;
                    }
                    
                    document.getElementById('personDetected').textContent = `Yes (${validKeypoints.length} valid, ${invalidKeypoints} invalid)`;
                    
                    // Calculate average confidence from VALID keypoints only
                    const avgConfidence = validKeypoints.reduce((sum, kp) => sum + kp.score, 0) / validKeypoints.length;
                    document.getElementById('confidence').textContent = `${(avgConfidence * 100).toFixed(1)}%`;
                    
                    // Calculate ROI center from VALID keypoints only (with proper scaling)
                    // Modal coordinates are in actual video resolution, need to scale to canvas display size
                    const scaleX = canvas.width / modalData.videoWidth;
                    const scaleY = canvas.height / modalData.videoHeight;
                    
                    let centerX = 0, centerY = 0;
                    let validCenterKeypoints = 0;
                    
                    validKeypoints.forEach(keypoint => {
                        centerX += keypoint.x * scaleX;
                        centerY += keypoint.y * scaleY;
                        validCenterKeypoints++;
                    });
                    
                    if (validCenterKeypoints > 0) {
                        centerX /= validCenterKeypoints;
                        centerY /= validCenterKeypoints;
                        document.getElementById('roiCenter').textContent = 
                            `(${Math.round(centerX)}, ${Math.round(centerY)}) [${validCenterKeypoints} pts]`;
                    }
                    
                    // Draw pose overlay from Modal data (with valid keypoints)
                    drawModalPose(frameDataWithValidKps);
                    
                    // Update pose info
                    document.getElementById('poseInfo').style.display = 'block';
                    document.getElementById('poseScore').textContent = (avgConfidence * 100).toFixed(1);
                    document.getElementById('keypointCount').textContent = 
                        `${validKeypoints.length}/${processedKeypoints.length}`;
                    document.getElementById('currentFrame').textContent = currentFrameData.frameNumber;
                    
                    // Update current frame info in metrics
                    document.getElementById('currentFrameInfo').textContent = 
                        `${currentFrameData.frameNumber} @ ${currentFrameData.timestamp.toFixed(2)}s (${invalidKeypoints} invalid)`;
                    
                    // Update video dimensions info
                    if (frameCount === 1) { // Only update once
                        const dimMismatch = (video.videoWidth !== modalData.videoWidth || video.videoHeight !== modalData.videoHeight) ? ' ⚠️ MISMATCH' : '';
                        document.getElementById('videoDimensions').textContent = 
                            `Modal: ${modalData.videoWidth}×${modalData.videoHeight}, Video: ${video.videoWidth}×${video.videoHeight}${dimMismatch}`;
                    }
                    
                    // Update keypoint details (show processed keypoints with invalid markings)
                    updateKeypointInfo(processedKeypoints);
                    
                    // Log frame info occasionally
                    if (frameCount % 30 === 0) {
                        log(`Modal frame displayed: Frame ${currentFrameData.frameNumber}, Time ${currentFrameData.timestamp.toFixed(2)}s`);
                    }
                    
                } else {
                    document.getElementById('personDetected').textContent = 'No Modal Data';
                    document.getElementById('confidence').textContent = '0%';
                    document.getElementById('roiCenter').textContent = 'N/A';
                    document.getElementById('poseInfo').style.display = 'none';
                    
                    if (frameCount % 60 === 0) { // Less frequent logging for missing frames
                        log(`No Modal data for time ${video.currentTime.toFixed(2)}s`);
                    }
                }
                
                // Update metrics
                const processingTime = performance.now() - startTime;
                document.getElementById('processingTime').textContent = `${processingTime.toFixed(1)}ms`;
                document.getElementById('detectionStage').textContent = 'Modal Display';
                
                // Calculate FPS
                frameCount++;
                if (frameCount % 30 === 0) {
                    const currentTime = performance.now();
                    if (lastFpsTime > 0) {
                        const fps = 1000 / ((currentTime - lastFpsTime) / 30);
                        document.getElementById('fps').textContent = fps.toFixed(1);
                    }
                    lastFpsTime = currentTime;
                }
                
            } catch (error) {
                log(`Processing error: ${error.message}`, 'error');
                document.getElementById('detectionStage').textContent = 'Error';
            }
        }
        
        // Draw pose from Modal data
        function drawModalPose(frameData) {
            const minConfidence = 0.3;
            
            // Skip filtered keypoints per SPEC: Eyes (1-6), Mouth (9-10), Fingers (17-22)
            const skipKeypoints = [
                1, 2, 3, 4, 5, 6,  // eyes
                9, 10,             // mouth  
                17, 18, 19, 20, 21, 22  // fingers
            ];
            
            // Scale coordinates from Modal data to canvas display size
            // Modal data: 1080x1920, Canvas display: ~353x628 (9:16 ratio maintained)
            const scaleX = canvas.width / modalData.videoWidth;
            const scaleY = canvas.height / modalData.videoHeight;
            
            let drawnKeypoints = 0;
            let invalidKeypoints = 0;
            
            // Draw keypoints (excluding the ones we want to skip)
            frameData.keypoints.forEach((keypoint, idx) => {
                if (skipKeypoints.includes(idx)) return; // Skip filtered keypoints
                
                // Check for invalid coordinates first
                if (keypoint.isInvalid) {
                    invalidKeypoints++;
                    return;
                }
                
                if (keypoint.score > minConfidence) {
                    // Scale coordinates
                    const x = keypoint.x * scaleX;
                    const y = keypoint.y * scaleY;
                    
                    // Color based on confidence and validity
                    let color;
                    if (keypoint.isEdgeCase) {
                        color = '#ff8800'; // Orange for edge cases (partial coordinates)
                    } else if (keypoint.score > 0.8) {
                        color = '#00ff00'; // Green for high confidence
                    } else if (keypoint.score > 0.5) {
                        color = '#ffaa00'; // Orange for medium confidence
                    } else {
                        color = '#ff0000'; // Red for low confidence
                    }
                    
                    // Draw keypoint with outline for better visibility
                    ctx.fillStyle = color;
                    ctx.strokeStyle = keypoint.isEdgeCase ? 'red' : 'white';
                    ctx.lineWidth = keypoint.isEdgeCase ? 3 : 2;
                    ctx.beginPath();
                    ctx.arc(x, y, keypoint.isEdgeCase ? 8 : 6, 0, 2 * Math.PI);
                    ctx.fill();
                    ctx.stroke();
                    
                    // Draw keypoint index for debugging
                    ctx.fillStyle = keypoint.isEdgeCase ? 'red' : 'white';
                    ctx.font = '10px Arial';
                    ctx.fillText(keypoint.isEdgeCase ? `${idx}!` : idx.toString(), x + 6, y - 6);
                    
                    drawnKeypoints++;
                }
            });
            
            // Draw data quality indicator
            if (invalidKeypoints > 0) {
                ctx.fillStyle = 'rgba(255, 0, 0, 0.9)';
                ctx.fillRect(10, canvas.height - 80, 300, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText(`⚠️ ${invalidKeypoints} invalid coordinates (0,0)`, 20, canvas.height - 60);
            }
            
            // Draw skeleton connections
            drawModalSkeleton(frameData.keypoints, scaleX, scaleY);
            
            // Draw bounding box around person
            drawModalBoundingBox(frameData.keypoints, scaleX, scaleY);
            
            // Draw SmoothNet indicator
            ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
            ctx.fillRect(canvas.width - 220, 10, 210, 30);
            ctx.fillStyle = 'white';
            ctx.font = '14px Arial';
            ctx.fillText('🔄 SmoothNet 3DPW-SPIN-3D', canvas.width - 210, 30);
        }
        
        function displayRunningMetrics(pose) {
            if (!coordinateScaler || !coordinateScaler.calibrated) return;
            
            const keypoints = pose.keypoints;
            
            // Calculate stride length (left heel to right heel)
            const leftHeel = keypoints[29];
            const rightHeel = keypoints[30];
            
            if (leftHeel.score > 0.3 && rightHeel.score > 0.3) {
                const stridePixels = Math.sqrt(
                    Math.pow(leftHeel.x - rightHeel.x, 2) + 
                    Math.pow(leftHeel.y - rightHeel.y, 2)
                );
                const strideCm = coordinateScaler.scaleDistance(stridePixels);
                
                if (strideCm) {
                    // Display stride length in overlay
                    ctx.save();
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    ctx.fillRect(10, canvas.height - 40, 200, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.fillText(`Stride: ${coordinateScaler.formatDistance(strideCm)}`, 20, canvas.height - 20);
                    ctx.restore();
                }
            }
            
            // Calculate vertical displacement (for step height)
            const leftAnkle = keypoints[27];
            const rightAnkle = keypoints[28];
            
            if (leftAnkle.score > 0.3 && rightAnkle.score > 0.3) {
                // This would track over time for actual step height
                // For now, show ankle separation
                const ankleDistancePixels = Math.abs(leftAnkle.y - rightAnkle.y);
                const ankleDistanceCm = coordinateScaler.scaleDistance(ankleDistancePixels);
                
                if (ankleDistanceCm > 5) { // Only show if significant
                    ctx.save();
                    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    ctx.fillRect(220, canvas.height - 40, 200, 30);
                    ctx.fillStyle = 'white';
                    ctx.font = '16px Arial';
                    ctx.fillText(`Step Height: ${coordinateScaler.formatDistance(ankleDistanceCm)}`, 230, canvas.height - 20);
                    ctx.restore();
                }
            }
        }
        
        function getFacingDirection(keypoints) {
            // For side-view running analysis, determine left vs right facing
            // Use toe/heel positions to determine which direction they're moving/facing
            const leftHeel = keypoints[29];     // left_heel
            const leftToe = keypoints[31];      // left_foot_index (toe)
            const rightHeel = keypoints[30];    // right_heel  
            const rightToe = keypoints[32];     // right_foot_index (toe)
            
            // Check if we have good foot data
            const leftFootVisible = leftHeel.score > 0.3 && leftToe.score > 0.3;
            const rightFootVisible = rightHeel.score > 0.3 && rightToe.score > 0.3;
            
            if (leftFootVisible || rightFootVisible) {
                // Determine direction based on toe position relative to heel
                let facingLeft = false;
                let facingRight = false;
                
                if (leftFootVisible) {
                    // If left toe is to the left of left heel, they're facing left
                    if (leftToe.x < leftHeel.x) facingLeft = true;
                    else facingRight = true;
                }
                
                if (rightFootVisible) {
                    // If right toe is to the left of right heel, they're facing left
                    if (rightToe.x < rightHeel.x) facingLeft = true;
                    else facingRight = true;
                }
                
                // Return the dominant direction
                if (facingLeft && !facingRight) return 'Facing Left';
                if (facingRight && !facingLeft) return 'Facing Right';
                if (facingLeft && facingRight) return 'Facing Left'; // Default to left if mixed
            }
            
            // Fallback: use shoulder positions for basic left/right determination
            const leftShoulder = keypoints[11];   // left_shoulder
            const rightShoulder = keypoints[12];  // right_shoulder
            
            if (leftShoulder.score > 0.3 && rightShoulder.score > 0.3) {
                // Assume facing forward (toward camera) and determine which side is closer
                if (leftShoulder.x < rightShoulder.x) {
                    return 'Facing Right'; // Standard orientation
                } else {
                    return 'Facing Left';  // Mirrored or flipped
                }
            }
            
            return 'Unknown';
        }
        
        function drawModalSkeleton(keypoints, scaleX, scaleY) {
            const connections = [
                // Head (nose to ears only - skip eyes and mouth)
                [0, 7], [0, 8], // nose to ears for head angle
                // Torso
                [11, 12], [11, 23], [12, 24], [23, 24], // shoulders and hips
                // Arms (to wrists only, skip fingers)
                [11, 13], [13, 15], [12, 14], [14, 16], // shoulder to elbow to wrist
                // Legs (full leg structure for running analysis)
                [23, 25], [25, 27], [27, 29], [27, 31], // left leg including foot
                [24, 26], [26, 28], [28, 30], [28, 32]  // right leg including foot
            ];
            
            // Use brighter, more visible colors for skeleton
            ctx.strokeStyle = 'rgba(0, 255, 255, 0.9)'; // Bright cyan
            ctx.lineWidth = 3; // Thicker lines for better visibility
            
            connections.forEach(([i, j]) => {
                const kp1 = keypoints[i];
                const kp2 = keypoints[j];
                
                if (kp1 && kp2 && kp1.score > 0.3 && kp2.score > 0.3) {
                    ctx.beginPath();
                    ctx.moveTo(kp1.x * scaleX, kp1.y * scaleY);
                    ctx.lineTo(kp2.x * scaleX, kp2.y * scaleY);
                    ctx.stroke();
                }
            });
        }
        
        function drawModalBoundingBox(keypoints, scaleX, scaleY) {
            const validKeypoints = keypoints.filter(kp => kp.score > 0.3);
            if (validKeypoints.length === 0) return;
            
            let minX = Infinity, minY = Infinity;
            let maxX = -Infinity, maxY = -Infinity;
            
            validKeypoints.forEach(kp => {
                const x = kp.x * scaleX;
                const y = kp.y * scaleY;
                minX = Math.min(minX, x);
                minY = Math.min(minY, y);
                maxX = Math.max(maxX, x);
                maxY = Math.max(maxY, y);
            });
            
            // Add padding
            const padding = 20;
            minX -= padding;
            minY -= padding;
            maxX += padding;
            maxY += padding;
            
            // Draw bounding box
            ctx.strokeStyle = 'rgba(0, 255, 255, 0.8)';
            ctx.lineWidth = 2;
            ctx.strokeRect(minX, minY, maxX - minX, maxY - minY);
            
            // Draw center point
            const centerX = (minX + maxX) / 2;
            const centerY = (minY + maxY) / 2;
            ctx.fillStyle = 'cyan';
            ctx.beginPath();
            ctx.arc(centerX, centerY, 8, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        function updateKeypointInfo(keypoints) {
            const keypointNames = [
                'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
                'right_eye_inner', 'right_eye', 'right_eye_outer',
                'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
                'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
                'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
                'left_index', 'right_index', 'left_thumb', 'right_thumb',
                'left_hip', 'right_hip', 'left_knee', 'right_knee',
                'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
                'left_foot_index', 'right_foot_index',
                // Additional 6 keypoints for full model
                'left_wrist_pinky', 'left_wrist_index', 'left_wrist_thumb',
                'right_wrist_pinky', 'right_wrist_index', 'right_wrist_thumb'
            ];
            
            // Only show keypoints that we're actually drawing (not skipped)
            const skipKeypoints = [
                1, 2, 3, 4, 5, 6,  // eyes (left_eye_inner, left_eye, left_eye_outer, right_eye_inner, right_eye, right_eye_outer)
                9, 10,             // mouth (mouth_left, mouth_right)
                17, 18, 19, 20, 21, 22  // fingers (pinky, index, thumb for both hands)
            ];
            
            const keypointInfo = document.getElementById('keypointInfo');
            keypointInfo.innerHTML = '';
            
            keypoints.slice(0, 33).forEach((kp, idx) => {
                // Only show keypoints we're actually drawing
                if (!skipKeypoints.includes(idx)) {
                    const card = document.createElement('div');
                    card.className = 'keypoint-card';
                    
                    // Style based on validity and confidence
                    if (kp.isInvalid) {
                        card.classList.add('low-confidence');
                        card.style.backgroundColor = '#ffebee';
                        card.style.borderColor = '#f44336';
                    } else if (kp.isEdgeCase) {
                        card.classList.add('medium-confidence');
                        card.style.backgroundColor = '#fff3e0';
                        card.style.borderColor = '#ff9800';
                    } else if (kp.score > 0.8) {
                        card.classList.add('high-confidence');
                    } else if (kp.score > 0.5) {
                        card.classList.add('medium-confidence');
                    } else if (kp.score > 0.3) {
                        card.classList.add('low-confidence');
                    }
                    
                    const statusIcon = kp.isInvalid ? '❌' : kp.isEdgeCase ? '⚠️' : '✅';
                    const statusText = kp.isInvalid ? 'INVALID (0,0)' : kp.isEdgeCase ? 'EDGE CASE' : 'VALID';
                    
                    card.innerHTML = `
                        <strong>${idx}. ${keypointNames[idx] || 'Unknown'} ${statusIcon}</strong><br>
                        <small>Score: ${(kp.score * 100).toFixed(1)}%</small><br>
                        <small>Pos: (${Math.round(kp.x)}, ${Math.round(kp.y)})</small><br>
                        <small style="color: ${kp.isInvalid ? '#d32f2f' : kp.isEdgeCase ? '#f57c00' : '#388e3c'}; font-weight: bold;">${statusText}</small>
                    `;
                    
                    keypointInfo.appendChild(card);
                }
            });
        }
        
        // Initialize on load
        window.addEventListener('load', initialize);
    </script>
</body>
</html>