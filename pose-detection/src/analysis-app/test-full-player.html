<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxWattz - Full Processed Player Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .logo {
            font-size: 28px;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 8px;
        }
        
        .player-container {
            position: relative;
            margin: 20px 0;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            overflow: hidden;
            background: #000;
        }
        
        .video-wrapper {
            position: relative;
            width: 100%;
        }
        
        .pose-canvas {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            z-index: 10;
        }
        
        .controls {
            background: #f8f9fa;
            padding: 16px;
            border-top: 1px solid #e1e5e9;
        }
        
        .info-panel {
            background: #e8f5e8;
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
            font-size: 14px;
        }
        
        .error-panel {
            background: #ffebee;
            border: 1px solid #f44336;
            color: #c62828;
            padding: 12px;
            border-radius: 6px;
            margin: 16px 0;
        }
        
        .loading-panel {
            background: #e3f2fd;
            padding: 20px;
            text-align: center;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">MaxWattz Full Player Test</div>
            <div class="subtitle">Complete Pose Overlay with Video Synchronization</div>
        </div>
        
        <div id="root"></div>
    </div>

    <script type="text/babel">
        const { useState, useEffect, useRef, useCallback } = React;

        function FullProcessedPlayer({ videoUrl, resultsUrl }) {
            const videoRef = useRef(null);
            const canvasRef = useRef(null);
            const animationFrameRef = useRef(null);
            
            const [isLoading, setIsLoading] = useState(true);
            const [error, setError] = useState(null);
            const [processedData, setProcessedData] = useState(null);
            const [isPlaying, setIsPlaying] = useState(false);
            const [currentFrame, setCurrentFrame] = useState(0);
            const [currentPose, setCurrentPose] = useState(null);

            // Load processed results
            useEffect(() => {
                async function loadData() {
                    try {
                        setIsLoading(true);
                        const response = await fetch(resultsUrl);
                        if (!response.ok) throw new Error(`HTTP ${response.status}`);
                        const data = await response.json();
                        setProcessedData(data);
                        setIsLoading(false);
                        console.log('✅ Loaded data:', data);
                    } catch (err) {
                        setError(err.message);
                        setIsLoading(false);
                    }
                }
                
                if (resultsUrl) loadData();
            }, [resultsUrl]);

            // Find frame data for current video time
            const findFrameForTime = useCallback((currentTime) => {
                if (!processedData) return null;
                
                const targetFrameNumber = Math.round(currentTime * processedData.fps);
                return processedData.frames.find(frame => 
                    Math.abs(frame.frameNumber - targetFrameNumber) <= 1
                ) || processedData.frames[0];
            }, [processedData]);

            // Draw pose overlay on canvas
            const drawPoseOverlay = useCallback((ctx, frameData, canvasWidth, canvasHeight) => {
                if (!frameData || !processedData) return;

                const { keypoints } = frameData;
                const scaleX = canvasWidth / processedData.videoWidth;
                const scaleY = canvasHeight / processedData.videoHeight;

                // Clear canvas
                ctx.clearRect(0, 0, canvasWidth, canvasHeight);

                // Draw keypoints
                keypoints.forEach((keypoint) => {
                    if (keypoint.score < 0.3) return;

                    const x = keypoint.x * scaleX;
                    const y = keypoint.y * scaleY;
                    const alpha = Math.min(keypoint.score, 1.0);

                    // Color by confidence
                    let color = `rgba(0, 255, 0, ${alpha})`;
                    if (keypoint.score < 0.7) color = `rgba(255, 165, 0, ${alpha})`;
                    if (keypoint.score < 0.5) color = `rgba(255, 0, 0, ${alpha})`;

                    // Draw keypoint
                    ctx.fillStyle = color;
                    ctx.beginPath();
                    ctx.arc(x, y, 6, 0, 2 * Math.PI);
                    ctx.fill();

                    // Draw keypoint name
                    ctx.fillStyle = 'white';
                    ctx.strokeStyle = 'black';
                    ctx.lineWidth = 2;
                    ctx.font = '12px Arial';
                    ctx.strokeText(keypoint.name, x + 8, y - 8);
                    ctx.fillText(keypoint.name, x + 8, y - 8);
                });

                // Draw skeleton connections
                const connections = [
                    ['left_shoulder', 'right_shoulder'],
                    ['left_shoulder', 'left_hip'],
                    ['right_shoulder', 'right_hip'],
                    ['left_hip', 'right_hip'],
                    ['left_hip', 'left_knee'],
                    ['left_knee', 'left_ankle'],
                    ['right_hip', 'right_knee'],
                    ['right_knee', 'right_ankle'],
                    ['left_shoulder', 'left_elbow'],
                    ['left_elbow', 'left_wrist'],
                    ['right_shoulder', 'right_elbow'],
                    ['right_elbow', 'right_wrist']
                ];

                ctx.strokeStyle = 'rgba(255, 255, 255, 0.9)';
                ctx.lineWidth = 3;

                connections.forEach(([point1Name, point2Name]) => {
                    const point1 = keypoints.find(kp => kp.name === point1Name);
                    const point2 = keypoints.find(kp => kp.name === point2Name);

                    if (point1 && point2 && point1.score > 0.3 && point2.score > 0.3) {
                        ctx.beginPath();
                        ctx.moveTo(point1.x * scaleX, point1.y * scaleY);
                        ctx.lineTo(point2.x * scaleX, point2.y * scaleY);
                        ctx.stroke();
                    }
                });

                // Draw SmoothNet indicator
                ctx.fillStyle = 'rgba(0, 100, 255, 0.8)';
                ctx.fillRect(canvasWidth - 220, 10, 210, 30);
                ctx.fillStyle = 'white';
                ctx.font = '14px Arial';
                ctx.fillText('🔄 SmoothNet Processed', canvasWidth - 210, 30);
            }, [processedData]);

            // Animation loop for synchronized playback
            const startSyncLoop = useCallback(() => {
                const syncFrame = () => {
                    const video = videoRef.current;
                    const canvas = canvasRef.current;
                    
                    if (!video || !canvas || video.paused || video.ended || !processedData) {
                        return;
                    }

                    // Get the actual displayed dimensions of the video element
                    if (!video) return;

                    // Use the video's actual display dimensions
                    const videoRect = video.getBoundingClientRect();
                    canvas.width = videoRect.width;
                    canvas.height = videoRect.height;

                    // Position canvas to exactly overlay the video
                    canvas.style.width = `${videoRect.width}px`;
                    canvas.style.height = `${videoRect.height}px`;

                    // Find pose data for current time
                    const frameData = findFrameForTime(video.currentTime);
                    setCurrentFrame(frameData ? frameData.frameNumber : 0);
                    setCurrentPose(frameData);

                    // Draw pose overlay
                    const ctx = canvas.getContext('2d');
                    if (ctx && frameData) {
                        drawPoseOverlay(ctx, frameData, canvas.width, canvas.height);
                    }

                    // Continue loop
                    if (isPlaying) {
                        animationFrameRef.current = requestAnimationFrame(syncFrame);
                    }
                };

                syncFrame();
            }, [processedData, isPlaying, findFrameForTime, drawPoseOverlay]);

            // Handle video events
            const handlePlay = () => {
                setIsPlaying(true);
                startSyncLoop();
            };

            const handlePause = () => {
                setIsPlaying(false);
                if (animationFrameRef.current) {
                    cancelAnimationFrame(animationFrameRef.current);
                }
            };

            // Start sync when playing
            useEffect(() => {
                if (isPlaying && processedData) {
                    startSyncLoop();
                }
                return () => {
                    if (animationFrameRef.current) {
                        cancelAnimationFrame(animationFrameRef.current);
                    }
                };
            }, [isPlaying, processedData, startSyncLoop]);

            if (isLoading) {
                return (
                    <div className="loading-panel">
                        <div>Loading processed results...</div>
                    </div>
                );
            }

            if (error) {
                return (
                    <div className="error-panel">
                        <strong>Error:</strong> {error}
                    </div>
                );
            }

            return (
                <div>
                    {processedData && (
                        <div className="info-panel">
                            <strong>✅ Loaded Successfully:</strong> {processedData.modelType} | 
                            {processedData.frames.length} frames | 
                            {processedData.processingTime.toFixed(1)}s processing time
                        </div>
                    )}

                    <div className="player-container">
                        <div className="video-wrapper">
                            <video
                                ref={videoRef}
                                src={videoUrl}
                                style={{ 
                                    width: '100%', 
                                    height: 'auto', 
                                    display: 'block',
                                    maxHeight: '500px'
                                }}
                                controls={true}
                                preload="metadata"
                                onPlay={handlePlay}
                                onPause={handlePause}
                                onEnded={handlePause}
                                onLoadedMetadata={() => console.log('Video metadata loaded')}
                                onError={(e) => console.error('Video error:', e)}
                            />
                            
                            <canvas
                                ref={canvasRef}
                                className="pose-canvas"
                                style={{ display: processedData ? 'block' : 'none' }}
                            />
                        </div>
                        
                        <div className="controls">
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '12px' }}>
                                <div style={{ display: 'flex', gap: '12px', alignItems: 'center' }}>
                                    <button
                                        onClick={() => {
                                            const video = videoRef.current;
                                            if (video) {
                                                if (video.paused) {
                                                    video.play();
                                                } else {
                                                    video.pause();
                                                }
                                            }
                                        }}
                                        style={{
                                            padding: '8px 16px',
                                            backgroundColor: '#4ecdc4',
                                            color: 'white',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            fontSize: '14px'
                                        }}
                                    >
                                        {isPlaying ? '⏸️ Pause' : '▶️ Play'}
                                    </button>
                                    
                                    <button
                                        onClick={() => {
                                            const video = videoRef.current;
                                            if (video) {
                                                video.currentTime = 0;
                                            }
                                        }}
                                        style={{
                                            padding: '8px 16px',
                                            backgroundColor: '#ff6b6b',
                                            color: 'white',
                                            border: 'none',
                                            borderRadius: '4px',
                                            cursor: 'pointer',
                                            fontSize: '14px'
                                        }}
                                    >
                                        ⏮️ Reset
                                    </button>
                                </div>
                                
                                <div>
                                    <strong>Status:</strong> {isPlaying ? '▶️ Playing' : '⏸️ Paused'}
                                </div>
                            </div>
                            
                            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', fontSize: '14px' }}>
                                <div>
                                    <strong>Frame:</strong> {currentFrame} / {processedData ? processedData.frames.length : 0}
                                </div>
                                {currentPose && (
                                    <div>
                                        <strong>Avg Confidence:</strong> {(currentPose.keypoints.reduce((sum, kp) => sum + kp.score, 0) / currentPose.keypoints.length * 100).toFixed(1)}%
                                    </div>
                                )}
                                <div>
                                    <strong>Time:</strong> {videoRef.current ? videoRef.current.currentTime?.toFixed(1) || '0.0' : '0.0'}s
                                </div>
                            </div>
                        </div>
                    </div>

                    {currentPose && (
                        <div style={{ marginTop: '16px', padding: '12px', background: '#f0f0f0', borderRadius: '6px', fontSize: '12px' }}>
                            <strong>Current Frame Detail:</strong>
                            <div>Timestamp: {currentPose.timestamp.toFixed(2)}s | Keypoints: {currentPose.keypoints.length}</div>
                            <div>Sample keypoint: {currentPose.keypoints[0]?.name} = ({currentPose.keypoints[0]?.x.toFixed(1)}, {currentPose.keypoints[0]?.y.toFixed(1)})</div>
                        </div>
                    )}
                </div>
            );
        }

        function TestApp() {
            // Try local video first, then S3 as fallback
            const [videoUrl, setVideoUrl] = useState('./Michael_test_side.mp4');
            const [videoSource, setVideoSource] = useState('local');
            const resultsUrl = './test-results-2.json'; // Use the latest fixed results
            const [videoError, setVideoError] = useState(null);
            
            const tryS3Fallback = () => {
                console.log('Trying S3 fallback...');
                setVideoUrl('https://maxwattz-videos.s3.amazonaws.com/maxwattz-running-videos-raw-side/Michael_test_side.mp4');
                setVideoSource('s3');
                setVideoError(null);
            };

            return (
                <div>
                    <div style={{ marginBottom: '20px', padding: '16px', background: '#e3f2fd', borderRadius: '6px' }}>
                        <h3>🎯 Test Objectives</h3>
                        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
                            <li><strong>Pose Alignment:</strong> Keypoints should align with person in video</li>
                            <li><strong>Smooth Motion:</strong> SmoothNet should reduce jitter between frames</li>
                            <li><strong>Frame Sync:</strong> Pose data should update in real-time with video</li>
                            <li><strong>Coordinate System:</strong> X/Y coordinates should be pixel-accurate</li>
                        </ul>
                        
                        <div style={{ marginTop: '12px', padding: '8px', background: '#fff3cd', borderRadius: '4px', fontSize: '14px' }}>
                            <strong>📹 Video Source:</strong> {videoSource === 'local' ? 'Local file (./Michael_test_side.mp4)' : 'S3 bucket'}<br/>
                            <strong>💡 If local video fails:</strong> Run `python download_video.py` to download the video locally.
                            {videoSource === 'local' && (
                                <button 
                                    onClick={tryS3Fallback}
                                    style={{ marginLeft: '8px', padding: '4px 8px', fontSize: '12px', cursor: 'pointer' }}
                                >
                                    Try S3 instead
                                </button>
                            )}
                        </div>
                    </div>

                    {videoError && (
                        <div style={{ marginBottom: '16px', padding: '12px', background: '#ffebee', border: '1px solid #f44336', borderRadius: '6px', color: '#c62828' }}>
                            <strong>Video Error:</strong> {videoError}
                        </div>
                    )}

                    <FullProcessedPlayer
                        key={videoUrl} // Force re-render when video URL changes
                        videoUrl={videoUrl}
                        resultsUrl={resultsUrl}
                    />
                </div>
            );
        }

        ReactDOM.render(<TestApp />, document.getElementById('root'));
    </script>
</body>
</html>