/**
 * Running Metrics Calculation Engine
 * Analyzes pose data to calculate running biomechanics metrics
 */

import { PoseData, PoseKeypoint, RunningMetrics, CoordinateTransform, UserHeight } from '../types';
import { 
  calculateDistance, 
  calculateAngle, 
  getKeypointByName, 
  calculateCoordinateTransform,
  validateEssentialKeypoints 
} from './coordinates';

interface FrameAnalysis {
  timestamp: number;
  leftFootContact: boolean;
  rightFootContact: boolean;
  stridePhase: 'stance' | 'swing' | 'transition';
  keyMeasurements: {
    strideLength?: number;
    kneeFlexionAngle?: number;
    postureAlignment?: number;
    footStrike?: 'heel' | 'midfoot' | 'forefoot';
  };
}

export class RunningMetricsCalculator {
  private frameHistory: PoseData[] = [];
  private analysisHistory: FrameAnalysis[] = [];
  private userHeight: UserHeight;
  private coordinateTransform?: CoordinateTransform;
  
  // Analysis parameters
  private readonly HISTORY_SIZE = 90; // 3 seconds at 30 FPS
  private readonly MIN_CONFIDENCE = 0.5;
  private readonly GROUND_CONTACT_THRESHOLD = 0.02; // meters
  
  constructor(userHeight: UserHeight) {
    this.userHeight = userHeight;
  }

  /**
   * Add new pose data frame for analysis
   */
  addFrame(poseData: PoseData, videoWidth: number, videoHeight: number): RunningMetrics | null {
    // Validate pose data
    const validation = validateEssentialKeypoints(poseData.keypoints);
    if (!validation.isValid || validation.confidence < this.MIN_CONFIDENCE) {
      console.warn('⚠️ Pose data quality insufficient for metrics calculation');
      return null;
    }

    // Update coordinate transform
    this.coordinateTransform = calculateCoordinateTransform(
      this.userHeight,
      videoWidth,
      videoHeight,
      poseData.keypoints
    );

    // Add to history
    this.frameHistory.push(poseData);
    if (this.frameHistory.length > this.HISTORY_SIZE) {
      this.frameHistory.shift();
    }

    // Analyze current frame
    const frameAnalysis = this.analyzeFrame(poseData);
    this.analysisHistory.push(frameAnalysis);
    if (this.analysisHistory.length > this.HISTORY_SIZE) {
      this.analysisHistory.shift();
    }

    // Calculate metrics if we have enough data
    if (this.frameHistory.length >= 30) { // At least 1 second of data
      return this.calculateMetrics();
    }

    return null;
  }

  /**
   * Analyze single frame for running parameters
   */
  private analyzeFrame(poseData: PoseData): FrameAnalysis {
    const { keypoints } = poseData;
    
    // Get key points
    const leftAnkle = getKeypointByName(keypoints, 'left_ankle');
    const rightAnkle = getKeypointByName(keypoints, 'right_ankle');
    const leftKnee = getKeypointByName(keypoints, 'left_knee');
    const rightKnee = getKeypointByName(keypoints, 'right_knee');
    const leftHip = getKeypointByName(keypoints, 'left_hip');
    const rightHip = getKeypointByName(keypoints, 'right_hip');
    const nose = getKeypointByName(keypoints, 'nose');

    const analysis: FrameAnalysis = {
      timestamp: poseData.timestamp,
      leftFootContact: false,
      rightFootContact: false,
      stridePhase: 'transition',
      keyMeasurements: {}
    };

    if (!this.coordinateTransform) return analysis;

    // Analyze foot contact
    if (leftAnkle && rightAnkle) {
      analysis.leftFootContact = this.isFootInContact(leftAnkle);
      analysis.rightFootContact = this.isFootInContact(rightAnkle);
      
      // Determine stride phase
      if (analysis.leftFootContact && analysis.rightFootContact) {
        analysis.stridePhase = 'stance';
      } else if (!analysis.leftFootContact && !analysis.rightFootContact) {
        analysis.stridePhase = 'swing';
      } else {
        analysis.stridePhase = 'transition';
      }
    }

    // Calculate stride length (distance between feet)
    if (leftAnkle && rightAnkle && this.coordinateTransform) {
      analysis.keyMeasurements.strideLength = calculateDistance(
        leftAnkle, 
        rightAnkle, 
        this.coordinateTransform
      );
    }

    // Calculate knee flexion angle
    if (leftKnee && leftHip && leftAnkle) {
      analysis.keyMeasurements.kneeFlexionAngle = calculateAngle(
        leftHip,
        leftKnee,
        leftAnkle
      );
    }

    // Calculate posture alignment
    if (nose && leftHip && rightHip) {
      const hipMidpoint = {
        x: (leftHip.x + rightHip.x) / 2,
        y: (leftHip.y + rightHip.y) / 2,
        score: Math.min(leftHip.score || 0, rightHip.score || 0)
      };
      
      // Calculate lean angle from vertical
      const verticalAngle = Math.atan2(nose.x - hipMidpoint.x, hipMidpoint.y - nose.y) * (180 / Math.PI);
      analysis.keyMeasurements.postureAlignment = Math.abs(verticalAngle);
    }

    // Analyze foot strike pattern
    analysis.keyMeasurements.footStrike = this.analyzeFootStrike(keypoints);

    return analysis;
  }

  /**
   * Determine if foot is in contact with ground
   */
  private isFootInContact(ankle: PoseKeypoint): boolean {
    if (!ankle.z || !this.coordinateTransform) return false;
    
    // Use Z-coordinate to estimate ground contact
    // Lower Z values indicate closer to ground
    return ankle.z < this.GROUND_CONTACT_THRESHOLD;
  }

  /**
   * Analyze foot strike pattern from keypoint positions
   */
  private analyzeFootStrike(keypoints: PoseKeypoint[]): 'heel' | 'midfoot' | 'forefoot' {
    const leftToe = getKeypointByName(keypoints, 'left_foot_index');
    const rightToe = getKeypointByName(keypoints, 'right_foot_index');
    const leftAnkle = getKeypointByName(keypoints, 'left_ankle');
    const rightAnkle = getKeypointByName(keypoints, 'right_ankle');

    // Simplified foot strike analysis based on toe-ankle relationship
    if (leftToe && leftAnkle && leftToe.y < leftAnkle.y) {
      return 'forefoot'; // Toe lower than ankle = forefoot strike
    } else if (leftToe && leftAnkle && Math.abs(leftToe.y - leftAnkle.y) < 10) {
      return 'midfoot'; // Toe and ankle roughly level = midfoot strike
    }
    
    return 'heel'; // Default to heel strike
  }

  /**
   * Calculate comprehensive running metrics from frame history
   */
  private calculateMetrics(): RunningMetrics {
    const recentFrames = this.analysisHistory.slice(-60); // Last 2 seconds
    
    // Calculate stride length
    const strideLengths = recentFrames
      .map(f => f.keyMeasurements.strideLength)
      .filter(s => s !== undefined) as number[];
    
    const avgStrideLength = strideLengths.length > 0 
      ? strideLengths.reduce((sum, s) => sum + s, 0) / strideLengths.length 
      : 0;

    // Calculate cadence (steps per minute)
    const cadence = this.calculateCadence();

    // Calculate ground contact time
    const groundContactTime = this.calculateGroundContactTime();

    // Calculate knee flexion stats
    const kneeAngles = recentFrames
      .map(f => f.keyMeasurements.kneeFlexionAngle)
      .filter(a => a !== undefined) as number[];
    
    const peakKneeFlexion = kneeAngles.length > 0 ? Math.max(...kneeAngles) : 0;

    // Calculate posture stats
    const postureAngles = recentFrames
      .map(f => f.keyMeasurements.postureAlignment)
      .filter(a => a !== undefined) as number[];
    
    const avgPostureAlignment = postureAngles.length > 0
      ? postureAngles.reduce((sum, a) => sum + a, 0) / postureAngles.length
      : 0;

    // Analyze foot strike pattern
    const footStrikes = recentFrames
      .map(f => f.keyMeasurements.footStrike)
      .filter(fs => fs !== undefined);
    
    const dominantFootStrike = this.getDominantFootStrike(footStrikes as string[]);

    return {
      strideLength: {
        value: avgStrideLength,
        unit: 'm',
        score: this.scoreStrideLength(avgStrideLength),
        status: this.getMetricStatus(this.scoreStrideLength(avgStrideLength)),
        description: this.getStrideLengthDescription(avgStrideLength)
      },
      cadence: {
        value: cadence,
        unit: 'steps/min',
        score: this.scoreCadence(cadence),
        status: this.getMetricStatus(this.scoreCadence(cadence)),
        description: this.getCadenceDescription(cadence)
      },
      footStrike: {
        type: dominantFootStrike as 'heel' | 'midfoot' | 'forefoot',
        score: this.scoreFootStrike(dominantFootStrike),
        status: this.getMetricStatus(this.scoreFootStrike(dominantFootStrike)),
        description: this.getFootStrikeDescription(dominantFootStrike)
      },
      posture: {
        alignment: avgPostureAlignment,
        score: this.scorePosture(avgPostureAlignment),
        status: this.getMetricStatus(this.scorePosture(avgPostureAlignment)),
        description: this.getPostureDescription(avgPostureAlignment)
      },
      kneeFlexion: {
        peakAngle: peakKneeFlexion,
        unit: 'degrees',
        score: this.scoreKneeFlexion(peakKneeFlexion),
        status: this.getMetricStatus(this.scoreKneeFlexion(peakKneeFlexion)),
        description: this.getKneeFlexionDescription(peakKneeFlexion)
      },
      groundContactTime: {
        value: groundContactTime,
        unit: 'ms',
        score: this.scoreGroundContactTime(groundContactTime),
        status: this.getMetricStatus(this.scoreGroundContactTime(groundContactTime)),
        description: this.getGroundContactTimeDescription(groundContactTime)
      }
    };
  }

  /**
   * Calculate running cadence from foot contact patterns
   */
  private calculateCadence(): number {
    const recentFrames = this.analysisHistory.slice(-90); // 3 seconds
    if (recentFrames.length < 30) return 0;

    let stepCount = 0;
    let previousLeftContact = false;
    let previousRightContact = false;

    for (const frame of recentFrames) {
      // Count step when foot contact changes from false to true
      if (frame.leftFootContact && !previousLeftContact) stepCount++;
      if (frame.rightFootContact && !previousRightContact) stepCount++;
      
      previousLeftContact = frame.leftFootContact;
      previousRightContact = frame.rightFootContact;
    }

    // Convert to steps per minute (assuming 30 FPS)
    const timeSpanSeconds = recentFrames.length / 30;
    return stepCount > 0 ? (stepCount / timeSpanSeconds) * 60 : 0;
  }

  /**
   * Calculate average ground contact time
   */
  private calculateGroundContactTime(): number {
    const recentFrames = this.analysisHistory.slice(-90); // 3 seconds
    if (recentFrames.length < 10) return 0;

    const contactDurations: number[] = [];
    let currentContactStart = -1;

    for (let i = 0; i < recentFrames.length; i++) {
      const frame = recentFrames[i];
      const isInContact = frame.leftFootContact || frame.rightFootContact;

      if (isInContact && currentContactStart === -1) {
        currentContactStart = i;
      } else if (!isInContact && currentContactStart !== -1) {
        const duration = (i - currentContactStart) * (1000 / 30); // Convert to milliseconds
        contactDurations.push(duration);
        currentContactStart = -1;
      }
    }

    return contactDurations.length > 0
      ? contactDurations.reduce((sum, d) => sum + d, 0) / contactDurations.length
      : 245; // Default reasonable value
  }

  /**
   * Get dominant foot strike pattern from recent frames
   */
  private getDominantFootStrike(footStrikes: string[]): string {
    const counts = footStrikes.reduce((acc, fs) => {
      acc[fs] = (acc[fs] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.keys(counts).reduce((a, b) => counts[a] > counts[b] ? a : b, 'midfoot');
  }

  // Scoring functions (0-100 scale)
  private scoreStrideLength(value: number): number {
    const optimal = 1.2; // Optimal stride length in meters
    const diff = Math.abs(value - optimal);
    return Math.max(0, 100 - (diff * 50));
  }

  private scoreCadence(value: number): number {
    const optimal = 180; // Optimal cadence
    const diff = Math.abs(value - optimal);
    return Math.max(0, 100 - diff);
  }

  private scoreFootStrike(pattern: string): number {
    const scores = { 'midfoot': 90, 'forefoot': 75, 'heel': 60 };
    return scores[pattern as keyof typeof scores] || 50;
  }

  private scorePosture(alignment: number): number {
    return Math.max(0, 100 - (alignment * 2)); // Penalize deviation from vertical
  }

  private scoreKneeFlexion(angle: number): number {
    const optimal = 130; // Optimal knee flexion angle
    const diff = Math.abs(angle - optimal);
    return Math.max(0, 100 - diff);
  }

  private scoreGroundContactTime(time: number): number {
    const optimal = 200; // Optimal ground contact time in ms
    const diff = Math.abs(time - optimal);
    return Math.max(0, 100 - (diff / 2));
  }

  // Status determination
  private getMetricStatus(score: number): 'excellent' | 'good' | 'needs-work' {
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    return 'needs-work';
  }

  // Description generation
  private getStrideLengthDescription(value: number): string {
    if (value > 1.3) return 'Stride may be too long - consider shortening';
    if (value < 1.0) return 'Stride may be too short - consider lengthening';
    return 'Optimal stride length maintained';
  }

  private getCadenceDescription(value: number): string {
    if (value > 190) return 'Cadence is high - good for efficiency';
    if (value < 170) return 'Consider increasing cadence for better efficiency';
    return 'Optimal cadence for efficiency';
  }

  private getFootStrikeDescription(pattern: string): string {
    const descriptions = {
      'midfoot': 'Excellent midfoot landing pattern',
      'forefoot': 'Good forefoot strike - efficient pattern',
      'heel': 'Consider shifting to midfoot strike for efficiency'
    };
    return descriptions[pattern as keyof typeof descriptions] || 'Analyzing foot strike pattern';
  }

  private getPostureDescription(alignment: number): string {
    if (alignment > 10) return 'Excessive forward lean detected';
    if (alignment < 5) return 'Excellent upright posture maintained';
    return 'Good posture with slight forward lean';
  }

  private getKneeFlexionDescription(angle: number): string {
    if (angle > 140) return 'Excellent knee drive through stance';
    if (angle < 120) return 'Consider increasing knee lift for efficiency';
    return 'Good knee flexion pattern observed';
  }

  private getGroundContactTimeDescription(time: number): string {
    if (time > 250) return 'Ground contact time is longer than optimal';
    if (time < 180) return 'Very quick ground contact - excellent efficiency';
    return 'Optimal ground contact time';
  }

  /**
   * Reset calculator state
   */
  reset(): void {
    this.frameHistory = [];
    this.analysisHistory = [];
    this.coordinateTransform = undefined;
  }
}