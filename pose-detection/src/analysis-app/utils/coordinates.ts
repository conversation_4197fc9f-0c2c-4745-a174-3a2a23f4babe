/**
 * Coordinate transformation utilities for BlazePose analysis
 * Handles user height-based scaling and coordinate conversion
 */

import { UserHeight, CoordinateTransform, PoseKeypoint } from '../types';

/**
 * Convert user height from feet/inches to meters
 */
export const heightToMeters = (height: UserHeight): number => {
  const totalInches = height.feet * 12 + height.inches;
  return totalInches * 0.0254; // Convert inches to meters
};

/**
 * Calculate coordinate transformation parameters based on user height
 * Assumes side-view treadmill setup with 5-foot distance from camera
 */
export const calculateCoordinateTransform = (
  userHeight: UserHeight,
  videoWidth: number,
  videoHeight: number,
  detectedPose?: PoseKeypoint[]
): CoordinateTransform => {
  const userHeightInMeters = heightToMeters(userHeight);
  
  // For side-view analysis, we use the detected pose height to calibrate scale
  // Default assumption: full body visible in frame
  let estimatedPixelHeight = videoHeight * 0.85; // Assume person takes up 85% of frame height
  
  if (detectedPose && detectedPose.length >= 33) {
    // Use actual detected pose for more accurate scaling
    // Find head and foot keypoints for height measurement
    const noseKeypoint = detectedPose.find(kp => kp.name === 'nose');
    const leftAnkle = detectedPose.find(kp => kp.name === 'left_ankle');
    const rightAnkle = detectedPose.find(kp => kp.name === 'right_ankle');
    
    if (noseKeypoint && (leftAnkle || rightAnkle)) {
      const ankleY = leftAnkle ? leftAnkle.y : rightAnkle!.y;
      estimatedPixelHeight = Math.abs(ankleY - noseKeypoint.y);
    }
  }
  
  // Calculate pixels per meter based on user height
  const pixelsPerMeter = estimatedPixelHeight / userHeightInMeters;
  
  // Scale factor for coordinate conversion
  const scale = pixelsPerMeter;
  
  // Center offsets (assume person is centered in frame)
  const offsetX = videoWidth / 2;
  const offsetY = videoHeight / 2;
  
  return {
    scale,
    offsetX,
    offsetY,
    pixelsPerMeter,
    userHeightInMeters
  };
};

/**
 * Transform pixel coordinates to real-world coordinates in meters
 */
export const pixelsToMeters = (
  x: number,
  y: number,
  transform: CoordinateTransform
): { x: number; y: number } => {
  // Convert pixel coordinates to meters relative to center point
  const realX = (x - transform.offsetX) / transform.pixelsPerMeter;
  const realY = (y - transform.offsetY) / transform.pixelsPerMeter;
  
  return { x: realX, y: realY };
};

/**
 * Transform real-world coordinates back to pixel coordinates
 */
export const metersToPixels = (
  x: number,
  y: number,
  transform: CoordinateTransform
): { x: number; y: number } => {
  const pixelX = x * transform.pixelsPerMeter + transform.offsetX;
  const pixelY = y * transform.pixelsPerMeter + transform.offsetY;
  
  return { x: pixelX, y: pixelY };
};

/**
 * Calculate distance between two keypoints in meters
 */
export const calculateDistance = (
  point1: PoseKeypoint,
  point2: PoseKeypoint,
  transform: CoordinateTransform
): number => {
  const p1 = pixelsToMeters(point1.x, point1.y, transform);
  const p2 = pixelsToMeters(point2.x, point2.y, transform);
  
  const dx = p2.x - p1.x;
  const dy = p2.y - p1.y;
  
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Calculate angle between three keypoints in degrees
 */
export const calculateAngle = (
  point1: PoseKeypoint,
  vertex: PoseKeypoint,
  point3: PoseKeypoint
): number => {
  const a = { x: point1.x - vertex.x, y: point1.y - vertex.y };
  const b = { x: point3.x - vertex.x, y: point3.y - vertex.y };
  
  const dotProduct = a.x * b.x + a.y * b.y;
  const magnitudeA = Math.sqrt(a.x * a.x + a.y * a.y);
  const magnitudeB = Math.sqrt(b.x * b.x + b.y * b.y);
  
  if (magnitudeA === 0 || magnitudeB === 0) return 0;
  
  const cosAngle = dotProduct / (magnitudeA * magnitudeB);
  const angleRadians = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
  
  return angleRadians * (180 / Math.PI);
};

/**
 * Get keypoint by name from pose data
 */
export const getKeypointByName = (
  keypoints: PoseKeypoint[],
  name: string
): PoseKeypoint | undefined => {
  return keypoints.find(kp => kp.name === name);
};

/**
 * Validate that essential keypoints are visible for running analysis
 */
export const validateEssentialKeypoints = (keypoints: PoseKeypoint[]): {
  isValid: boolean;
  missingKeypoints: string[];
  confidence: number;
} => {
  const essentialKeypoints = [
    'nose',
    'left_shoulder', 'right_shoulder',
    'left_hip', 'right_hip',
    'left_knee', 'right_knee',
    'left_ankle', 'right_ankle'
  ];
  
  const confidenceThreshold = 0.5;
  const missingKeypoints: string[] = [];
  let totalConfidence = 0;
  let validKeypointCount = 0;
  
  for (const requiredName of essentialKeypoints) {
    const keypoint = getKeypointByName(keypoints, requiredName);
    
    if (!keypoint || !keypoint.score || keypoint.score < confidenceThreshold) {
      missingKeypoints.push(requiredName);
    } else {
      totalConfidence += keypoint.score;
      validKeypointCount++;
    }
  }
  
  const averageConfidence = validKeypointCount > 0 ? totalConfidence / validKeypointCount : 0;
  const isValid = missingKeypoints.length === 0;
  
  return {
    isValid,
    missingKeypoints,
    confidence: averageConfidence
  };
};