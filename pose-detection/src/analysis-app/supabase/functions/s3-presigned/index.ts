// Supabase Edge Function for S3 Presigned URLs
// Deploy with: supabase functions deploy s3-presigned

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { S3Client, PutObjectCommand, CreateMultipartUploadCommand, UploadPartCommand, CompleteMultipartUploadCommand, AbortMultipartUploadCommand } from 'https://esm.sh/@aws-sdk/client-s3@3.0.0'
import { getSignedUrl } from 'https://esm.sh/@aws-sdk/s3-request-presigner@3.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

// Initialize S3 client
const s3Client = new S3Client({
  region: Deno.env.get('AWS_REGION') || 'us-east-1',
  credentials: {
    accessKeyId: Deno.env.get('AWS_ACCESS_KEY_ID')!,
    secretAccessKey: Deno.env.get('AWS_SECRET_ACCESS_KEY')!,
  },
})

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const url = new URL(req.url)
    const path = url.pathname

    // Route handlers
    if (path === '/s3-presigned/upload' && req.method === 'POST') {
      return await handleSingleUpload(req)
    } else if (path === '/s3-presigned/multipart/init' && req.method === 'POST') {
      return await handleMultipartInit(req)
    } else if (path === '/s3-presigned/multipart/part-url' && req.method === 'POST') {
      return await handlePartUrl(req)
    } else if (path === '/s3-presigned/multipart/complete' && req.method === 'POST') {
      return await handleMultipartComplete(req)
    } else if (path === '/s3-presigned/multipart/abort' && req.method === 'POST') {
      return await handleMultipartAbort(req)
    }

    return new Response(
      JSON.stringify({ error: 'Not found' }),
      { status: 404, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  } catch (error) {
    console.error('Edge function error:', error)
    return new Response(
      JSON.stringify({ error: error.message }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})

// Handle single file upload
async function handleSingleUpload(req: Request) {
  const { key, contentType, bucket } = await req.json()

  const command = new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    ContentType: contentType,
  })

  const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 }) // 1 hour
  const fileUrl = `https://${bucket}.s3.amazonaws.com/${key}`

  return new Response(
    JSON.stringify({ uploadUrl, fileUrl }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// Initialize multipart upload
async function handleMultipartInit(req: Request) {
  const { key, contentType, bucket } = await req.json()

  const command = new CreateMultipartUploadCommand({
    Bucket: bucket,
    Key: key,
    ContentType: contentType,
  })

  const response = await s3Client.send(command)

  return new Response(
    JSON.stringify({ uploadId: response.UploadId }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// Get presigned URL for part upload
async function handlePartUrl(req: Request) {
  const { key, uploadId, partNumber, bucket } = await req.json()

  const command = new UploadPartCommand({
    Bucket: bucket,
    Key: key,
    UploadId: uploadId,
    PartNumber: partNumber,
  })

  const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 3600 })

  return new Response(
    JSON.stringify({ uploadUrl }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// Complete multipart upload
async function handleMultipartComplete(req: Request) {
  const { key, uploadId, parts, bucket } = await req.json()

  const command = new CompleteMultipartUploadCommand({
    Bucket: bucket,
    Key: key,
    UploadId: uploadId,
    MultipartUpload: { Parts: parts },
  })

  const response = await s3Client.send(command)
  const fileUrl = `https://${bucket}.s3.amazonaws.com/${key}`

  return new Response(
    JSON.stringify({ fileUrl, etag: response.ETag }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}

// Abort multipart upload
async function handleMultipartAbort(req: Request) {
  const { key, uploadId, bucket } = await req.json()

  const command = new AbortMultipartUploadCommand({
    Bucket: bucket,
    Key: key,
    UploadId: uploadId,
  })

  await s3Client.send(command)

  return new Response(
    JSON.stringify({ success: true }),
    { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
  )
}