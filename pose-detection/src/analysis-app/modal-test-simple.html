<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal SmoothNet Results - Visual Test</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .video-container {
            flex: 0 0 calc(33.333% - 6.67px); /* Take exactly 1/3 minus proportional gap */
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            min-width: 0;
            margin-bottom: 20px;
        }
        
        .video-wrapper {
            position: relative;
            display: inline-block;
            width: 100%;
        }
        
        #videoElement {
            width: 100%;
            height: auto;
            display: block;
            margin: 0;
            border-radius: 8px;
        }
        
        #canvasElement {
            position: absolute;
            top: 0;
            left: 0;
            pointer-events: none;
            border-radius: 8px;
            /* Canvas size is set programmatically to match video exactly */
        }
        
        @media (max-width: 768px) {
            #videoElement {
                max-width: 90vw;
            }
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.loading { background: #fff3cd; color: #856404; }
        .status.ready { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        
        .controls {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .video-section {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            align-items: flex-start;
        }
        
        @media (max-width: 1024px) {
            .video-section {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Modal SmoothNet Results Test</h1>
            <p>Testing visual alignment of pre-processed pose data from Modal pipeline</p>
        </div>
        
        <div class="controls">
            <div id="status" class="status">Click Load Video to start</div>
            <button onclick="loadVideo()">Load Video</button>
            <span id="modelStatus">Not loaded</span>
        </div>
        
        <div class="video-section">
            <div class="video-container">
                <div class="video-wrapper">
                    <video id="videoElement" controls></video>
                    <canvas id="canvasElement"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let modalData = null;
        let video = null;
        let canvas = null;
        let ctx = null;
        let animationId = null;
        
        // Keypoint filtering per SPEC: Eyes (1-6), Mouth (9-10), Fingers (17-22)
        const FILTERED_KEYPOINT_INDICES = [1, 2, 3, 4, 5, 6, 9, 10, 17, 18, 19, 20, 21, 22];
        
        async function loadModalData() {
            try {
                updateStatus('Loading Modal processed data...', 'loading');
                log('Loading Modal SmoothNet JSON results...');
                
                const response = await fetch('./test-results-2.json');
                if (!response.ok) {
                    throw new Error(`Failed to load JSON: ${response.status} ${response.statusText}`);
                }
                
                modalData = await response.json();
                
                log('Modal data loaded successfully', 'info', {
                    video: modalData.video,
                    dimensions: `${modalData.videoWidth}×${modalData.videoHeight}`,
                    fps: modalData.fps,
                    frames: modalData.frames.length,
                    modelType: modalData.modelType
                });
                
                updateStatus('Modal SmoothNet data loaded successfully', 'ready');
                document.getElementById('modelStatus').textContent = `Modal ${modalData.modelType} - ${modalData.frames.length} frames`;
                
            } catch (error) {
                updateStatus(`Failed to load Modal data: ${error.message}`, 'error');
                log('Modal data loading failed', 'error', { 
                    message: error.message 
                });
                throw error;
            }
        }
        
        function findFrameForTime(currentTime) {
            if (!modalData || !modalData.frames) return null;
            
            // Find frame closest to current video time
            let closestFrame = null;
            let minDiff = Infinity;
            
            for (const frame of modalData.frames) {
                const diff = Math.abs(frame.timestamp - currentTime);
                if (diff < minDiff) {
                    minDiff = diff;
                    closestFrame = frame;
                }
            }
            
            return closestFrame;
        }
        
        function drawPoseOverlay() {
            if (!video || !canvas || !ctx || !modalData) return;
            
            // Update canvas size to match video display
            const rect = video.getBoundingClientRect();
            canvas.width = rect.width;
            canvas.height = rect.height;
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';
            
            // Clear canvas
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // Find frame data for current time
            const frameData = findFrameForTime(video.currentTime);
            if (!frameData || !frameData.keypoints) return;
            
            // Calculate scaling from video dimensions to canvas display size
            const scaleX = canvas.width / modalData.videoWidth;
            const scaleY = canvas.height / modalData.videoHeight;
            
            // Draw keypoints (with filtering per SPEC)
            frameData.keypoints.forEach((keypoint, idx) => {
                // Skip filtered keypoints per SPEC
                if (FILTERED_KEYPOINT_INDICES.includes(idx)) return;
                
                // Skip low confidence or invalid coordinates
                if (keypoint.score < 0.3) return;
                
                const x = keypoint.x * scaleX;
                const y = keypoint.y * scaleY;
                
                // Color based on confidence
                let color;
                const alpha = Math.min(keypoint.score, 1.0);
                if (keypoint.score >= 0.7) {
                    color = `rgba(0, 255, 0, ${alpha})`;  // Green for high confidence
                } else if (keypoint.score >= 0.5) {
                    color = `rgba(255, 165, 0, ${alpha})`;  // Orange for medium confidence
                } else {
                    color = `rgba(255, 0, 0, ${alpha})`;  // Red for low confidence
                }
                
                ctx.fillStyle = color;
                ctx.beginPath();
                ctx.arc(x, y, 6, 0, 2 * Math.PI);
                ctx.fill();
            });
            
            // Draw skeleton connections
            drawSkeleton(frameData.keypoints, scaleX, scaleY);
        }
        
        function drawSkeleton(keypoints, scaleX, scaleY) {
            // BlazePose skeleton connections
            const connections = [
                [11, 12], [11, 13], [13, 15], [15, 17], [15, 19], [15, 21], [17, 19],
                [12, 14], [14, 16], [16, 18], [16, 20], [16, 22], [18, 20],
                [11, 23], [12, 24], [23, 24], [23, 25], [24, 26],
                [25, 27], [26, 28], [27, 29], [28, 30], [29, 31], [30, 32],
                [27, 31], [28, 32]
            ];
            
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.lineWidth = 3;
            
            connections.forEach(([startIdx, endIdx]) => {
                const startKp = keypoints[startIdx];
                const endKp = keypoints[endIdx];
                
                if (!startKp || !endKp || startKp.score < 0.3 || endKp.score < 0.3) return;
                
                ctx.beginPath();
                ctx.moveTo(startKp.x * scaleX, startKp.y * scaleY);
                ctx.lineTo(endKp.x * scaleX, endKp.y * scaleY);
                ctx.stroke();
            });
        }
        
        function startPoseLoop() {
            function loop() {
                drawPoseOverlay();
                animationId = requestAnimationFrame(loop);
            }
            loop();
        }
        
        function stopPoseLoop() {
            if (animationId) {
                cancelAnimationFrame(animationId);
                animationId = null;
            }
        }
        
        async function loadVideo() {
            try {
                video = document.getElementById('videoElement');
                canvas = document.getElementById('canvasElement');
                ctx = canvas.getContext('2d');
                
                // Load Modal data first
                await loadModalData();
                
                // Set video source to match Modal data
                video.src = './Michael_test_side.mp4';
                
                video.addEventListener('loadeddata', () => {
                    log('Video loaded successfully');
                    startPoseLoop();
                });
                
                video.addEventListener('play', startPoseLoop);
                video.addEventListener('pause', stopPoseLoop);
                
            } catch (error) {
                updateStatus(`Failed to load: ${error.message}`, 'error');
                log('Loading failed', 'error', { message: error.message });
            }
        }
        
        function updateStatus(message, type = 'info') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type}`;
        }
        
        function log(message, type = 'info', data = null) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = {
                timestamp,
                type,
                message,
                data
            };
            
            console.log(JSON.stringify(logEntry));
        }
    </script>
</body>
</html>