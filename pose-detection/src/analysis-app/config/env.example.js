/**
 * Environment Variables Configuration
 * Copy this file to env.js and fill in your actual values
 * DO NOT commit env.js to version control
 */

const ENV = {
    // Supabase Configuration
    SUPABASE_URL: 'https://your-project.supabase.co',
    SUPABASE_ANON_KEY: 'your-anon-key',
    SUPABASE_SERVICE_KEY: 'your-service-key', // Only for server-side operations
    
    // AWS S3 Configuration
    AWS_REGION: 'us-east-1',
    AWS_ACCESS_KEY_ID: 'your-access-key',
    AWS_SECRET_ACCESS_KEY: 'your-secret-key',
    AWS_S3_BUCKET: 'maxwattz-videos',
    AWS_S3_SIDE_PREFIX: 'maxwattz-running-videos-raw-side/',
    AWS_S3_REAR_PREFIX: 'maxwattz-running-videos-raw-rear/',
    AWS_S3_METRICS_SIDE_PREFIX: 'maxwattz-running-metrics-side/',
    AWS_S3_METRICS_REAR_PREFIX: 'maxwattz-running-metrics-rear/',
    
    // Modal Configuration
    MODAL_TOKEN_ID: 'your-modal-token-id',
    MODAL_TOKEN_SECRET: 'your-modal-token-secret',
    MODAL_WORKSPACE: 'your-workspace',
    MODAL_FUNCTION_NAME: 'process_running_video',
    
    // Application Configuration
    APP_ENV: 'development', // 'development', 'staging', 'production'
    APP_URL: 'http://localhost:8000',
    
    // Video Processing Configuration
    MAX_VIDEO_SIZE_MB: 200,
    MAX_VIDEO_DURATION_SECONDS: 10,
    SUPPORTED_VIDEO_FORMATS: ['video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/webm'],
    DEFAULT_FPS: 30,
    ENABLE_60FPS: true,
    ENABLE_4K: true,
    
    // Processing Configuration
    BLAZEPOSE_MODEL: 'heavy', // 'lite', 'full', 'heavy'
    SMOOTHNET_ENABLED: true,
    PROCESSING_TIMEOUT_SECONDS: 120,
    
    // Feature Flags
    ENABLE_S3_UPLOADS: true,
    ENABLE_MODAL_PROCESSING: true,
    ENABLE_LOCAL_PROCESSING: false, // Fallback for development
    ENABLE_DEBUG_LOGS: true,
    
    // Polling Configuration
    STATUS_POLL_INTERVAL_MS: 2000, // Check every 2 seconds
    MAX_POLL_ATTEMPTS: 60, // Max 2 minutes of polling
};

// Validate required environment variables
const validateEnv = () => {
    const required = [
        'SUPABASE_URL',
        'SUPABASE_ANON_KEY',
        'AWS_REGION',
        'AWS_S3_BUCKET'
    ];
    
    const missing = required.filter(key => !ENV[key] || ENV[key].includes('your-'));
    
    if (missing.length > 0) {
        console.error('❌ Missing required environment variables:', missing);
        console.error('Please copy env.example.js to env.js and fill in your values');
        return false;
    }
    
    return true;
};

// Export configuration
export { ENV, validateEnv };