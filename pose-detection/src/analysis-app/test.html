<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaxWattz - Upload Workflow Test</title>
    
    <!-- Styles -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/components.css">
    <link rel="stylesheet" href="styles/form-updates.css">
    
    <!-- TensorFlow.js -->
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-core@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-converter@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow/tfjs-backend-webgl@4.15.0"></script>
    <script src="https://cdn.jsdelivr.net/npm/@tensorflow-models/pose-detection@2.1.0"></script>
</head>
<body style="margin: 0; padding: 20px; background: var(--bg-primary); min-height: 100vh;">
    
    <div style="max-width: 1200px; margin: 0 auto;">
        <header style="text-align: center; margin-bottom: 40px;">
            <h1 style="color: var(--text-primary); margin-bottom: 8px;">MaxWattz Upload Workflow Test</h1>
            <p style="color: var(--text-muted);">Testing the complete side + rear video upload and processing workflow</p>
        </header>

        <!-- Upload Container -->
        <div class="upload-container">
            <div class="upload-header">
                <h2>Upload Running Video</h2>
                <p>Select your view type and upload a video for AI-powered biomechanical analysis</p>
            </div>
            
            
            <!-- Single Upload Area (will be replaced with dual zones) -->
            <div class="upload-area" id="uploadArea">
                <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                </svg>
                <div class="upload-text">
                    <h3>Drop video file here or click to select</h3>
                    <p>Supports MP4, MOV, AVI, WebM</p>
                    <p class="file-types">Recommended: 1080p or higher, 30fps, max 10 seconds, 200MB</p>
                </div>
                <input type="file" id="videoInput" accept="video/*" style="display: none;">
            </div>
            
            <!-- Runner Information Form -->
            <div class="runner-info">
                <h3>Runner Information</h3>
                <div class="form-row">
                    <div class="form-group height-group">
                        <label for="heightSystem">Height</label>
                        <div class="height-inputs">
                            <select id="heightSystem" class="form-select height-system">
                                <option value="imperial">Ft/In</option>
                                <option value="metric">M/Cm</option>
                            </select>
                            <div class="height-values imperial-height">
                                <select id="heightFeet" class="form-select">
                                    <option value="">Ft</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                </select>
                                <select id="heightInches" class="form-select">
                                    <option value="">In</option>
                                    <option value="0">0</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                    <option value="3">3</option>
                                    <option value="4">4</option>
                                    <option value="5">5</option>
                                    <option value="6">6</option>
                                    <option value="7">7</option>
                                    <option value="8">8</option>
                                    <option value="9">9</option>
                                    <option value="10">10</option>
                                    <option value="11">11</option>
                                </select>
                            </div>
                            <div class="height-values metric-height" style="display: none;">
                                <select id="heightMeters" class="form-select">
                                    <option value="">M</option>
                                    <option value="1">1</option>
                                    <option value="2">2</option>
                                </select>
                                <select id="heightCentimeters" class="form-select">
                                    <option value="">Cm</option>
                                    <option value="0">0</option>
                                    <option value="5">5</option>
                                    <option value="10">10</option>
                                    <option value="15">15</option>
                                    <option value="20">20</option>
                                    <option value="25">25</option>
                                    <option value="30">30</option>
                                    <option value="35">35</option>
                                    <option value="40">40</option>
                                    <option value="45">45</option>
                                    <option value="50">50</option>
                                    <option value="55">55</option>
                                    <option value="60">60</option>
                                    <option value="65">65</option>
                                    <option value="70">70</option>
                                    <option value="75">75</option>
                                    <option value="80">80</option>
                                    <option value="85">85</option>
                                    <option value="90">90</option>
                                    <option value="95">95</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group gender-group">
                        <label for="gender">Gender</label>
                        <select id="gender" class="form-select">
                            <option value="">Select Gender</option>
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>
                    <div class="form-group weight-group">
                        <label for="weight">Weight</label>
                        <div class="weight-inputs">
                            <input type="number" id="weight" class="form-input weight-value" placeholder="150" min="30" max="500">
                            <select id="weightUnit" class="form-select weight-unit">
                                <option value="lbs">lbs</option>
                                <option value="kg">kg</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Controls -->
        <div style="background: var(--bg-secondary); padding: 20px; border-radius: 12px; margin-top: 40px;">
            <h3 style="color: var(--text-primary); margin-bottom: 16px;">Test Controls</h3>
            <div style="display: flex; gap: 12px; flex-wrap: wrap;">
                <button class="btn btn-secondary" onclick="testDualUpload()">Switch to Dual Upload</button>
                <button class="btn btn-secondary" onclick="testProcessingScreen()">Test Processing Screen</button>
                <button class="btn btn-secondary" onclick="testVideoValidation()">Test Video Validation</button>
                <button class="btn btn-secondary" onclick="window.location.reload()">Reset Test</button>
            </div>
            
            <!-- Console Output -->
            <div style="margin-top: 16px;">
                <h4 style="color: var(--text-secondary); margin-bottom: 8px;">Console Output:</h4>
                <div id="consoleOutput" style="background: #1a1a1a; color: #00ff00; font-family: monospace; font-size: 12px; padding: 12px; border-radius: 6px; max-height: 200px; overflow-y: auto;">
                    <!-- Console messages will appear here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Test Scripts -->
    <script>
        // Capture console output for testing
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('consoleOutput');
        
        function addToConsole(message, type = 'log') {
            const div = document.createElement('div');
            div.style.color = type === 'error' ? '#ff4444' : '#00ff00';
            div.style.marginBottom = '2px';
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole(args.join(' '), 'error');
        };
        
        // Test functions
        async function testDualUpload() {
            if (window.uploadWorkflow) {
                window.uploadWorkflow.createDualUploadZones();
                console.log('✅ Dual upload zones created');
            } else {
                console.error('❌ Upload workflow not initialized');
            }
        }
        
        async function testProcessingScreen() {
            if (window.processingScreen) {
                window.processingScreen.show('test-analysis-id', {
                    side_video_id: 'test-side-video',
                    rear_video_id: null
                });
                console.log('✅ Processing screen shown');
            } else {
                console.error('❌ Processing screen not available');
            }
        }
        
        async function testVideoValidation() {
            // Create a fake video file for testing
            const fakeVideo = new File(['fake video content'], 'test-side-view.mp4', {
                type: 'video/mp4',
                lastModified: Date.now()
            });
            
            if (window.videoValidator) {
                try {
                    const validation = await window.videoValidator.validateVideo(fakeVideo, 'side');
                    console.log('✅ Video validation result:', JSON.stringify(validation, null, 2));
                } catch (error) {
                    console.error('❌ Video validation failed:', error.message);
                }
            } else {
                console.error('❌ Video validator not available');
            }
        }
        
        // Initialize test page
        console.log('🧪 MaxWattz Test Page loaded');
        console.log('📋 Available test functions: testDualUpload(), testProcessingScreen(), testVideoValidation()');
    </script>
    
    <!-- Main App (Module) -->
    <script type="module">
        import { ENV, validateEnv } from './config/env.js';
        
        console.log('🔧 Environment validation:', validateEnv());
        console.log('⚙️ Current environment:', ENV.APP_ENV);
        
        // Import and initialize services
        import { uploadWorkflow } from './js/services/upload-workflow.js';
        import { processingScreen } from './js/components/processing-screen.js';
        import { videoValidator } from './js/services/video-validator.js';
        
        // Make available globally for testing
        window.uploadWorkflow = uploadWorkflow;
        window.processingScreen = processingScreen;
        window.videoValidator = videoValidator;
        
        // Initialize workflow
        try {
            await uploadWorkflow.initialize();
            console.log('✅ Upload workflow initialized successfully');
        } catch (error) {
            console.error('❌ Failed to initialize upload workflow:', error);
        }
        
        console.log('🎯 Test page ready! Try uploading a video or use the test controls.');
    </script>
</body>
</html>