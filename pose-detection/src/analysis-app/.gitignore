# Environment variables - NEVER commit these
config/env.js
.env
.env.local
.env.production

# AWS credentials
aws-credentials.json
.aws/

# Modal credentials
.modal/
modal-credentials.json

# Supabase credentials
supabase-credentials.json

# Uploaded videos (local testing)
uploads/
temp/

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/

# Logs
*.log
npm-debug.log*
yarn-debug.log*

# Dependencies
node_modules/

# Build outputs
dist/
build/

# Test coverage
coverage/

# Local test data
test-videos/
test-outputs/