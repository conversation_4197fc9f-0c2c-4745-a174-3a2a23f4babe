/* Form Layout Updates for Compact Design */

/* Runner Information Form */
.runner-info {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 24px;
    margin-top: 32px;
}

.runner-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 20px;
}

/* Form Row - 3 Equal Sub-boxes */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
}

/* Form Groups - Sub-box containers */
.form-group {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    transition: all 0.2s ease;
}

.form-group:hover {
    border-color: var(--border-hover);
    background: var(--bg-tertiary);
}

.form-group label {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    text-align: center;
}

/* Height Input Container - Centered layout */
.height-inputs {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
}

.height-system {
    width: 80px;
}

.height-values {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
}

.height-values .form-select {
    width: 50px;
}

/* Weight Input Container - Centered layout */
.weight-inputs {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.weight-value {
    width: 80px;
}

.weight-unit {
    width: 60px;
}

/* Gender Select - Centered */
.form-group.gender-group .form-select {
    width: 120px;
}

/* Form Controls - Compact styling */
.form-select,
.form-input {
    height: 40px;
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.2s ease;
    text-align: center;
}

.form-select:hover,
.form-input:hover {
    border-color: var(--border-hover);
}

.form-select:focus,
.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Remove the view toggle from upload page */
.upload-container .view-toggle {
    display: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .form-group {
        width: 100%;
        margin: 0;
    }
    
    .height-inputs {
        flex-direction: row;
        justify-content: center;
    }
    
    .height-system {
        width: 70px;
    }
}