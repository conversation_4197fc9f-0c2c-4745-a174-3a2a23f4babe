/* Results Page Styles - Builds on existing main.css and components.css */

.results-page {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: var(--bg-primary, #ffffff);
    min-height: 100vh;
}

/* ===== RESULTS HEADER ===== */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid var(--border-color, #e5e5e5);
}

.results-header h1 {
    color: var(--text-primary, #333);
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 15px;
}

/* ===== LOADING STATES ===== */
.results-page.loading,
.results-page.processing {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.loading-spinner,
.processing-container {
    text-align: center;
    padding: 40px;
}

.processing-container {
    max-width: 500px;
}

.processing-container h2 {
    color: var(--text-primary, #333);
    margin: 20px 0 10px 0;
    font-size: 1.8rem;
}

.processing-container p {
    color: var(--text-secondary, #666);
    margin: 10px 0;
    font-size: 1.1rem;
    line-height: 1.5;
}

.processing-status {
    margin: 30px 0;
    padding: 20px;
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 12px;
    border: 1px solid var(--border-color, #e5e5e5);
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid var(--border-light, #f0f0f0);
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 600;
    color: var(--text-primary, #333);
}

.status-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.status-badge.completed {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status-badge.processing {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

/* ===== SPINNER ANIMATION ===== */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-light, #f0f0f0);
    border-top: 4px solid var(--primary-color, #007bff);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== ERROR STATES ===== */
.results-page.error {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.error-container {
    text-align: center;
    padding: 40px;
    max-width: 500px;
}

.error-container h2 {
    color: var(--error-color, #dc3545);
    margin-bottom: 15px;
}

.error-container p {
    color: var(--text-secondary, #666);
    margin-bottom: 25px;
    line-height: 1.5;
}

.error-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* ===== TABS CONTAINER ===== */
.tabs-container {
    margin-bottom: 30px;
}

.tabs-nav {
    display: flex;
    gap: 0;
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 12px;
    padding: 4px;
    border: 1px solid var(--border-color, #e5e5e5);
}

.tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    border: none;
    background: transparent;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.tab-button:hover {
    background: rgba(0, 123, 255, 0.1);
}

.tab-button.active {
    background: var(--primary-color, #007bff);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.tab-icon {
    font-size: 1.5rem;
    min-width: 24px;
}

.tab-content {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.tab-label {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 4px;
}

.tab-description {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.3;
}

.missing-view-notice {
    margin-top: 15px;
    display: flex;
    gap: 10px;
}

.missing-tab {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    background: var(--bg-light, #f5f5f5);
    border-radius: 8px;
    opacity: 0.6;
}

.missing-tab .tab-icon.disabled,
.missing-tab .tab-label.disabled,
.missing-tab .tab-description.disabled {
    opacity: 0.5;
}

/* ===== RESULTS CONTENT ===== */
.results-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    align-items: start;
}

@media (max-width: 1200px) {
    .results-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

.video-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color, #e5e5e5);
}

.metrics-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--border-color, #e5e5e5);
    position: sticky;
    top: 20px;
    max-height: calc(100vh - 40px);
    overflow-y: auto;
}

/* ===== METRICS DISPLAY ===== */
.metrics-display {
    width: 100%;
}

.metrics-header {
    margin-bottom: 25px;
    text-align: center;
}

.metrics-header h3 {
    color: var(--text-primary, #333);
    font-size: 1.4rem;
    font-weight: 700;
    margin: 0 0 8px 0;
}

.metrics-subtitle {
    color: var(--text-secondary, #666);
    font-size: 0.95rem;
    margin: 0;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.metric-card {
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
}

.metric-card.good {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.05);
}

.metric-card.warning {
    border-color: #ffc107;
    background: rgba(255, 193, 7, 0.05);
}

.metric-card.needs-attention {
    border-color: #dc3545;
    background: rgba(220, 53, 69, 0.05);
}

.metric-card.neutral {
    border-color: var(--border-color, #e5e5e5);
}

.metric-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.metric-icon {
    font-size: 1.3rem;
}

.metric-status {
    font-size: 1rem;
}

.metric-value {
    margin-bottom: 8px;
}

.metric-value .value {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary, #333);
    line-height: 1;
}

.metric-value .unit {
    font-size: 0.85rem;
    color: var(--text-secondary, #666);
    font-weight: 500;
}

.metric-label {
    font-weight: 600;
    color: var(--text-primary, #333);
    font-size: 0.95rem;
    margin-bottom: 6px;
}

.metric-description {
    font-size: 0.8rem;
    color: var(--text-secondary, #666);
    line-height: 1.3;
}

/* ===== METRICS SUMMARY ===== */
.metrics-summary {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color, #e5e5e5);
}

.summary-header h4 {
    color: var(--text-primary, #333);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 12px 0;
}

.summary-content {
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--text-secondary, #666);
    padding: 15px;
    background: var(--bg-secondary, #f8f9fa);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color, #007bff);
}

/* ===== NO RESULTS STATE ===== */
.no-results {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary, #666);
}

.no-results p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

/* ===== BUTTON STYLES (Extending existing) ===== */
.btn-primary, .btn-secondary, .btn-retry {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: var(--primary-color, #007bff);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-hover, #0056b3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-secondary {
    background: var(--bg-secondary, #f8f9fa);
    color: var(--text-primary, #333);
    border: 1px solid var(--border-color, #e5e5e5);
}

.btn-secondary:hover {
    background: var(--bg-light, #e9ecef);
    border-color: var(--border-hover, #adb5bd);
}

.btn-retry {
    background: var(--success-color, #28a745);
    color: white;
}

.btn-retry:hover {
    background: var(--success-hover, #218838);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .results-page {
        padding: 15px;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .results-header h1 {
        font-size: 2rem;
    }
    
    .tabs-nav {
        flex-direction: column;
    }
    
    .tab-button {
        justify-content: center;
        text-align: center;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .processing-container,
    .error-container {
        padding: 20px;
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* ===== DARK MODE SUPPORT (if needed) ===== */
@media (prefers-color-scheme: dark) {
    .results-page {
        --bg-primary: #1a1a1a;
        --bg-secondary: #2d2d2d;
        --bg-light: #404040;
        --text-primary: #ffffff;
        --text-secondary: #cccccc;
        --border-color: #404040;
        --border-light: #505050;
        --border-hover: #606060;
    }
}