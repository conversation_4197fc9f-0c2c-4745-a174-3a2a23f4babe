/* ============================================
   MaxWattz - Components Stylesheet
   ============================================ */

/* ============================================
   Upload Page Components
   ============================================ */

.upload-container {
    max-width: 1200px;
    margin: 0 auto;
}

.upload-header {
    text-align: center;
    margin-bottom: 40px;
}

.upload-header h2 {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.upload-header p {
    font-size: 16px;
    color: var(--text-muted);
}

/* View Toggle */
.view-toggle {
    display: flex;
    justify-content: center;
    margin-bottom: 32px;
    background: var(--bg-tertiary);
    padding: 6px;
    border-radius: 12px;
    width: fit-content;
    margin-left: auto;
    margin-right: auto;
    box-shadow: inset 0 1px 3px var(--shadow);
}

.view-option {
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.view-option.active {
    background: var(--bg-primary);
    color: var(--accent-primary);
    box-shadow: 0 2px 8px var(--shadow);
}

.view-option.coming-soon {
    opacity: 0.5;
    cursor: not-allowed;
}

.view-option.coming-soon::after {
    content: "Soon";
    position: absolute;
    top: -8px;
    right: -8px;
    background: #ed8936;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 600;
}

/* File Upload Area */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    padding: 40px;
    text-align: center;
    background: var(--bg-tertiary);
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 24px;
}

.upload-area:hover {
    border-color: var(--accent-primary);
    background: var(--bg-secondary);
}

.upload-area.dragover {
    border-color: var(--accent-primary);
    background: var(--bg-secondary);
    transform: scale(1.02);
}

.upload-icon {
    width: 48px;
    height: 48px;
    margin: 0 auto 16px;
    color: #a0aec0;
}

.upload-text h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.upload-text p {
    color: var(--text-muted);
    margin-bottom: 4px;
}

.file-types {
    font-size: 12px;
    color: var(--text-muted);
}

/* Runner Information Form */
.runner-info {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 1px 3px var(--shadow);
    border: 1px solid var(--border-color);
}

.runner-info h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 4px;
    font-size: 14px;
}

.form-input {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s ease;
    background: var(--bg-primary);
    color: var(--text-primary);
}

.form-input:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select {
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    background: var(--bg-primary);
    color: var(--text-primary);
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.form-select:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Height Input System */
.height-inputs {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.height-system {
    width: 100%;
}

.height-values {
    display: flex;
    gap: 8px;
}

.height-values select {
    flex: 1;
}

.imperial-height select:first-child {
    min-width: 60px;
}

.imperial-height select:last-child {
    min-width: 60px;
}

.metric-height select:first-child {
    min-width: 60px;
}

.metric-height select:last-child {
    min-width: 80px;
}

/* Weight Input System */
.weight-inputs {
    display: flex;
    gap: 8px;
    align-items: center;
}

.weight-value {
    flex: 2;
}

.weight-unit {
    flex: 1;
    min-width: 70px;
}

/* ============================================
   Analysis Page Components
   ============================================ */

.analysis-container {
    display: flex;
    gap: 24px;
    height: 100%;
}

/* Video Section - Exactly 1/3 width */
.video-section {
    flex: 0 0 calc(33.333% - 8px);
    display: flex;
    flex-direction: column;
}

.video-container {
    position: relative;
    background: #1a202c;
    border-radius: 8px;
    overflow: hidden;
    flex: 1;
    min-height: 400px;
}

.video-element {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.canvas-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 2;
}

/* Video Controls */
.video-controls {
    display: flex;
    gap: 8px;
    margin-top: 12px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 8px 16px;
    border-radius: 6px;
    border: none;
    font-weight: 500;
    font-size: 13px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.control-btn.play {
    background: #48bb78;
    color: white;
}

.control-btn.pause {
    background: #ed8936;
    color: white;
}

.control-btn.reset {
    background: #4299e1;
    color: white;
}

.control-btn.process {
    background: #9f7aea;
    color: white;
}

.control-btn:hover {
    transform: translateY(-1px);
    filter: brightness(1.1);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* Results Section - 2/3 width */
.results-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* Detection Metrics Panel */
.metrics-panel {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.metrics-panel h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
    margin-bottom: 16px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.metric-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f5f9;
}

.metric-item:last-child {
    border-bottom: none;
}

.metric-label {
    font-weight: 500;
    color: #4a5568;
    font-size: 14px;
}

.metric-value {
    font-weight: 600;
    color: #1a202c;
    font-size: 14px;
}

.metric-value.success {
    color: #38a169;
}

.metric-value.warning {
    color: #d69e2e;
}

.metric-value.error {
    color: #e53e3e;
}

/* Status Indicator */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    font-weight: 500;
    padding: 4px 8px;
    border-radius: 4px;
}

.status-indicator.idle {
    background: #f7fafc;
    color: #4a5568;
}

.status-indicator.processing {
    background: #fef5e7;
    color: #d69e2e;
}

.status-indicator.complete {
    background: #f0fff4;
    color: #38a169;
}

.status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: currentColor;
}

/* Debug Information */
.debug-panel {
    background: #1a202c;
    color: #e2e8f0;
    border-radius: 8px;
    padding: 16px;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
    font-size: 12px;
    max-height: 200px;
    overflow-y: auto;
}

.debug-panel pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
}

/* Keypoints Display */
.keypoints-section {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    flex: 1;
}

.keypoints-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.keypoints-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1a202c;
}

.filter-info {
    font-size: 12px;
    color: #718096;
}

.keypoints-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    max-height: 300px;
    overflow-y: auto;
}

.keypoint-item {
    padding: 8px 12px;
    background: #f7fafc;
    border-radius: 6px;
    font-size: 12px;
}

.keypoint-name {
    font-weight: 500;
    color: #2d3748;
    margin-bottom: 2px;
}

.keypoint-coords {
    color: #718096;
    font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', monospace;
}

/* ============================================
   Loading and Error States
   ============================================ */

.loading-spinner {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #667eea;
}

.spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e2e8f0;
    border-top-color: #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.error-message {
    background: #fed7d7;
    color: #c53030;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 4px solid #e53e3e;
    font-size: 14px;
}

.success-message {
    background: #c6f6d5;
    color: #2f855a;
    padding: 12px 16px;
    border-radius: 6px;
    border-left: 4px solid #38a169;
    font-size: 14px;
}

/* ============================================
   Responsive Components
   ============================================ */

@media (max-width: 1024px) {
    .analysis-container {
        flex-direction: column;
    }
    
    .video-section {
        flex: none;
        margin-bottom: 20px;
    }
    
    .results-section {
        flex: none;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .view-toggle {
        flex-direction: column;
        width: 100%;
    }
    
    .upload-area {
        padding: 24px 16px;
    }
    
    .runner-info {
        padding: 16px;
    }
    
    .keypoints-grid {
        grid-template-columns: 1fr;
    }
}

/* ============================================
   Animation Classes
   ============================================ */

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}