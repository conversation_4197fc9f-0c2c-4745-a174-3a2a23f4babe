/* ============================================
   MaxWattz - Main Stylesheet
   ============================================ */

/* CSS Variables for Theming */
:root {
    /* Light Theme */
    --bg-primary: #ffffff;
    --bg-secondary: #fafafa;
    --bg-tertiary: #f7fafc;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --border-hover: #cbd5e0;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Sidebar Light */
    --sidebar-bg: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
    --sidebar-text: #e2e8f0;
    --sidebar-border: #4a5568;
    --sidebar-hover: #4a5568;
    --sidebar-active: #667eea;
    
    /* Accent Colors */
    --accent-primary: #667eea;
    --accent-secondary: #764ba2;
    --success: #38a169;
    --warning: #d69e2e;
    --error: #e53e3e;
}

[data-theme="dark"] {
    /* Dark Theme */
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-tertiary: #4a5568;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --border-color: #4a5568;
    --border-hover: #718096;
    --shadow: rgba(0, 0, 0, 0.3);
    
    /* Sidebar Dark */
    --sidebar-bg: linear-gradient(180deg, #1a202c 0%, #0f0f23 100%);
    --sidebar-text: #cbd5e0;
    --sidebar-border: #2d3748;
    --sidebar-hover: #2d3748;
    --sidebar-active: #667eea;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Layout Container */
body {
    display: flex;
    height: 100vh;
    overflow: hidden;
}

/* ============================================
   Sidebar Navigation
   ============================================ */

.sidebar {
    width: 260px;
    background: var(--sidebar-bg);
    color: var(--sidebar-text);
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--sidebar-border);
    box-shadow: 2px 0 10px var(--shadow);
    z-index: 100;
    transition: width 0.3s ease, transform 0.3s ease;
    position: relative;
}

.sidebar.collapsed {
    width: 60px;
    cursor: pointer;
}

/* Visual hover feedback on collapsed sidebar */
.sidebar.collapsed:hover {
    background: linear-gradient(180deg, rgba(45, 55, 72, 0.95) 0%, rgba(26, 32, 44, 0.95) 100%);
    box-shadow: 2px 0 15px rgba(102, 126, 234, 0.2);
}

.sidebar.collapsed .sidebar-title,
.sidebar.collapsed .nav-item span,
.sidebar.collapsed .profile-text,
.sidebar.collapsed .theme-label {
    opacity: 0;
    transform: translateX(-10px);
    pointer-events: none;
}

/* Tooltip on hover when collapsed */
.sidebar.collapsed .nav-item {
    position: relative;
}

.sidebar.collapsed .nav-item:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    margin-left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
}

.sidebar-header {
    padding: 20px;
    padding-top: 60px; /* Space for toggle button */
    border-bottom: 1px solid var(--sidebar-border);
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
    min-width: 0;
}

.logo-image {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    flex-shrink: 0;
    object-fit: contain;
}

/* Fallback for logo placeholder */
.logo-placeholder {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 16px;
    flex-shrink: 0;
}

.sidebar-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--sidebar-text);
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

.sidebar-toggle {
    position: absolute;
    top: 16px;
    right: 16px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--sidebar-text);
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    z-index: 10;
}

.sidebar-toggle:hover {
    background-color: var(--sidebar-hover);
    border-color: rgba(255, 255, 255, 0.3);
}

.sidebar.collapsed .sidebar-toggle {
    right: 50%;
    transform: translateX(50%);
}

.toggle-icon {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.sidebar.collapsed .toggle-icon {
    transform: rotate(180deg);
}

/* Navigation Items */
.nav-items {
    list-style: none;
    padding: 16px 12px;
    flex: 1;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    margin-bottom: 4px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--sidebar-text);
    position: relative;
}

.nav-item:hover {
    background-color: var(--sidebar-hover);
    color: var(--sidebar-text);
}

.nav-item.active {
    background-color: var(--sidebar-active);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.nav-icon {
    width: 20px;
    height: 20px;
    stroke-width: 2;
}

.nav-item span {
    font-weight: 500;
    font-size: 14px;
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

/* Smooth icon centering when collapsed */
.sidebar.collapsed .nav-icon {
    margin: 0 auto;
}

/* Sidebar Footer */
.sidebar-footer {
    padding: 16px;
    border-top: 1px solid var(--sidebar-border);
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Theme Toggle */
.theme-toggle {
    margin-bottom: 8px;
}

.theme-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--sidebar-text);
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    position: relative;
    overflow: hidden;
}

.theme-button:hover {
    background-color: var(--sidebar-hover);
}

/* Keep theme button functional when collapsed */
.sidebar.collapsed .theme-button {
    padding: 8px;
    justify-content: center;
}

.sidebar.collapsed .theme-icon {
    margin: 0;
}

/* Tooltip for theme button when collapsed */
.sidebar.collapsed .theme-button:hover::after {
    content: attr(aria-label);
    position: absolute;
    left: 100%;
    margin-left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    white-space: nowrap;
    font-size: 12px;
    z-index: 1000;
    pointer-events: none;
}

.theme-icon {
    width: 16px;
    height: 16px;
    transition: opacity 0.3s ease;
}

.theme-icon.sun-icon {
    opacity: 1;
}

.theme-icon.moon-icon {
    opacity: 0;
    position: absolute;
}

[data-theme="dark"] .theme-icon.sun-icon {
    opacity: 0;
}

[data-theme="dark"] .theme-icon.moon-icon {
    opacity: 1;
    position: static;
}

.theme-label {
    font-size: 14px;
    font-weight: 500;
    transition: opacity 0.3s ease, transform 0.3s ease;
    white-space: nowrap;
    overflow: hidden;
}

[data-theme="dark"] .theme-label::after {
    content: " Mode";
}

.profile-button {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: var(--sidebar-text);
}

.profile-button:hover {
    background-color: var(--sidebar-hover);
}

.avatar-placeholder {
    width: 32px;
    height: 32px;
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    font-size: 14px;
}

.chevron {
    width: 16px;
    height: 16px;
    margin-left: auto;
    transition: transform 0.2s ease;
}

.profile-button:hover .chevron {
    transform: rotate(180deg);
}

/* ============================================
   Main Content Area
   ============================================ */

.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: var(--bg-primary);
    overflow: hidden;
    transition: margin-left 0.3s ease;
}

/* Header */
.main-header {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 20px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px var(--shadow);
}

.header-content h1 {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 4px;
}

.subtitle {
    font-size: 14px;
    color: var(--text-muted);
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 12px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--accent-primary) 0%, var(--accent-secondary) 100%);
    color: white;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    border-color: var(--border-hover);
}

/* Page Container */
.page-container {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
}

.page {
    display: none;
    height: 100%;
}

.page.active {
    display: block;
}

/* ============================================
   Responsive Design
   ============================================ */

@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .main-header {
        padding: 16px 20px;
    }
    
    .header-content h1 {
        font-size: 20px;
    }
    
    .page-container {
        padding: 20px;
    }
}

@media (max-width: 640px) {
    body {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        height: auto;
        flex-direction: row;
        overflow-x: auto;
    }
    
    .nav-items {
        display: flex;
        flex-direction: row;
        padding: 8px;
        gap: 8px;
        flex: 1;
    }
    
    .nav-item {
        white-space: nowrap;
        margin-bottom: 0;
        min-width: fit-content;
    }
    
    .sidebar-footer {
        padding: 8px;
    }
    
    .main-content {
        height: calc(100vh - 80px);
    }
}

/* ============================================
   Utility Classes
   ============================================ */

.text-center {
    text-align: center;
}

.hidden {
    display: none !important;
}

.loading {
    pointer-events: none;
    opacity: 0.6;
}

.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}