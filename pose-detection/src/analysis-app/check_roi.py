import json

# Load the new Modal results
with open('Michael_test_2_pose.json', 'r') as f:
    data = json.load(f)

# Check first few frames for ROI calculation
for i in range(min(5, len(data['frames']))):
    frame = data['frames'][i]
    keypoints = frame['keypoints']
    
    # Calculate ROI center from valid keypoints (excluding 0,0)
    valid_x = []
    valid_y = []
    invalid_count = 0
    
    for kp in keypoints:
        if kp['x'] != 0.0 or kp['y'] != 0.0:
            valid_x.append(kp['x'])
            valid_y.append(kp['y'])
        else:
            invalid_count += 1
    
    if valid_x and valid_y:
        roi_x = sum(valid_x) / len(valid_x)
        roi_y = sum(valid_y) / len(valid_y)
        print(f'Frame {i}: ROI center ({roi_x:.1f}, {roi_y:.1f}) from {len(valid_x)} valid keypoints ({invalid_count} invalid)')
    else:
        print(f'Frame {i}: All keypoints invalid!')

# Also check which keypoints are commonly at (0,0)
zero_counts = {}
for frame in data['frames'][:10]:  # Check first 10 frames
    for kp in frame['keypoints']:
        if kp['x'] == 0.0 and kp['y'] == 0.0:
            zero_counts[kp['name']] = zero_counts.get(kp['name'], 0) + 1

print("\nKeypoints frequently at (0,0) in first 10 frames:")
for name, count in sorted(zero_counts.items(), key=lambda x: x[1], reverse=True)[:10]:
    print(f"  {name}: {count}/10 frames")