"""
Check coordinates from pose inference results
"""
import json
import sys

def check_coordinates(json_file):
    """Analyze coordinates from pose detection results"""
    print(f"\n=== Analyzing coordinates from: {json_file} ===\n")
    
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    # Video dimensions
    width = data.get('videoWidth', 1080)
    height = data.get('videoHeight', 1920)
    print(f"Video dimensions: {width}x{height}")
    print(f"Expected center: ({width/2:.0f}, {height/2:.0f})")
    
    # Check frames
    frames = data.get('frames', [])
    if not frames:
        print("No frames found!")
        return
    
    print(f"\nTotal frames: {len(frames)}")
    
    # Analyze first few frames
    for i in range(min(5, len(frames))):
        frame = frames[i]
        keypoints = frame.get('keypoints', [])
        
        # Calculate ROI center from valid keypoints
        valid_kps = [kp for kp in keypoints if kp['x'] != 0.0 or kp['y'] != 0.0]
        
        if valid_kps:
            avg_x = sum(kp['x'] for kp in valid_kps) / len(valid_kps)
            avg_y = sum(kp['y'] for kp in valid_kps) / len(valid_kps)
            
            print(f"\nFrame {i}:")
            print(f"  Valid keypoints: {len(valid_kps)}/{len(keypoints)}")
            print(f"  ROI center: ({avg_x:.1f}, {avg_y:.1f})")
            
            # Check coordinate range
            min_x = min(kp['x'] for kp in valid_kps)
            max_x = max(kp['x'] for kp in valid_kps)
            min_y = min(kp['y'] for kp in valid_kps)
            max_y = max(kp['y'] for kp in valid_kps)
            
            print(f"  X range: {min_x:.1f} to {max_x:.1f}")
            print(f"  Y range: {min_y:.1f} to {max_y:.1f}")
            
            # Check if coordinates are reasonable
            if avg_x > 400 and avg_x < 700 and avg_y > 800 and avg_y < 1100:
                print("  ✅ Coordinates look CORRECT!")
            else:
                print("  ❌ Coordinates still off - expected center around (540, 960)")
            
            # Show a few sample keypoints
            print("  Sample keypoints:")
            for kp in valid_kps[:3]:
                print(f"    {kp['name']}: ({kp['x']:.1f}, {kp['y']:.1f}, {kp.get('z', 0):.1f})")
        else:
            print(f"\nFrame {i}: All keypoints at (0,0)")
    
    # Summary
    print("\n=== SUMMARY ===")
    all_x = []
    all_y = []
    for frame in frames[:10]:  # First 10 frames
        for kp in frame.get('keypoints', []):
            if kp['x'] != 0.0 or kp['y'] != 0.0:
                all_x.append(kp['x'])
                all_y.append(kp['y'])
    
    if all_x and all_y:
        avg_x_all = sum(all_x) / len(all_x)
        avg_y_all = sum(all_y) / len(all_y)
        
        print(f"Average position across first 10 frames: ({avg_x_all:.1f}, {avg_y_all:.1f})")
        print(f"Expected center: ({width/2:.0f}, {height/2:.0f})")
        
        # Calculate scale factor
        scale_x = avg_x_all / (width/2)
        scale_y = avg_y_all / (height/2)
        print(f"Scale factors: x={scale_x:.3f}, y={scale_y:.3f}")
        
        if scale_x < 0.2 and scale_y < 0.2:
            print("\n🔍 DIAGNOSIS: Coordinates are scaled down by ~90%")
            print("   This matches the untrained SmoothNet behavior!")
        elif scale_x > 0.8 and scale_y > 0.8:
            print("\n✅ SUCCESS: Coordinates are in the correct range!")
            print("   SmoothNet was the issue!")
        else:
            print("\n❓ UNEXPECTED: Coordinates have unusual scaling")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        check_coordinates(sys.argv[1])
    else:
        # Default files to check
        files_to_check = [
            "test_no_smoothnet_results.json",  # Without SmoothNet
            "Michael_test_2_pose.json",        # With SmoothNet
        ]
        
        for file in files_to_check:
            try:
                check_coordinates(file)
            except FileNotFoundError:
                print(f"File not found: {file}")