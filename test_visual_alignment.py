#!/usr/bin/env python3
"""
Quick test to verify that our processed pose data has reasonable coordinates
"""

import json
import sys

def check_pose_data_quality(json_file):
    """Analyze the quality of pose data for visual alignment testing"""
    
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    print(f"🔍 Analyzing pose data quality for: {data.get('video', 'Unknown')}")
    print(f"   Video dimensions: {data.get('videoWidth', 0)}×{data.get('videoHeight', 0)}")
    print(f"   Total frames: {len(data.get('frames', []))}")
    print()
    
    frames = data.get('frames', [])
    if not frames:
        print("❌ No frame data found!")
        return False
        
    # Analyze first few frames for data quality
    good_frames = 0
    zero_coordinate_frames = 0
    
    for i, frame in enumerate(frames[:10]):  # Check first 10 frames
        keypoints = frame.get('keypoints', [])
        if not keypoints:
            continue
            
        # Count keypoints with non-zero coordinates
        non_zero_keypoints = 0
        zero_keypoints = 0
        high_confidence_zeros = 0
        
        for kp in keypoints:
            x, y = kp.get('x', 0), kp.get('y', 0)
            score = kp.get('score', 0)
            
            if x == 0.0 and y == 0.0:
                zero_keypoints += 1
                if score > 0.8:  # High confidence but zero coordinates is suspicious
                    high_confidence_zeros += 1
            else:
                non_zero_keypoints += 1
        
        print(f"Frame {i}: {non_zero_keypoints} valid keypoints, {zero_keypoints} zero coordinates")
        if high_confidence_zeros > 0:
            print(f"   ⚠️  {high_confidence_zeros} high-confidence keypoints at (0,0) - suspicious!")
            
        if non_zero_keypoints >= 20:  # At least 20 keypoints with real coordinates
            good_frames += 1
        if zero_keypoints > 20:
            zero_coordinate_frames += 1
    
    print()
    print(f"📊 Data Quality Summary:")
    print(f"   Good frames (≥20 valid keypoints): {good_frames}/10")
    print(f"   Frames with excessive zeros: {zero_coordinate_frames}/10")
    
    # Show sample coordinates from best frame
    best_frame = None
    best_score = 0
    
    for frame in frames[:5]:
        keypoints = frame.get('keypoints', [])
        non_zero_count = sum(1 for kp in keypoints if kp.get('x', 0) != 0 or kp.get('y', 0) != 0)
        if non_zero_count > best_score:
            best_score = non_zero_count
            best_frame = frame
    
    if best_frame:
        print(f"\n📍 Sample coordinates from best frame ({best_score} valid keypoints):")
        for kp in best_frame.get('keypoints', [])[:5]:
            if kp.get('x', 0) != 0 or kp.get('y', 0) != 0:
                print(f"   {kp.get('name', 'unknown')}: ({kp.get('x', 0):.1f}, {kp.get('y', 0):.1f}) confidence: {kp.get('score', 0):.2f}")
    
    # Assessment
    if good_frames >= 7:
        print(f"\n✅ Data quality looks good - ready for visual testing!")
        print(f"💡 Test at: http://localhost:8000/test-full-player.html")
        return True
    elif good_frames >= 3:
        print(f"\n⚠️  Data quality is moderate - some frames may not display well")
        print(f"💡 Test at: http://localhost:8000/test-full-player.html")
        return True
    else:
        print(f"\n❌ Data quality is poor - many frames have invalid coordinates")
        print(f"🔧 Consider reprocessing the video or checking the Modal pipeline")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        json_file = "pose-detection/src/analysis-app/test-results-2.json"
        print(f"Using default file: {json_file}")
    else:
        json_file = sys.argv[1]
    
    try:
        check_pose_data_quality(json_file)
    except FileNotFoundError:
        print(f"❌ File not found: {json_file}")
        print("💡 Run: python3 download_results_2.py first")
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")