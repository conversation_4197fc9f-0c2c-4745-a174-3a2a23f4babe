#!/usr/bin/env python3
"""
Download the test video from S3 for local testing
"""

import boto3
import os
from pathlib import Path

def download_video():
    """Download the test video for local testing"""
    
    # S3 configuration
    bucket = 'maxwattz-videos'
    key = 'maxwattz-running-videos-raw-side/Michael_test_side.mp4'
    local_file = 'pose-detection/src/analysis-app/Michael_test_side.mp4'
    
    try:
        # Initialize S3 client (uses AWS credentials from environment)
        s3 = boto3.client('s3')
        
        print(f"📥 Downloading {key} from {bucket}...")
        print(f"   This may take a moment as video files are large...")
        
        # Download the file
        s3.download_file(bucket, key, local_file)
        
        # Get file size
        file_size = os.path.getsize(local_file)
        file_size_mb = file_size / (1024 * 1024)
        
        print(f"✅ Downloaded to: {local_file}")
        print(f"   File size: {file_size_mb:.1f} MB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error downloading video: {e}")
        print(f"\n💡 Alternative: Use AWS CLI:")
        print(f"   aws s3 cp s3://{bucket}/{key} {local_file}")
        return False

if __name__ == "__main__":
    success = download_video()
    
    if success:
        print(f"\n🚀 Video ready for local testing!")
        print(f"   The test page will now use: './Michael_test_side.mp4'")
    else:
        print(f"\n🔧 Setup AWS credentials first:")
        print(f"   export AWS_ACCESS_KEY_ID=your-key")
        print(f"   export AWS_SECRET_ACCESS_KEY=your-secret")