You are working on the Max Wattz video-analysis backend. Right now I want to replace our in-browser WebGL overlay with a fully-decoupled, server-side pipeline using my SmoothNet repository: https://github.com/MJPeakInsight/SmoothNet

### 1. Overview
- Users upload Side-View and/or Rear-View treadmill videos via our React web UI.
- Each raw video file should be stored in AWS S3 under:
    - s3://maxwattz-videos/maxwattz-running-videos-raw-side/
    - s3://maxwattz-videos/maxwattz-running-videos-raw-rear/
- We will not save the raw video bytes in Supabase; instead, Supabase stores only:
    - A generated S3 URL for each video (i.e. `side_video_url`, `rear_video_url`)
    - The JSON metrics output after processing

### 2. Modal Setup
- Use Modal’s Python or Node GPU service with the T4 (or A100) images.
- Write a Modal “function” that:
    1. Downloads the raw video from S3.
    2. Runs BlazePose frame extraction to get keypoints.
    3. Pipes the keypoint sequences through SmoothNet’s temporal refinement.
    4. Writes out a `.json` file of smoothed keypoints back to S3 under:
       s3://maxwattz-videos/maxwattz-running-metrics-side/ (or rear/)
    5. Signals completion (e.g. via webhook or direct Supabase API call).
- Keep the compute bound to < \$4 per 10-second video by batching within one Modal invocation.

### 3. SmoothNet Integration
- Clone `github.com/MJPeakInsight/SmoothNet` into the Modal container.
- In the processing script:
  ```python
  from smoothnet import SmoothNetRefiner
  refiner = SmoothNetRefiner(model_path='/path/to/checkpoint.pth')

  raw_keypoints = load_raw_keypoints_from_blazepose(...)
  smoothed_keypoints = refiner.refine(raw_keypoints)
  save_to_s3('.../maxwattz-running-metrics-side/xyz.json', smoothed_keypoints)

### 4. Notes
4. Supabase Table Changes
Create a table video_analyses (or modify existing) with columns:
-We will set this up shortly.id (uuid),user_id,view_type (side or rear),side_video_url / rear_video_url,metrics_url (S3 path to JSON), status (pending, processing, completed, error)

On Modal success, call Supabase’s REST API or client library to:
supabase
  .from('video_analyses')
  .update({ status: 'completed', metrics_url: 's3://…json' })
  .eq('id', analysisId)

### 5. Playback
After upload, poll the video_analyses row’s status until completed.
-Fetch the JSON from metrics_url.
-Render with <video> + Canvas: replay the video and draw the smoothed keypoints at each timestamp.
-Remove any live-inference code (tfjs-webgl) to avoid duplicate processing.
