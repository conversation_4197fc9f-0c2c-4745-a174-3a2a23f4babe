{"title": "SmoothNet Pre-trained Weights for Pose Smoothing (BlazePose)", "description": "Details on SmoothNet models, where to download them, how to load them, and how they are trained for 2D pose smoothing compatible with BlazePose.", "models": [{"name": "H36M-FCN-3D", "dataset": "Human3.6M", "estimator": "FCN", "type": "3D", "use_case": "Recommended for 2D BlazePose smoothing with x, y, z", "window_sizes": [8, 16, 32, 64], "download": "https://drive.google.com/drive/folders/1-9vUN3iv_2lYFyPBWIk9FJFE1i6_sEuX", "config_file": "configs/h36m_fcn_3D.yaml"}, {"name": "3DPW-SPIN-3D", "dataset": "3DPW", "estimator": "SPIN", "type": "3D", "use_case": "Outdoor pose estimation with SMPL fitting", "window_sizes": [8, 16, 32, 64], "download": "https://drive.google.com/drive/folders/1-9vUN3iv_2lYFyPBWIk9FJFE1i6_sEuX", "config_file": "configs/3dpw_spin_3D.yaml"}, {"name": "AIST-VIBE-3D", "dataset": "AIST++", "estimator": "VIBE", "type": "3D", "use_case": "Dance and complex motion smoothing", "window_sizes": [8, 16, 32, 64], "download": "https://drive.google.com/drive/folders/1-9vUN3iv_2lYFyPBWIk9FJFE1i6_sEuX", "config_file": "configs/aist_vibe_3D.yaml"}], "loading_instructions": {"steps": ["Load SmoothNet model definition from YAML config file.", "Use torch.load('checkpoint_32.pth.tar') to load checkpoint.", "Extract state_dict and load into model using model.load_state_dict().", "Prepare input keypoint sequences as shape (1, T, J*3) for inference.", "Call model.eval(); then run model(pose_tensor) to get smoothed results.", "Reshape output as needed (e.g., back to (T, J, 3))."], "alternatives": ["Use MMPose 0.x and SmoothNetFilter to simplify integration via the 'Smoother' API.", "Apply smoothing post-BlazePose extraction inside your Modal pipeline."]}, "training_background": {"data_generation": "Noisy pose sequences from pose estimators on Human3.6M, 3DPW, and AIST++ with ground-truth targets.", "losses": ["L1 position loss", "L1 acceleration loss"], "architecture": "8-layer MLP with residual connections, temporal-only model", "generalization": "Trained on 3D joints but applicable to 2D or pseudo-3D with x, y, z", "performance": {"parameters": "~0.33M", "speed": "1300 FPS (CPU), 46000+ FPS (GPU)"}}}