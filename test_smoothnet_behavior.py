"""
Test what an untrained SmoothNet model does to coordinate values
"""
import torch
import torch.nn as nn
import numpy as np

class SmoothNet(nn.Module):
    def __init__(self, window_size=16, output_size=16, hidden_size=512, 
                 res_hidden_size=256, num_blocks=3, dropout=0.5):
        super(SmoothNet, self).__init__()
        
        self.window_size = window_size
        self.output_size = output_size
        
        # Simple temporal smoothing network (same as in pose_inference.py)
        self.input_layer = nn.Linear(66, hidden_size)
        
        # Residual blocks for temporal modeling
        self.residual_blocks = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_size, res_hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(res_hidden_size, hidden_size),
                nn.ReLU()
            ) for _ in range(num_blocks)
        ])
        
        # Temporal convolution for smoothing
        self.temporal_conv = nn.Conv1d(hidden_size, hidden_size, 
                                     kernel_size=3, padding=1)
        
        # Output layer
        self.output_layer = nn.Linear(hidden_size, 66)
        
        # Activation and dropout
        self.relu = nn.ReLU()
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # x shape: (batch_size, 66, sequence_length)
        batch_size, input_dim, seq_len = x.shape
        
        # Transpose for linear layers
        x = x.transpose(1, 2)  # Shape: (batch_size, seq_len, 66)
        
        # Input transformation
        x = self.input_layer(x)
        x = self.relu(x)
        
        # Residual blocks
        for block in self.residual_blocks:
            residual = x
            x = block(x) + residual
        
        # Transpose for temporal convolution
        x = x.transpose(1, 2)
        
        # Apply temporal smoothing
        x = self.temporal_conv(x)
        x = self.relu(x)
        x = self.dropout(x)
        
        # Transpose back for output layer
        x = x.transpose(1, 2)
        
        # Output layer
        x = self.output_layer(x)
        
        # Take only the desired output length
        if self.output_size < seq_len:
            start_idx = (seq_len - self.output_size) // 2
            x = x[:, start_idx:start_idx + self.output_size, :]
        
        # Transpose back to original format
        x = x.transpose(1, 2)
        
        return x

def test_smoothnet_scaling():
    print("=== TESTING UNTRAINED SMOOTHNET BEHAVIOR ===\n")
    
    # Create untrained model (same parameters as in pose_inference.py)
    model = SmoothNet(
        window_size=16,
        output_size=16,
        hidden_size=512,
        res_hidden_size=256,
        num_blocks=3,
        dropout=0.5
    )
    model.eval()  # Set to evaluation mode
    
    print("Created untrained SmoothNet model")
    
    # Create test input: center coordinates (0.5, 0.5) for all keypoints
    # Shape: (1, 66, 16) - 1 batch, 66 coordinates (33 keypoints × 2), 16 frames
    batch_size = 1
    num_coords = 66  # 33 keypoints × 2 coordinates
    num_frames = 16
    
    # Test input: all normalized coordinates at center (0.5)
    test_input = torch.full((batch_size, num_coords, num_frames), 0.5)
    print(f"Input shape: {test_input.shape}")
    print(f"Input coordinate range: {test_input.min().item():.3f} to {test_input.max().item():.3f}")
    print(f"Input center coordinates: ({test_input[0, 0, 0].item():.3f}, {test_input[0, 1, 0].item():.3f})")
    
    # Run through untrained model
    with torch.no_grad():
        output = model(test_input)
    
    print(f"\nOutput shape: {output.shape}")
    print(f"Output coordinate range: {output.min().item():.3f} to {output.max().item():.3f}")
    print(f"Output center coordinates: ({output[0, 0, 0].item():.3f}, {output[0, 1, 0].item():.3f})")
    
    # Calculate scaling factor
    input_center = 0.5
    output_center_x = output[0, 0, 0].item()  # First x coordinate
    output_center_y = output[0, 1, 0].item()  # First y coordinate
    
    scale_x = output_center_x / input_center if input_center != 0 else 0
    scale_y = output_center_y / input_center if input_center != 0 else 0
    
    print(f"\nScaling analysis:")
    print(f"  Input: {input_center}")
    print(f"  Output X: {output_center_x:.6f} (scale: {scale_x:.6f})")
    print(f"  Output Y: {output_center_y:.6f} (scale: {scale_y:.6f})")
    
    # Check if this matches our observed scaling issue
    observed_scale_x = 0.074  # From our debug (40/540)
    observed_scale_y = 0.099  # From our debug (95/960)
    
    print(f"\nComparison with observed Modal results:")
    print(f"  Observed scale X: {observed_scale_x:.3f}")
    print(f"  Observed scale Y: {observed_scale_y:.3f}")
    print(f"  Model scale X: {scale_x:.3f}")
    print(f"  Model scale Y: {scale_y:.3f}")
    
    scale_match_x = abs(scale_x - observed_scale_x) < 0.05
    scale_match_y = abs(scale_y - observed_scale_y) < 0.05
    
    if scale_match_x and scale_match_y:
        print("\n🎯 SMOKING GUN: Untrained SmoothNet scaling matches observed results!")
        print("   The untrained model is causing the coordinate scaling issue.")
    else:
        print("\n❓ Scaling doesn't match - need to investigate further")
    
    # Test with different input values to see model behavior
    print(f"\n=== TESTING MODEL BEHAVIOR WITH DIFFERENT INPUTS ===")
    test_values = [0.0, 0.25, 0.5, 0.75, 1.0]
    
    for val in test_values:
        test_tensor = torch.full((batch_size, num_coords, num_frames), val)
        with torch.no_grad():
            result = model(test_tensor)
        output_val = result[0, 0, 0].item()
        scale = output_val / val if val != 0 else float('inf')
        print(f"  Input {val:.2f} → Output {output_val:.6f} (scale: {scale:.6f})")

if __name__ == "__main__":
    test_smoothnet_behavior()