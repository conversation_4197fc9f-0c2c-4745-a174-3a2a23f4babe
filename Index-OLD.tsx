import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Info } from 'lucide-react';
import Header from '@/components/Header';
import UploadPanel from '@/components/UploadPanel';
import ConfigurationPanel from '@/components/ConfigurationPanel';
import ProcessingPanel from '@/components/ProcessingPanel';
import ResultsPanel from '@/components/ResultsPanel';
type AppState = 'upload' | 'processing' | 'results';
interface VideoFile {
  file: File;
  url: string;
  name: string;
}
const Index = () => {
  const [currentState, setCurrentState] = useState<AppState>('upload');
  const [sideVideo, setSideVideo] = useState<VideoFile | null>(null);
  const [rearVideo, setRearVideo] = useState<VideoFile | null>(null);
  const [viewType, setViewType] = useState<'side' | 'rear' | 'coming-soon'>('side');
  const [height, setHeight] = useState({
    feet: 5,
    inches: 10
  });
  const [gender, setGender] = useState('');
  const [weight, setWeight] = useState('');
  const [weightUnit, setWeightUnit] = useState('lbs');

  // Fixed values for BlazePose Full implementation
  const analysisType = 'running';
  const analysisMode = '3D';
  const activityType = 'Running';
  const videoSetup = 'Treadmill';
  const overlayStyle = 'Medical';
  const analysisQuality = 'Full';
  const canStartAnalysis = sideVideo !== null;
  const handleStartAnalysis = () => {
    if (canStartAnalysis) {
      console.log('🚀 STARTING BLAZEPOSE FULL ANALYSIS:');
      console.log('analysisMode:', analysisMode);
      console.log('activityType:', activityType);
      console.log('videoSetup:', videoSetup);
      console.log('analysisQuality:', analysisQuality);
      console.log('userHeight:', height);
      setCurrentState('processing');
      // Simulate processing time
      setTimeout(() => {
        setCurrentState('results');
      }, 3000);
    }
  };
  const handleNewAnalysis = () => {
    setCurrentState('upload');
    setSideVideo(null);
    setRearVideo(null);
  };
  if (currentState === 'processing') {
    return <>
        <Header activityType={activityType} onActivityTypeChange={() => {}} // No-op since type is fixed
      isLocked={true} />
        <ProcessingPanel analysisMode={analysisMode} />
      </>;
  }
  if (currentState === 'results') {
    return <>
        <Header 
          activityType={activityType} 
          onActivityTypeChange={() => {}} // No-op since type is fixed
          isLocked={true} 
          viewType={viewType}
          onViewTypeChange={setViewType}
          showViewToggle={true}
          sideVideo={sideVideo}
          rearVideo={rearVideo}
        />
        <ResultsPanel 
          sideVideo={sideVideo} 
          rearVideo={rearVideo} 
          onNewAnalysis={handleNewAnalysis} 
          analysisMode={analysisMode} 
          videoSetup={videoSetup} 
          overlayStyle={overlayStyle} 
          analysisQuality={analysisQuality} 
          userHeight={height}
          viewType={viewType}
        />
      </>;
  }
  return <>
      <Header activityType={activityType} onActivityTypeChange={() => {}} // No-op since type is fixed
    isLocked={false} />
      
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-6xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gray-900">
              3D Running Analysis - BlazePose Full
            </h1>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Upload your treadmill videos for Max Wattz to perform your biomechanical analysis.
              Record from 5 feet away from the runner.
            </p>
            
            {/* Taking Good Videos Button - Prominent placement */}
            <div className="flex justify-center">
              <Dialog>
                <DialogTrigger asChild>
                  <Button size="lg" className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-3">
                    <Info className="w-5 h-5 mr-2" />
                    How to Take Good Videos
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle className="text-xl font-bold">How to Take Good Videos for Analysis</DialogTitle>
                  </DialogHeader>
                  
                  <div className="space-y-6">
                    {/* Side View Section */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-blue-600">Side View Setup</h3>
                      <div className="grid md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <img 
                            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop" 
                            alt="Side view positioning" 
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <p className="text-xs text-gray-600">Position phone exactly to the side of the runner</p>
                        </div>
                        <div className="text-center">
                          <img 
                            src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=300&h=200&fit=crop" 
                            alt="Phone height level" 
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <p className="text-xs text-gray-600">Keep phone level with runner's abdomen</p>
                        </div>
                        <div className="text-center">
                          <img 
                            src="https://images.unsplash.com/photo-1518770660439-4636190af475?w=300&h=200&fit=crop" 
                            alt="Distance measurement" 
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <p className="text-xs text-gray-600">Maintain 5 feet distance from runner</p>
                        </div>
                      </div>
                      <ul className="space-y-2 text-sm text-gray-700">
                        <li>• <strong>Positioning:</strong> Phone should be positioned exactly to the side of the runner, not at an angle</li>
                        <li>• <strong>Height:</strong> Keep the phone level with the runner's abdomen for optimal body tracking</li>
                        <li>• <strong>Distance:</strong> Maintain exactly 5 feet distance from the runner</li>
                      </ul>
                    </div>

                    {/* Rear View Section */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-green-600">Rear View Setup</h3>
                      <div className="grid md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <img 
                            src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=300&h=200&fit=crop" 
                            alt="Rear view alignment" 
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <p className="text-xs text-gray-600">Align phone directly behind runner</p>
                        </div>
                        <div className="text-center">
                          <img 
                            src="https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=300&h=200&fit=crop" 
                            alt="Back height level" 
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <p className="text-xs text-gray-600">Position at mid-back level</p>
                        </div>
                        <div className="text-center">
                          <img 
                            src="https://images.unsplash.com/photo-1518770660439-4636190af475?w=300&h=200&fit=crop" 
                            alt="Rear distance" 
                            className="w-full h-32 object-cover rounded-md mb-2"
                          />
                          <p className="text-xs text-gray-600">Keep 5 feet distance from behind</p>
                        </div>
                      </div>
                      <ul className="space-y-2 text-sm text-gray-700">
                        <li>• <strong>Alignment:</strong> Phone should be positioned directly behind the runner, not off to the side</li>
                        <li>• <strong>Height:</strong> Keep the phone level with the runner's mid-back area</li>
                        <li>• <strong>Distance:</strong> Maintain exactly 5 feet distance from behind the runner</li>
                      </ul>
                    </div>

                    {/* General Tips */}
                    <div className="bg-gray-50 p-4 rounded-md">
                      <h4 className="font-semibold mb-2">General Recording Tips:</h4>
                      <ul className="space-y-1 text-sm text-gray-700">
                        <li>• Record for at least 30 seconds to capture multiple stride cycles</li>
                        <li>• Ensure good lighting and avoid shadows on the runner</li>
                        <li>• Keep the phone steady - use a tripod if available</li>
                        <li>• Make sure the entire runner is visible in the frame</li>
                        <li>• Record in landscape mode for better coverage</li>
                      </ul>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {/* Upload Panels - Side and Rear Views */}
          <div className="grid md:grid-cols-2 gap-6">
            <UploadPanel title="Upload Side View Video" description="Record from the side to capture leg extension and body posture" acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB" video={sideVideo} onVideoUpload={setSideVideo} />
            <UploadPanel title="Upload Rear View Video" description="Record from behind to analyze stride symmetry and foot placement" acceptedFormats="MP4, MOV, AVI or WebM • Max 100MB" video={rearVideo} onVideoUpload={setRearVideo} />
          </div>

          {/* Configuration Panel - Minimized */}
          <Card>
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold mb-6 text-center">User Information</h3>
              
              <div className="grid md:grid-cols-3 gap-8">
                {/* Height */}
                <div className="text-center">
                  <label className="block text-sm font-medium text-gray-700 mb-3">Height</label>
                  <div className="flex gap-2 justify-center">
                    <select 
                      value={height.feet} 
                      onChange={e => setHeight({ ...height, feet: parseInt(e.target.value) })} 
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {[4, 5, 6, 7].map(ft => <option key={ft} value={ft}>{ft} ft</option>)}
                    </select>
                    <select 
                      value={height.inches} 
                      onChange={e => setHeight({ ...height, inches: parseInt(e.target.value) })} 
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {[...Array(12)].map((_, i) => <option key={i} value={i}>{i} in</option>)}
                    </select>
                  </div>
                </div>

                {/* Gender */}
                <div className="text-center">
                  <label className="block text-sm font-medium text-gray-700 mb-3">Gender</label>
                  <Select value={gender} onValueChange={setGender}>
                    <SelectTrigger className="w-full max-w-xs mx-auto">
                      <SelectValue placeholder="Select gender" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                      <SelectItem value="prefer-not-to-say">Prefer not to say</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Weight */}
                <div className="text-center">
                  <label className="block text-sm font-medium text-gray-700 mb-3">Weight</label>
                  <div className="flex gap-2 justify-center max-w-xs mx-auto">
                    <Input
                      type="number"
                      placeholder="Enter weight"
                      value={weight}
                      onChange={(e) => setWeight(e.target.value)}
                      className="flex-1"
                    />
                    <Select value={weightUnit} onValueChange={setWeightUnit}>
                      <SelectTrigger className="w-20">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="lbs">lbs</SelectItem>
                        <SelectItem value="kg">kg</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Start Analysis Button */}
          <Card>
            <CardContent className="p-6 text-center">
              <button onClick={handleStartAnalysis} disabled={!canStartAnalysis} className={`inline-flex items-center gap-3 px-8 py-4 rounded-lg text-lg font-semibold transition-all ${canStartAnalysis ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}>
                <div className="w-6 h-6 rounded-full border-2 border-current flex items-center justify-center">
                  <div className="w-2 h-2 rounded-full bg-current"></div>
                </div>
                Start 3D Analysis
              </button>
              {!canStartAnalysis && <p className="text-sm text-gray-500 mt-2">
                  Please upload at least a side view video to begin analysis
                </p>}
            </CardContent>
          </Card>
        </div>
      </div>
    </>;
};
export default Index;