# Modal SmoothNet Training Options

## Current Status
- **Problem Identified**: Multiple critical implementation issues causing coordinate displacement
  - Wrong checkpoint format (.pth vs .pth.tar)
  - **Tensor dimension mismatch**: SmoothNet expects (batch, time, features) not (batch, features, time)
  - Coordinate space mismatch: Using normalized pixel coords instead of world coordinates
- **Solution Progress**: 
  - ✅ Fixed checkpoint format - using official checkpoint_32.pth.tar
  - ✅ Fixed tensor dimensions - now using (1, T, 99) format
  - ✅ Fixed coordinate space conversion - now using root-relative world coordinates
- **Current Work**: Implementing 32-frame sliding window (SmoothNet standard)

## Available Pre-trained Models

### Primary Options
1. **H36M-FCN-3D** 
   - Best for: General 3D pose estimation
   - Download: https://drive.google.com/file/d/1ZketGlY4qA3kFp044T1-PaykV2llNUjB
   - Use case: Laboratory/controlled environments

2. **3DPW-SPIN-3D** ⭐ **RECOMMENDED**
   - Best for: In-the-wild scenarios (matches treadmill footage)
   - Download: https://drive.google.com/file/d/106MnTXFLfMlJ2W7Fvw2vAlsUuQFdUe6k
   - Use case: Real-world video analysis, similar to treadmill running

3. **AIST-VIBE-3D**
   - Best for: Dynamic movements and dance
   - Download: https://drive.google.com/file/d/101TH_Z8uiXD58d_xkuFTh5bI4NtRm_cK
   - Use case: High-motion activities

### Window Size Options
- **8 frames**: Ultra-low latency, minimal smoothing
- **16 frames**: Low latency, basic smoothing
- **32 frames**: Default - balanced latency/smoothing (RECOMMENDED)
- **64 frames**: Maximum smoothing, higher latency

## Technical Requirements

### Model Architecture
- **Input**: 99 channels (33 keypoints × 3 coordinates each)
- **Output**: 99 channels (smoothed 3D coordinates)
- **Temporal Window**: Configurable (8, 16, 32, 64 frames)

### Implementation Strategy
1. **Download Strategy**: Google Drive → S3 hosting for production
2. **Fallback**: Direct Google Drive download in Modal (requires special handling)
3. **Testing**: Start with disable_smoothnet=False + pre-trained weights

## Decision Matrix

| Model | Pros | Cons | Best For |
|-------|------|------|----------|
| H36M-FCN-3D | General purpose, well-tested | May not handle treadmill variations | Controlled environments |
| 3DPW-SPIN-3D | Real-world optimized, robust | Potentially over-smoothed | Treadmill running ⭐ |
| AIST-VIBE-3D | Handles dynamic motion | May be overkill for running | High-motion activities |

## Implementation Plan

### Phase 1: Quick Test
- Download 3DPW-SPIN-3D (32-frame window)
- Host on S3 for reliable access
- Update Modal pipeline to load weights

### Phase 2: Comparison
- Test all three models with same video
- Compare smoothing quality vs coordinate accuracy
- Measure processing time impact

### Phase 3: Production
- Select best-performing model
- Implement automatic fallback if weights unavailable
- Add model validation and error handling

## Current Implementation Status

### ✅ Completed
- Identified untrained SmoothNet as coordinate scaling issue
- Fixed coordinate transformation pipeline
- Verified 3D coordinate support (x,y,z with proper depth values)
- Updated tensor dimensions to 99 channels (33 keypoints × 3)
- **Downloaded official checkpoint format**: checkpoint_32.pth.tar (0.8 MB) from official Google Drive
- **Fixed tensor input format**: Changed from (1, 99, T) to (1, T, 99) to match SmoothNet expectations
  - Tensor creation: `torch.zeros(1, num_frames, 99)`
  - Data storage: `tensor[0, frame_idx, kp_idx * 3 + coord] = value`
  - Data extraction: `tensor[0, frame_idx, kp_idx * 3 + coord]`
  - Removed unnecessary transposes in model forward method
  - Updated sliding window processing to maintain correct format
- **Fixed coordinate space conversion**: Changed from normalized pixel coordinates to world coordinates
  - **World coordinate scaling**: Uses 2000mm / video_width (assumes ~2m treadmill space)
  - **Root-relative coordinates**: All keypoints relative to pelvis center (hip average)
  - **Bidirectional conversion**: pixel → world → root-relative → world → pixel
  - **Enhanced logging**: Shows coordinate ranges and sample values for verification
- **Implemented 32-frame sliding window**: Updated from 16-frame to standard 32-frame processing
  - **Window overlap**: 50% overlap (16-frame stride) for smooth blending
  - **Triangular weighting**: Center frames weighted higher than edge frames
  - **Short video handling**: Automatic padding for videos < 32 frames
  - **Enhanced monitoring**: Detailed logging of window operations and coverage

- **Loaded official SmoothNet config files**: ✅ Downloaded pw3d_spin_3D.yaml from cure-lab/SmoothNet
  - **Config stored**: s3://maxwattz-videos/model-weights/pw3d_spin_3D.yaml
  - **Architecture discovered**: HIDDEN_SIZE=512, RES_HIDDEN_SIZE=16, NUM_BLOCK=1, DROPOUT=0.5
  - **Key finding**: Official architecture differs from hardcoded values (res_hidden=16 not 256, blocks=1 not 3)
  - **Implementation**: Added `_load_smoothnet_config()` method to dynamically load config from S3
  - **Status**: Config loading implemented, model now uses official architecture parameters
- **Added coordinate validation**: ✅ Comprehensive validation before/after SmoothNet
  - **Pre-smoothing validation**: Checks input coordinate ranges, anomalies, center of mass
  - **Post-smoothing validation**: Verifies smoothing impact and coordinate centering
  - **Validation checks**: Out-of-bounds, zero coordinates, low confidence, center of mass deviation
  - **Anomaly detection**: Logs specific issues like misplaced nose or hip positions
  - **Impact summary**: Compares pre/post metrics to verify smoothing effectiveness
  - **Center verification**: Confirms coordinates moved to expected ~540,960 center position

### 📋 Pending
- Test coordinate accuracy with trained models
- Performance comparison between models
- Validate against expected center-frame positioning (~540, 960)

## Notes
- All models support 3D coordinates (matching our MediaPipe BlazePose output)
- Google Drive links require special download handling in Modal
- Consider S3 hosting for production reliability
- Window size directly affects both latency and smoothing quality