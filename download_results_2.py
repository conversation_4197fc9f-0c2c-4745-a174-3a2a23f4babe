#!/usr/bin/env python3
"""
Download Michael_test_2 results from S3 for verification
"""

import boto3
import json
import os

def download_results():
    """Download the processed results JSON for Michael_test_2"""
    
    # S3 configuration
    bucket = 'maxwattz-videos'
    key = 'maxwattz-running-metrics-side/Michael_test_2_pose.json'
    local_file = 'pose-detection/src/analysis-app/test-results-2.json'
    
    try:
        # Initialize S3 client
        s3 = boto3.client('s3')
        
        print(f"📥 Downloading {key} from {bucket}...")
        
        # Download the file
        s3.download_file(bucket, key, local_file)
        
        print(f"✅ Downloaded to: {local_file}")
        
        # Validate the JSON
        with open(local_file, 'r') as f:
            data = json.load(f)
            
        print(f"\n📊 Results summary:")
        print(f"   Model: {data.get('modelType', 'Unknown')}")
        print(f"   View: {data.get('viewType', 'Unknown')}")
        print(f"   Frames: {len(data.get('frames', []))}")
        print(f"   Processing time: {data.get('processingTime', 0):.1f}s")
        print(f"   Video dimensions: {data.get('videoWidth', 0)}×{data.get('videoHeight', 0)}")
        
        # Check for smoothing info
        if 'smoothingApplied' in data:
            print(f"   Smoothing: {data['smoothingApplied']}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error downloading results: {e}")
        return False

if __name__ == "__main__":
    download_results()