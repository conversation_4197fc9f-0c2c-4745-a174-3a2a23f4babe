# Modal Implementation Specification for MaxWattz Video Processing

## Overview
This document captures the complete specification for implementing server-side video processing using Modal GPU infrastructure, replacing the current in-browser WebGL BlazePose detection with a decoupled pipeline.

## Architecture Flow
1. Users upload Side/Rear view videos via React UI
2. Videos stored in S3: `maxwattz-videos` bucket
3. <PERSON><PERSON> processes videos with BlazePose + SmoothNet
4. Results stored back to S3 as JSON
5. Frontend fetches and renders smoothed keypoints

## Video Specifications

### Input Formats
- **Resolutions**: 
  - HD: 1080×1920 (Width × Height) - Portrait
  - 4K: 2160×3840 (Width × Height) - Portrait
- **Frame Rates**: 30 FPS or 60 FPS
- **Duration**: Maximum 10 seconds
- **Size**: Up to 200MB
- **Formats**: MP4, MOV, AVI, WebM

### Processing Rules
- 60 FPS videos: Sample every 2nd frame (process at 30 FPS)
- 30 FPS videos: Process every frame
- Include timestamp in milliseconds for each frame

## Model Configuration

### BlazePose Models
- **Side View**: Heavy model (highest accuracy)
- **Rear View**: Full or Lite model (balanced accuracy/speed)
- **Keypoints**: Currently 33 (MediaPipe limitation), planning for 39

### Model Complexity Mapping
```python
model_complexity = {
    'lite': 0,
    'full': 1,
    'heavy': 2
}
```

## Coordinate System

### Current TFJS Output (Frontend)
- **Format**: Pixel coordinates
- **Origin**: Top-left (0,0)
- **X Range**: 0 to video width
- **Y Range**: 0 to video height
- **Z Coordinate**: Scaled with video width

### MediaPipe Python Output (Modal)
- **Format**: Normalized coordinates (0-1)
- **Requires Conversion**: Must convert to pixels before saving

### Conversion Formula
```python
pixel_x = normalized_x * video_width
pixel_y = normalized_y * video_height
pixel_z = normalized_z * video_width  # Z scaled with width
```

## Output JSON Format

### Required Structure
```json
{
  "video": "filename.mp4",
  "videoWidth": 1080,
  "videoHeight": 1920,
  "fps": 30,
  "modelType": "heavy",
  "analysisId": "uuid",
  "viewType": "side",
  "frames": [
    {
      "frameNumber": 0,
      "timestamp": 0.0,
      "keypoints": [
        {
          "x": 540.5,
          "y": 960.2,
          "z": 150.3,
          "score": 0.95,
          "name": "nose"
        },
        // ... 33 keypoints total
      ]
    },
    // ... more frames
  ]
}
```

### Keypoint Names (33 BlazePose keypoints)
```javascript
[
  'nose', 'left_eye_inner', 'left_eye', 'left_eye_outer',
  'right_eye_inner', 'right_eye', 'right_eye_outer',
  'left_ear', 'right_ear', 'mouth_left', 'mouth_right',
  'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow',
  'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky',
  'left_index', 'right_index', 'left_thumb', 'right_thumb',
  'left_hip', 'right_hip', 'left_knee', 'right_knee',
  'left_ankle', 'right_ankle', 'left_heel', 'right_heel',
  'left_foot_index', 'right_foot_index'
]
```

## S3 Storage Structure

### Input Videos
- Side: `s3://maxwattz-videos/maxwattz-running-videos-raw-side/{filename}`
- Rear: `s3://maxwattz-videos/maxwattz-running-videos-raw-rear/{filename}`

### Output Metrics
- Side: `s3://maxwattz-videos/maxwattz-running-metrics-side/{filename}_pose.json`
- Rear: `s3://maxwattz-videos/maxwattz-running-metrics-rear/{filename}_pose.json`

## Modal Configuration

### Infrastructure
- **GPU**: A10G (T4-equivalent) standard, A100 for heavy loads
- **Timeout**: 600 seconds (10 minutes)
- **Secret**: `maxwattz-videos` (AWS credentials)
- **Cost Target**: < $4 per 10-second video

### Dependencies **UPDATED**
```python
image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install([
        "libgl1-mesa-glx", 
        "libglib2.0-0", 
        "ffmpeg",
        "libgomp1",  # Required for MediaPipe multi-threading
        "libxcb1"    # Required for MediaPipe GUI components
    ])
    .pip_install([
        "opencv-python~=4.8.0",
        "mediapipe==0.10.0",
        "torch==2.0.1",
        "torchvision==0.15.2",
        "numpy>=1.21",
        "boto3>=1.26.0",
        "psycopg2-binary>=2.9.0",  # for Supabase
        # SmoothNet integration pending Phase 2
    ])
)
```

## Supabase Integration

### Update Method
- Direct database updates using existing `supabase-client.js` approach
- No webhook endpoint needed
- Update `bio_modal_processing_queue` and `bio_run_analysis` tables

### Required Updates
1. Set `status` to 'completed' or 'error'
2. Store S3 URL of results
3. Update processing timestamps
4. Store any error messages

## Frontend Integration

### Keypoint Display Filtering
The frontend filters out these keypoints for cleaner running visualization:
- Eyes: indices 1-6
- Mouth: indices 9-10
- Fingers: indices 17-22

### Canvas Synchronization
- Canvas always matches video display size
- Coordinates are drawn directly without transformation
- Real-world measurements calculated separately using CoordinateScaler

## Processing Pipeline Steps **CURRENT STATUS**

1. **Download video from S3** ✅ *Implemented*
2. **Extract video metadata** (width, height, fps) ✅ *Implemented*
3. **Process frames** ✅ *Implemented*:
   - Sample based on FPS (every frame for 30fps, every 2nd for 60fps)
   - Run BlazePose detection with Heavy/Full model
   - Convert normalized to pixel coordinates
   - Collect all keypoints with confidence scores
4. **Apply SmoothNet** temporal smoothing ✅ *Framework Complete*:
   - ✅ Fixed coordinate scaling issue (untrained weights caused ~90% reduction)
   - ✅ Updated to 3D coordinates (99 channels: 33 keypoints × 3 dimensions)
   - ✅ Fixed Z-axis depth preservation (negative values from MediaPipe)
   - ✅ UUID-based output filenames to avoid overwriting
   - 🔄 Ready for pre-trained weight implementation
5. **Save results to S3** as JSON ✅ *Implemented*
6. **Update Supabase** with completion status ✅ *Implemented*

## Error Handling

### Validation Checks
- Verify video file exists and is readable
- Check frame extraction success
- Validate keypoint detection (minimum confidence threshold)
- Ensure all 33 keypoints present

### Error Reporting **ENHANCED**
- Log detailed errors to Modal console
- Update Supabase with error status and message  
- Return meaningful error messages
- **Added**: FPS validation and division-by-zero protection
- **Added**: Model verification with test frame
- **Added**: Proper MediaPipe resource cleanup
- **Added**: Comprehensive logging at each pipeline step

## Performance Considerations

### Frame Processing
- Downscale 4K videos if needed for faster processing
- Use frame skipping for 60fps videos
- Batch process frames when possible

### Memory Management
- Release video capture after processing
- Clear frame buffers
- Properly dispose of tensors

## Testing Requirements

### Test Cases
1. HD 30fps video processing
2. 4K 60fps video processing
3. Error handling (corrupted video)
4. Supabase update verification
5. S3 upload/download validation

### Validation
- Output JSON matches expected format exactly
- Pixel coordinates in correct range
- All 33 keypoints present per frame
- Timestamps accurate

## Future Enhancements

### Phase 2
- Support for 39 keypoints when available
- Real-time progress updates via websocket
- Batch processing multiple videos
- Advanced biomechanical metrics calculation

### Phase 3
- Direct GPU-accelerated BlazePose (non-MediaPipe)
- Custom model training for running-specific poses
- Multi-view synchronization (side + rear)

---

**Last Updated**: January 2025
**Status**: Phase 1 Complete, SmoothNet Framework Complete
**Current Work**: Pre-trained weight implementation, playback optimization
**Next Steps**: Deploy production SmoothNet model, enhance visualization

## SmoothNet Optimization Strategy

### Immediate GPU Fix (Current Priority)
- Ensure model runs on GPU with proper device placement
- Add explicit `.cuda()` calls and device checks
- Expected to resolve timeout issues

### Future Optimization Plan
**Phase 1 - Quick Win (If GPU fix insufficient)**
- Implement lightweight temporal filtering (EMA, Savitzky-Golay)
- No neural network overhead, instant results

**Phase 2 - Progressive Enhancement (As data grows)**
- 2-3 videos: Lightweight filtering only
- 10-20 videos: Reduced SmoothNet (hidden_size: 256, blocks: 2)
- 50+ videos: Full SmoothNet with fine-tuning
- 100+ videos: Custom running-specific model

**Adaptive Processing Modes**
- RAW: No smoothing
- LIGHT: Fast mathematical filtering  
- MEDIUM: Reduced neural network
- FULL: Complete SmoothNet model

Selection based on video length and available GPU resources.