# Product Requirements Document: Modal Video Processing Implementation

## 🎯 Project Overview

### Objective
Replace in-browser WebGL pose detection with a scalable server-side GPU processing pipeline using Modal, BlazePose, and SmoothNet for the MaxWattz running analysis platform.

### Success Criteria
- ✅ Videos processed server-side with < 2 min turnaround for 10-second clips
- ✅ Cost maintained under $4 per 10-second video
- ✅ Seamless integration with existing frontend (no breaking changes)
- ✅ 99%+ detection accuracy matching current TFJS implementation
- ✅ Smooth temporal keypoint data reducing jitter by 50%+

---

## 📋 Implementation Phases

### Phase 1: Core Modal Function Development (Week 1)

#### 1.1 Basic Modal Setup ✅ **COMPLETE**
- [x] Modal account creation and CLI setup
- [x] AWS S3 secret configuration (`maxwattz-videos`)
- [x] Project structure initialization
- [x] Create `modal.toml` configuration file
- [x] Test basic S3 connectivity
- [x] Fix AWS credentials (SignatureDoesNotMatch resolved)
- [x] Verify S3 bucket structure and access

#### 1.2 Video Processing Function ✅ **COMPLETE**
- [x] Create `pose_inference.py` with proper image configuration
- [x] Implement video download from S3
- [x] Extract video metadata (dimensions, FPS)
- [x] Implement frame extraction logic
  - [x] Handle 30fps (process all frames)
  - [x] Handle 60fps (sample every 2nd frame)
- [x] Add proper error handling and logging
- [x] Add FPS validation and division-by-zero protection
- [x] Fix timestamp calculation for frame sampling

#### 1.3 BlazePose Integration ✅ **COMPLETE**
- [x] Setup MediaPipe with correct model complexity
  - [x] Heavy model for side view (complexity=2)
  - [x] Full model for rear view (complexity=1)
- [x] Implement pose detection per frame
- [x] Convert normalized to pixel coordinates
- [x] Validate 33 keypoint output
- [x] Add confidence score validation (min 0.5)
- [x] Add MediaPipe dependencies (libgomp1, libxcb1)
- [x] Add model verification with test frame
- [x] Proper MediaPipe import handling

#### 1.4 Coordinate Conversion ✅ **COMPLETE**
- [x] Implement normalized → pixel conversion
  - [x] x: normalized * videoWidth
  - [x] y: normalized * videoHeight
  - [x] z: normalized * videoWidth
- [x] Include keypoint names in output
- [x] Validate coordinate ranges
- [x] Handle portrait video dimensions (1080×1920, 2160×3840)

### Phase 2: SmoothNet & Output Generation (Week 1-2)

#### 2.1 SmoothNet Integration ✅ **FRAMEWORK COMPLETE**
- [x] Created custom SmoothNet implementation
- [x] Implemented data format conversion (keypoints ↔ tensors)
- [x] Built sliding window processing with overlap
- [x] Integrated into Modal pipeline
- [x] **FIXED**: Coordinate scaling issue (untrained weights caused 90% reduction)
- [x] **UPGRADED**: 3D coordinate support (99 channels: 33 keypoints × 3D)
- [x] **FIXED**: Z-axis depth preservation (negative MediaPipe values)
- [x] **IMPROVED**: UUID-based output filenames prevent overwriting
- [🔄] **CURRENT**: Implementing pre-trained model weights

##### Pre-trained Model Options ✅ **COMPLETE**
**Available Models:**
- **3DPW-SPIN-3D** ⭐ Recommended for treadmill scenarios
- **H36M-FCN-3D** - General 3D pose estimation
- **AIST-VIBE-3D** - Dynamic movement optimization

**Implementation Status:**
- [x] Research completed, download links identified
- [x] Documentation created (MODAL_SMOOTHNET_TRAINING.md)
- [ ] Download and implement 3DPW-SPIN-3D model
- [ ] Test coordinate accuracy with trained weights
- [ ] Performance comparison and validation

#### 2.2 JSON Output Generation ✅ **COMPLETE**
- [x] Create proper JSON structure with metadata
- [x] Include all required fields:
  - [x] video filename, dimensions, fps
  - [x] modelType, analysisId, viewType
  - [x] frame-by-frame keypoints with timestamps
- [x] Implement S3 upload to metrics bucket
- [x] Verify JSON matches frontend expectations

#### 2.3 Supabase Integration
- [ ] Add psycopg2 dependency
- [ ] Implement database connection
- [ ] Update `bio_modal_processing_queue` status
- [ ] Update `bio_run_analysis` with results
- [ ] Handle connection errors gracefully

### Phase 3: Frontend Integration (Week 2)

#### 3.1 Processing Trigger
- [ ] Modify upload workflow to trigger Modal
- [ ] Add Modal job ID tracking
- [ ] Implement status polling
- [ ] Update UI with processing progress

#### 3.2 Results Playback Component
- [ ] Create component to fetch JSON from S3
- [ ] Parse and validate smoothed keypoints
- [ ] Implement frame synchronization
- [ ] Render keypoints on video overlay
- [ ] Match existing visualization style

#### 3.3 Remove Live Inference
- [ ] Disable TFJS detector initialization
- [ ] Remove real-time processing code
- [ ] Switch to pre-processed data playback
- [ ] Maintain development mode toggle

### Phase 4: Testing & Optimization (Week 2-3)

#### 4.1 End-to-End Testing
- [ ] Test HD portrait videos (1080×1920)
- [ ] Test 4K portrait videos (2160×3840)
- [ ] Test 30fps processing
- [ ] Test 60fps with frame sampling
- [ ] Validate coordinate accuracy
- [ ] Verify Supabase updates

#### 4.2 Performance Optimization
- [ ] Profile GPU utilization
- [ ] Optimize batch processing
- [ ] Implement cost monitoring
- [ ] Add performance metrics logging
- [ ] Validate < $4 per video target

#### 4.3 Error Handling
- [ ] Test corrupted video handling
- [ ] Test network interruptions
- [ ] Validate error reporting to Supabase
- [ ] Implement retry logic
- [ ] Add comprehensive logging

### Phase 5: Production Deployment (Week 3)

#### 5.1 Modal Deployment
- [ ] Deploy Modal function to production
- [ ] Configure production secrets
- [ ] Set up monitoring/alerts
- [ ] Implement auto-scaling policies

#### 5.2 Integration Testing
- [ ] Full pipeline test with real users
- [ ] Load testing (multiple concurrent videos)
- [ ] Failover testing
- [ ] Performance benchmarking

#### 5.3 Documentation
- [ ] Update API documentation
- [ ] Create troubleshooting guide
- [ ] Document cost optimization tips
- [ ] Training for support team

---

## 🚀 Quick Start Checklist

### Immediate Next Steps **UPDATED**
1. [x] Create `modal.toml` in project root
2. [x] Create `pose_inference.py` with basic structure
3. [x] Test S3 connectivity with simple file read
4. [x] Implement basic video processing loop
5. [x] Add MediaPipe and test detection
6. [x] Debug test video processing issues
7. [x] Integrate SmoothNet temporal smoothing framework
8. [🔄] **CURRENT**: Fix GPU acceleration for SmoothNet
9. [ ] Add Supabase status updates
10. [ ] Frontend integration for results playback

### Daily Progress Tracking
- [ ] Morning: Review overnight Modal runs
- [ ] Check cost metrics
- [ ] Review error logs
- [ ] Update task status
- [ ] Evening: Deploy updates and test

---

## 📊 Success Metrics

### Technical Metrics
- **Processing Time**: < 120 seconds for 10-second video
- **Accuracy**: 99%+ pose detection rate
- **Cost**: < $4 per video
- **Uptime**: 99.9% availability
- **Error Rate**: < 1% failed processing

### Business Metrics
- **User Satisfaction**: Maintain current UX quality
- **Processing Volume**: Support 1000+ videos/day
- **Turnaround Time**: Results available within 2 minutes
- **Cost Efficiency**: 70% reduction vs. client-side GPU

---

## 🔧 Technical Dependencies

### Modal Resources
- GPU: A10G (standard), A100 (heavy load)
- Memory: 16GB minimum
- Storage: 50GB for model caching
- Timeout: 600 seconds

### External Services
- AWS S3: `maxwattz-videos` bucket
- Supabase: Direct database access
- SmoothNet: GitHub fork integration

---

## 📝 Risk Mitigation

### Technical Risks
1. **MediaPipe 33 vs 39 keypoints**
   - Mitigation: Document limitation, plan future upgrade

2. **Coordinate system mismatch**
   - Mitigation: Thorough testing, validation suite

3. **Cost overruns**
   - Mitigation: Implement cost alerts, optimize batching

### Operational Risks
1. **Modal downtime**
   - Mitigation: Implement fallback to client-side

2. **S3 connectivity issues**
   - Mitigation: Retry logic, regional redundancy

---

## 🎊 Definition of Done

### Phase 1 Complete When:
- [x] Modal function processes test video successfully **COMPLETE**
- [x] Output JSON matches specification exactly
- [x] Coordinates validated (pixel format maintained)
- [x] All infrastructure and dependencies working
- [x] S3 connectivity established
- [x] BlazePose Heavy model verified

### Phase 2 Complete When:
- [ ] SmoothNet reduces jitter by 50%+
- [ ] Supabase updates working reliably
- [ ] Cost per video confirmed < $4

### Phase 3 Complete When:
- [ ] Frontend displays pre-processed poses
- [ ] No visual difference from live processing
- [ ] Performance improved by 30%+

### Phase 4 Complete When:
- [ ] All test cases passing
- [ ] Error rate < 1%
- [ ] Documentation complete

### Phase 5 Complete When:
- [ ] Production deployment stable
- [ ] Monitoring in place
- [ ] Team trained on operations

---

**Sprint Start Date**: ___________
**Target Completion**: ___________
**Product Owner**: ___________
**Tech Lead**: ___________

*Let's get this across the finish line! 🏃‍♂️*
