"""
Create a test analysis in Supabase for Modal testing with <PERSON>_test_2.MOV
"""

import modal
from datetime import datetime
import uuid

# Define image with Supabase dependency
image = modal.Image.debian_slim(python_version="3.11").pip_install([
    "supabase>=2.0.0",
    "psycopg2-binary>=2.9.0"
])

app = modal.App(
    name="maxwattz-create-analysis",
    image=image,
    secrets=[modal.Secret.from_name("maxwattz-supabase")]
)

@app.function()
def create_test_analysis(user_email: str = "<EMAIL>"):
    """Create a test analysis record for Modal processing"""
    import os
    from supabase import create_client, Client
    
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_SERVICE_KEY')
    
    if not supabase_url or not supabase_key:
        return {"success": False, "error": "Missing credentials"}
    
    supabase: Client = create_client(supabase_url, supabase_key)
    
    try:
        # First create a video record for the test video
        video_data = {
            'filename': 'Michael_test_2.MOV',
            'original_filename': '<PERSON>_test_2.MOV',
            'file_size_bytes': 50000000,  # 50MB estimate
            'mime_type': 'video/quicktime',
            's3_bucket': 'maxwattz-videos',
            's3_key': 'maxwattz-running-videos-raw-side/Michael_test_2.MOV',
            's3_url': 'https://maxwattz-videos.s3.amazonaws.com/maxwattz-running-videos-raw-side/Michael_test_2.MOV',
            's3_upload_completed': True,
            'duration_seconds': 10.0,
            'width': 1080,
            'height': 1920,
            'fps': 30.0,
            'codec': 'h264',
            'view_type': 'side',
            'is_valid': True,
            'user_email': user_email,
            'session_id': str(uuid.uuid4())
        }
        
        print("Creating test video record...")
        video_result = supabase.table('bio_run_videos') \
            .insert(video_data) \
            .execute()
        
        if not video_result.data:
            return {"success": False, "error": "Failed to create video record"}
            
        video_id = video_result.data[0]['id']
        print(f"✅ Created video: {video_id}")
        
        # Now create analysis record with video reference
        analysis_data = {
            'user_email': user_email,
            'height_inches': 70,  # 5'10"
            'height_display': '5\'10"',
            'gender': 'male',
            'weight_lbs': 165,
            'weight_display': '165 lbs',
            'side_video_id': video_id,  # Reference the video
            'status': 'pending',
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        print("Creating test analysis...")
        result = supabase.table('bio_run_analysis') \
            .insert(analysis_data) \
            .execute()
        
        if result.data:
            analysis = result.data[0]
            analysis_id = analysis['id']
            print(f"✅ Created analysis: {analysis_id}")
            print(f"   User: {user_email}")
            print(f"   Status: pending")
            print(f"   Side video: {video_id}")
            
            # Also create a queue entry for side view
            queue_data = {
                'analysis_id': analysis_id,
                'video_id': video_id,
                'view_type': 'side',
                'status': 'queued',
                'modal_function_name': 'process_running_video',
                'created_at': datetime.now().isoformat()
            }
            
            queue_result = supabase.table('bio_modal_processing_queue') \
                .insert(queue_data) \
                .execute()
            
            if queue_result.data:
                queue_id = queue_result.data[0]['id']
                print(f"✅ Created queue entry: {queue_id}")
                
                return {
                    "success": True,
                    "analysis_id": analysis_id,
                    "queue_id": queue_id,
                    "video_id": video_id,
                    "command": f"modal run pose_inference.py --video Michael_test_2.MOV --analysis-id {analysis_id} --queue-id {queue_id} --view-type side"
                }
            
        return {"success": False, "error": "Failed to create records"}
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"success": False, "error": str(e)}

@app.local_entrypoint()
def main(email: str = "<EMAIL>"):
    result = create_test_analysis.remote(email)
    
    if result.get('success'):
        print(f"\n📋 Run this command to process with proper IDs:")
        print(f"{result['command']}")
        print(f"\n📊 Created records:")
        print(f"   Analysis ID: {result['analysis_id']}")
        print(f"   Queue ID: {result['queue_id']}")
        print(f"   Video ID: {result['video_id']}")
    else:
        print(f"\n❌ Failed: {result.get('error')}")

if __name__ == "__main__":
    import sys
    email = sys.argv[1] if len(sys.argv) > 1 else "<EMAIL>"
    main(email)