Adapting the YOLO Example to BlazePose + SmoothNet
Here's how you can pass maxwattz-videos to your Modal function:
import modal

app = modal.App(image=modal.Image.debian_slim().pip_install("boto3"))


@app.function(secrets=[modal.Secret.from_name("maxwattz-videos")])
def read_s3_file(bucket, key):
    import boto3

    s3 = boto3.client("s3")
    response = s3.get_object(Bucket=bucket, Key=key)
    contents = response["Body"].read()
    return contents.decode("utf-8")


@app.local_entrypoint()
def main():
    read_s3_file.remote("modal-public-assets", "hello.txt")

Below we outline the changes and additions needed to turn the YOLO template into a BlazePose+SmoothNet video pose inference app. We’ll cover container setup, S3 mounting, model integration, and the inference logic. 1. Container & Dependencies: Configure a Modal image with all the libraries for BlazePose and SmoothNet.
Start from Modal’s base image (Debian slim with a specific Python version).
System packages: BlazePose (from MediaPipe) and OpenCV may require some system libraries. The YOLO example installed libgl1-mesa-glx and libglib2.0-0 to support OpenCV GUI functions. Additionally, for video processing, FFmpeg is useful (for decoding video streams in OpenCV). We can apt-install ffmpeg or any codec libs if necessary.
Python packages: We need:
OpenCV (opencv-python) for video I/O and possibly drawing/visualization.
Mediapipe for BlazePose, or an alternative implementation. The simplest route is to use Google’s Mediapipe library (e.g. pip install mediapipe) which provides BlazePose models (on CPU by default). If GPU acceleration for BlazePose is needed, you might consider a PyTorch-based pose estimator (such as MMPose’s BlazePose model or RTMPose, which is a more recent pose model). For this guide, we’ll assume Mediapipe for ease of integration.
PyTorch and Torchvision if not already included. (Ultralytics/Yolo might pull in torch, but to be explicit, include torch and torchvision matching your CUDA version.)
SmoothNet: The official SmoothNet implementation isn’t a pip package, but it’s integrated into MMPose/MMHuman3D. One easy way is to install MMHuman3D (which will bring in SmoothNet’s code as a smoothing plugin). For example, pip install mmhuman3d (and its dependencies like mmcv). This might be heavy if we only need SmoothNet, so alternatively, you can install SmoothNet from a Git repo. If the repository has no setup.py, you could add it as a git submodule or download the weights directly. For a beginner-friendly approach, we’ll use the high-level API from MMHuman3D for SmoothNet. (If using MMHuman3D, also install a matching version of mmcv and mmpose – consult their docs for compatibility.)
NumPy for array manipulations (often needed for pose data).
For example, your modal.Image definition in code could look like:
python
Copy
Edit
image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install(["libgl1-mesa-glx", "libglib2.0-0", "ffmpeg"])  # system deps
    .pip_install([
        "opencv-python~=4.8.0",   # OpenCV for video handling
        "mediapipe==0.10.0",      # BlazePose via MediaPipe
        "torch==2.0.1", "torchvision==0.15.2",  # PyTorch (adjust versions as needed)
        "mmhuman3d==0.16.0",      # includes SmoothNet (ensure CUDA compatibility)
        "numpy>=1.21"
    ])
)
Explanation: We install OpenCV and mediapipe. Mediapipe contains BlazePose models (it will download them on first use). We add PyTorch; Modal’s base images don’t include it by default, so we explicitly install a version compatible with our CUDA (Modal’s A10G has CUDA 11.x – torch 2.0.1+cu118 is suitable, for example). We also install mmhuman3d which will bring in the SmoothNet functionality (MMHuman3D in v0.16 has SmoothNet as a built-in smoothing method). Note that mmhuman3d will internally install mmcv and other dependencies; to avoid long builds, you could pip install a pre-built mmcv-full wheel appropriate for your CUDA. Alternatively, skip mmhuman3d and directly use the SmoothNet repo: you could do
python
Copy
Edit
.pip_install(["git+https://github.com/cure-lab/SmoothNet.git"])
and handle its requirements similarly. For simplicity, we’ll proceed with the MMHuman3D approach. 2. Modal App & S3 Bucket Mount: Enable easy S3 file access by mounting the S3 bucket.
AWS Credentials: In Modal, create a Secret for your AWS credentials. For instance, go to the Modal dashboard’s Secrets section and add a new secret (as shown in Modal’s docs) called "s3-bucket-secret" with keys AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY (and optionally AWS_DEFAULT_REGION if needed). This secret will be referenced in our code to grant the container access to S3.
CloudBucketMount: We will mount the S3 bucket at a path in the container’s filesystem. In code, after defining the image, do:
python
Copy
Edit
aws_secret = modal.Secret.from_name("s3-bucket-secret")
MOUNT_PATH = "/mnt/s3"  # path inside container for the bucket
app = modal.App(
    name="pose-inference-blazepose",
    image=image,
    volumes={ MOUNT_PATH: modal.CloudBucketMount("your-s3-bucket-name", secret=aws_secret) }
)
Now everything under /mnt/s3 in the container corresponds to files in your S3 bucket. For example, /mnt/s3/videos/input.mp4 would be the object s3://your-s3-bucket-name/videos/input.mp4. We can read/write as if it’s local. (Modal handles syncing in the background – e.g., writing to /mnt/s3/output/pose.json will upload to S3). Tip: If your S3 bucket is in a non-default region or requires special config, you can set environment variables or use modal.CloudBucketMount(..., mount_path=..., region="us-west-2") etc., but typically the secret’s keys are enough if the bucket is in the default region of those credentials.
3. Define the GPU-accelerated Pose Estimation function: Following the pattern from the YOLO example, we define a class that will load the models once and provide a method to process videos.
python
Copy
Edit
@app.cls(gpu="A10G", timeout=600)  # request one A10G GPU, 10 minute timeout (adjust as needed)
class PoseEstimator:
    def __enter__(self):
        """Load models into memory when a container starts."""
        # Initialize BlazePose (e.g., MediaPipe Pose)
        import mediapipe as mp
        self.pose = mp.solutions.pose.Pose(model_complexity=1, enable_segmentation=False)
        # ^ model_complexity=1 for full BlazePose Heavy; use 0 for BlazePose Lite if faster inference needed.

        # Initialize SmoothNet (from MMHuman3D)
        from mmhuman3d.core.post_processing import smooth_pose_sequence
        # SmoothNet is used via the smooth_pose_sequence function, no heavy model load here if using MMHuman3D.
        self.smooth_func = smooth_pose_sequence

        # If using a direct SmoothNet model (e.g., from cure-lab repo), you would load the pretrained weights:
        # self.smoothnet_model = load_smoothnet_model(weights_path)
        # (Make sure to move it to GPU if using PyTorch, e.g., .to('cuda'))

    @modal.method()
    def process(self, video_path: str) -> str:
        """Process a video: run BlazePose on each frame, apply SmoothNet, save results to S3."""
        import cv2, numpy as np
        mp = __import__("mediapipe")  # import inside method to avoid issues (already imported in __enter__)

        # Open the video file (video_path is the path *inside the container*, e.g., "/mnt/s3/videos/vid.mp4")
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise FileNotFoundError(f"Could not open video: {video_path}")

        keypoints_list = []  # to collect pose keypoints for each frame
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break  # end of video
            frame_count += 1
            # BlazePose inference:
            # Convert frame to RGB as mediapipe expects RGB input
            image_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.pose.process(image_rgb)
            if results.pose_landmarks:
                # Extract (x,y,z) for each of the 33 BlazePose keypoints:
                landmarks = results.pose_landmarks.landmark
                keypoints = [(lm.x, lm.y, lm.z) for lm in landmarks]
            else:
                # No detection (blank frame), use None or zeros
                keypoints = [(0.0, 0.0, 0.0)] * 33
            keypoints_list.append(keypoints)
        cap.release()

        # Convert keypoints_list to a numpy array for SmoothNet: shape (T, J, D)
        pose_arr = np.array(keypoints_list, dtype=np.float32)  # shape = (T, 33, 3)
        # SmoothNet expects the pose in a certain format. If using MMHuman3D's smooth_pose_sequence:
        # We need to reshape it to (T, J*D) or provide axis arguments depending on their API.
        # Let's assume smooth_pose_sequence can take (T, J, D) directly for 'smoothnet' method.
        smoothed = self.smooth_func(pose_arr, method='smoothnet')
        # If smooth_pose_sequence returns the same shape (T, J, D) numpy array:
        smoothed_poses = smoothed  # alias for clarity

        # Prepare output (for example, save smoothed keypoints as JSON)
        import json, os
        video_filename = os.path.basename(video_path)
        output_filename = video_filename.rsplit('.', 1)[0] + "_pose.json"
        output_path = f"/mnt/s3/results/{output_filename}"
        # Create results directory if not exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        # Build a serializable structure (list of frames, each frame is list of keypoints)
        output_data = {
            "video": video_filename,
            "frames": []
        }
        for frame_idx, pose in enumerate(smoothed_poses):
            frame_data = {"frame": frame_idx, "keypoints": []}
            for (x, y, z) in pose:
                frame_data["keypoints"].append({"x": float(x), "y": float(y), "z": float(z)})
            output_data["frames"].append(frame_data)
        # Write JSON to the mounted S3 path:
        with open(output_path, "w") as f:
            json.dump(output_data, f)

        return output_path  # returning the S3 path of the result file
Let’s break down what this does:
We decorate the class with @app.cls(gpu="A10G") to ensure it runs on a GPU node. The A10G (T4-equivalent) is usually sufficient for BlazePose (which is lightweight) and SmoothNet. If you anticipate very large videos or heavier models, you could use gpu="A100" (which has more memory and compute) – just keep in mind cost and availability.
The __enter__ method (automatically run once per container start) loads our models:
BlazePose: Using MediaPipe’s high-level API. We create a Pose object with model_complexity=1 (which uses the full BlazePose Heavy model for better accuracy; use 0 for BlazePose Lite if you need faster inference and are okay with slightly lower accuracy).
SmoothNet: We set up the smoothing function. With MMHuman3D, smooth_pose_sequence(..., method='smoothnet') internally loads a SmoothNet model the first time it’s called (using a default pretrained model) and applies it to the input sequence. This approach abstracts away the direct model handling. If we weren’t using MMHuman3D, we would manually load SmoothNet’s PyTorch model. SmoothNet is essentially a “temporal-only refinement network” that attaches to existing pose estimators for jitter mitigation
github.com
. It learns to smooth the pose trajectories without altering the underlying motion significantly.
We don’t explicitly move anything to CUDA here because MediaPipe runs on CPU by default (there is a way to use GPU in mediapipe, but it might require building from source with CUDA support – for now, CPU is fine for BlazePose since it’s quite fast). SmoothNet via MMHuman3D will utilize PyTorch – by default it should use CPU too unless we move data to GPU. Given an A10G is available, we could accelerate SmoothNet by transferring the pose sequence to GPU and executing the model there. If using smooth_pose_sequence, we’d need to check if it automatically uses GPU; if not, one could manually instantiate the SmoothNet model from MMHuman3D and call .cuda() on it.
Important: If BlazePose on CPU becomes a bottleneck, consider switching to a PyTorch-based pose model that can run on the GPU (for example, OpenMMLab’s MMPose has models like HRNet or RTMPose that can achieve similar or better accuracy on GPU). That would involve a different library, but since the question specifically mentions BlazePose, we stuck with MediaPipe for now.
The process method is where the video is processed:
We use OpenCV (cv2.VideoCapture) to read frames from the video file stored in the mounted S3 path.
For each frame, we convert it to RGB and run self.pose.process(frame) from MediaPipe, which returns pose landmarks if detected. BlazePose provides 33 keypoints (in world coordinates with depth – we use (x, y, z) here). We collect these into keypoints_list. If a frame has no detection, we add a placeholder (here zeros) to keep sequence length consistent.
After looping through all frames, we convert the collected list into a NumPy array of shape (T, 33, 3), where T is the number of frames. This is fed into SmoothNet. SmoothNet will smooth out the trajectory of each keypoint over time. By minimizing jerkiness, it produces a more stable sequence
github.com
github.com
. (SmoothNet was trained to reduce both positional errors and acceleration (jerk) of poses
github.com
, resulting in visibly smoother motion).
We then prepare to save the results. In this example, we create a JSON file containing the smoothed keypoints frame by frame. We assemble a Python dict and dump it as JSON to the output_path under /mnt/s3/results/. When we open that path for writing, we are actually writing to the S3 bucket (Modal streams the data to S3). The result will be in s3://your-bucket/results/<video_name>_pose.json.
We return the output path (container path). When calling this Modal function, the returned string can be used to know where the result is. (If triggered via CLI, we’ll see a print; if via Python API, you get the return value.)
A few notes on the above code:
- Ensure that the mmhuman3d SmoothNet usage matches the expected input shapes. In some cases, you might need to format the array as (T, J*D) or add an extra dimension. The concept, however, remains: take the time-series of keypoints and apply SmoothNet. The SmoothNet paper and code show it’s versatile: it can smooth 2D or 3D poses by treating each coordinate sequence independently. Our approach is consistent with that design (smoothing each joint’s trajectory over time).
- The output JSON could be large for long videos. Depending on your needs, you might instead save a compressed NumPy .npz or a CSV of keypoints. JSON is human-readable but may not scale well. Since the question is about integration and pipeline, JSON is fine for an intermediate format (QuestDB or another service can ingest it later).
- The results could also be extended: e.g., you could generate an overlay video with the pose skeleton drawn on each frame using OpenCV and save that to S3 for easy review. This would involve drawing lines between keypoints and writing video frames, which is an enhancement to consider (not done here to keep things focused). 4. Modal Entrypoint for CLI Testing: To run this as a script (and for easy integration with orchestration), we add a local entrypoint. This is similar to how the YOLO example provides a main function that can be invoked with modal run. For our case:
python
Copy
Edit
@app.local_entrypoint()
def main(video: str):
    """Entry point for CLI or orchestrator. `video` is the S3 path or bucket key of the video to process."""
    # Construct the full container path for the video in S3
    input_path = f"/mnt/s3/{video}" if not video.startswith("/mnt/s3") else video
    # e.g., if video="videos/run1.mp4", input_path="/mnt/s3/videos/run1.mp4"
    result = PoseEstimator().process(input_path)
    print(f"✅ Pose inference complete. Results saved to: {result}")
This allows us (or Dagster) to kick off the job with a simple command, specifying the video to process. The video argument could be given as just the S3 key (path in bucket) like "videos/my_run.mp4" or an s3:// URL. In the above, we ensure it’s in the form the container expects (/mnt/s3/...). We then instantiate the PoseEstimator class and call its process method. Using .process(...) (without remote or call) inside the @app.local_entrypoint will run it synchronously in the same container that Modal spins up, which is fine for a single video. (If you wanted to process multiple videos in parallel, you could use PoseEstimator().process.map(list_of_paths) to fan out tasks – Modal will spin up multiple containers.) The print statement will output a message to the logs/console when done. It includes the result path so we know where to find the output. Now we have a complete Modal app definition for BlazePose + SmoothNet!
Step-by-Step Setup and Deployment Guide
This section will walk you through setting up the project, running it, and integrating it into your pipeline, assuming a beginner level of familiarity: Step 1: Modal Account & CLI Setup – If you haven’t already:
Create an account on Modal.com (free tier provides some GPU hours which should cover testing).
Install the Modal CLI by running pip install modal-client on your local machine (or wherever you’re orchestrating from).
Authenticate the CLI by running modal token new and following the prompts to copy your API token. This links the CLI to your Modal account.
(If you already went through “Modal Account Setup”, ensure you can run modal --help and see no errors, meaning you’re logged in.)
Step 2: Prepare Project Files –
Choose a project directory (e.g., maxwattz_pose_inference/). Inside it, create a file named modal.toml with the following content:
toml
Copy
Edit
[project]
name = "maxwattz-pose-inference"
This defines the project name on Modal (and isolates its logs, secrets usage, etc.). The name can be anything, but it helps to use something descriptive. This file also allows you to use modal run and modal deploy without extra flags.
Create a new Python script, e.g. pose_inference.py, and open it in an editor.
Step 3: Write the Modal App Code – In pose_inference.py, build the app as described earlier:
Imports and Image Definition: Import modal and other necessary modules. Then define the image = modal.Image... with apt and pip installs. Use the code snippet from section 1 above as a guide. Ensure versions are compatible (you might adjust versions based on availability or use latest stable versions).
Secret & S3 Mount: Import Path from pathlib (for constructing paths) and use modal.Secret.from_name and modal.CloudBucketMount as shown in section 2. Insert your actual S3 bucket name in place of "your-s3-bucket-name". (Make sure you created the s3-bucket-secret in Modal console containing your AWS creds.)
PoseEstimator Class: Write the @app.cls class as in section 3. Be careful to get indentation right (since it’s within the script). You can start with the code provided, and adjust as needed:
If using a different BlazePose approach (say, loading a PyTorch BlazePose model), replace the mediapipe parts with your model load and inference code.
If not using MMHuman3D, replace the smoothing part with direct SmoothNet usage. For example, if you installed SmoothNet from the Git repo and it provides a class or function, use that. (The SmoothNet official repo provides scripts; using MMHuman3D is a shortcut to avoid dealing with model files. MMHuman3D by default will download a pretrained SmoothNet model when you call smooth_pose_sequence the first time, likely one trained on 3D data which works for 2D as well
github.com
.)
Local Entrypoint: Add the @app.local_entrypoint function as in section 4 to tie everything together for running from CLI. This function should parse an argument for the video path. Note that Modal can automatically inject CLI args into the function parameters, as we did with video: str.
Double-check that your code does not have syntax errors and all sections are properly closed (especially the class definition and its methods). Step 4: Set Environment Variables (Secrets) in Modal – We already created the s3-bucket-secret for AWS keys. If your SmoothNet or BlazePose setup requires any additional environment variables, you can set them via image.env(...) or via secrets. For example:
If using Hugging Face Hub or other sources, you might need tokens – those can be additional secrets.
If you need to specify CUDA_VISIBLE_DEVICES or other CUDA env variables, Modal handles device allocation, so you typically don’t need to set these manually. Modal will make the GPU available and PyTorch should find it. You can call torch.cuda.is_available() inside the container to confirm.
For reproducibility, you might pin package versions in the pip install. The ones given are examples; feel free to update (e.g., newer mediapipe or mmhuman3d if available, and matching torch version).
Step 5: Test Run the Modal App (CLI) – Now that everything is set up:
Ensure your video file is in the specified S3 bucket (e.g., s3://your-bucket/videos/test.mp4). If not, upload a test video there. (Tip: start with a short video for faster testing.)
From your project directory, run the Modal app using the CLI:
bash
Copy
Edit
modal run pose_inference.py --video videos/test.mp4
Modal will:
Build the container image (this happens on the first run or when you change the Docker dependencies). You’ll see logs about installing apt packages and pip packages. This can take a few minutes especially for torch and mmhuman3d. (Subsequent runs will use a cached image, so it will be much faster.)
Start a container with the image, mount the S3 bucket, and execute the main() function, passing video="videos/test.mp4" (which our code turns into /mnt/s3/videos/test.mp4 inside the container).
In the logs, you should see output from our code: for example, OpenCV opening the video, BlazePose processing frames, any prints we added, and finally the completion message with the results path.
If everything succeeds, you’ll get a print like ✅ Pose inference complete. Results saved to: /mnt/s3/results/test_pose.json. This indicates the JSON was written to the S3 bucket in the results/ folder. Go check your S3 bucket: you should find results/test_pose.json now present. You can download it and inspect the contents (it will contain the smoothed keypoints).
Troubleshooting: If the run fails, the CLI will show an error log. Common issues could be missing dependencies or typos. For example, if mediapipe tries to use a missing library, you might need to install an additional apt package (check the error for hints, e.g., GLIBCXX errors might require a different base image or GCC, etc.). If SmoothNet (MMHuman3D) fails, ensure the version compatibility of mmcv and that a suitable CUDA is present (the A10G has CUDA 11.4/11.8 compatibility – our torch 2.0.1+cu118 should match). If you see an AssertionError or similar from mmhuman3d about device, you might need to explicitly put the data on GPU (pose_arr = torch.tensor(pose_arr).to('cuda') and call a SmoothNet model). In our simple use, CPU might suffice.
Once it runs correctly, you have a working pipeline! 🎉
Step 6: Integrate into Max Wattz Pipeline – With the Modal app working via CLI, the next step is to tie it into your orchestration (Dagster in the Max Wattz architecture):
From Dagster or another orchestrator: You can invoke the Modal app using a shell command or the Modal SDK. For instance, in a Dagster Python solid/op, you might use subprocess.run(["modal", "run", "pose_inference.py", "--video", video_key]) to trigger it for a given S3 key. Modal will handle the rest. This is simple but incurs the container startup each time.
Alternatively, use the Modal Python API for a more programmatic approach. You can import your pose_inference.py as a module (it has a modal.App, so be careful to not accidentally run it locally – use if __name__ == "__main__": guards if needed). Modal provides a way to schedule or trigger functions via stub. For example:
python
Copy
Edit
import modal
stub = modal.Stub.lookup("maxwattz-pose-inference")  # use the project name to lookup if deployed
stub.app.deploy()  # (if not already deployed)
# Now call the function:
stub.PoseEstimator.process.call(f"/mnt/s3/videos/{video_key}")
This will invoke the function in the cloud and return when done. Check Modal’s documentation for using the stub API in scripts for more details.
Deploy (Optional): If videos come in frequently, deploying the app can reduce latency. Running modal deploy pose_inference.py will create a persistent deployment. You could then, for example, expose an HTTP endpoint for this function (using @app.web_endpoint) or keep a warm pool of GPUs. For a batch job scenario, deploying may not be strictly necessary – you can just use modal run on demand. However, deployment does allow monitoring and reusing the same app without rebuilding each time.
Scheduling (Optional): Modal can schedule periodic runs (cron jobs) via @app.function(schedule=modal.Period(...)). For example, if you wanted to scan a bucket for new videos every hour. In our case, new videos are event-driven (triggered by Supabase/Dagster), so scheduling is not needed; we respond to events instead.
Step 7: Validation and Next Steps – After integration:
Verify that when a user uploads a video, Dagster successfully triggers the Modal job and that the output (smoothed keypoints JSON, etc.) appears in S3. This output can then be consumed by QuestDB or FalkorDB as per the architecture. For instance, a QuestDB ingester could read the JSON and insert records into a time-series DB (or you modify the Modal code to directly push to QuestDB’s REST API).
Monitor performance: BlazePose via MediaPipe on CPU might handle ~30 FPS easily. If videos are high resolution (1080p/4K), you might downscale frames before pose detection to speed it up (BlazePose was often applied to ~256x256 inputs). You can add frame = cv2.resize(frame, (width, height)) before processing to control this. The A10G GPU will largely be idle in the current setup (since mediapipe is on CPU). For a GPU boost, consider using a different model (e.g., a PyTorch pose model) – this could significantly speed up processing of large videos.
SmoothNet’s effect can be verified by comparing unsmoothed vs smoothed keypoint plots. SmoothNet will introduce a short latency (half the window size) if used in near-online mode, but since we process offline, it’s fine. The default SmoothNet window is 32 frames, which is ~1 second for 30 FPS video; this balances smoothness and adaptability.
Extending the pipeline: Now that BlazePose+SmoothNet is integrated, you can enrich the pipeline as described in the Max Wattz workflows. For example, after smoothing, compute biomechanical metrics (e.g., detect overpronation from foot angles, etc.) and store those in FalkorDB. Those steps can either be additional code after SmoothNet in the Modal function or separate functions. Because SmoothNet improves data quality, any downstream analysis (like detecting “form issues” or counting steps) will be more reliable.
If you encounter issues with Modal (e.g., needing more time for long videos), you can increase the timeout on the @app.cls or break the work into smaller tasks (process in chunks). Modal’s logging and the web dashboard are very useful to watch while the job runs – you can see logs in real time and even stream printouts from within the container.
By following this guide, you’ve transformed the YOLO Modal example into a BlazePose + SmoothNet inference service. This service is GPU-enabled, uses PyTorch under the hood, fetches data from S3 and returns results to S3, and aligns with the Max Wattz architecture’s processing layer. The combination of BlazePose for pose estimation and SmoothNet for temporal smoothing will yield smooth pose time-series data, reducing jitter for any analytics that follow
github.com
. With a clear, step-by-step setup, this should be accessible to a beginner while also being powerful enough for production use.

import modal

# 1. Define container image with required system and Python dependencies
image = (
    modal.Image.debian_slim(python_version="3.11")
    .apt_install(["libgl1-mesa-glx", "libglib2.0-0", "ffmpeg"])
    .pip_install([
        "opencv-python~=4.8.0",
        "mediapipe==0.10.0",
        "torch==2.0.1",
        "torchvision==0.15.2",
        "mmhuman3d==0.16.0",
        "numpy>=1.21",
        "git+https://github.com/MJPeakInsight/SmoothNet.git"
    ])
)

# 2. Mount S3 bucket via Modal Secret and CloudBucketMount
aws_secret = modal.Secret.from_name("s3-bucket-secret")  # configure in Modal console
MOUNT_PATH = "/mnt/s3"
app = modal.App(
    name="maxwattz-pose-inference",
    image=image,
    volumes={MOUNT_PATH: modal.CloudBucketMount("maxwattz-videos", secret=aws_secret)}
)

# 3. GPU-powered pose extraction and smoothing class
@app.cls(gpu="A10G", timeout=600)
class PoseEstimator:
    def __enter__(self):
        import mediapipe as mp
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(model_complexity=1)
        from mmhuman3d.core.post_processing import smooth_pose_sequence
        self.smooth = smooth_pose_sequence

    @modal.method()
    def process(self, video_path: str) -> str:
        import cv2, numpy as np, json, os
        cap = cv2.VideoCapture(video_path)
        keypoints = []
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
            rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.pose.process(rgb)
            if results.pose_landmarks:
                pts = [(lm.x, lm.y, lm.z) for lm in results.pose_landmarks.landmark]
            else:
                pts = [(0.0, 0.0, 0.0)] * 33
            keypoints.append(pts)
        cap.release()
        arr = np.array(keypoints, dtype=np.float32)
        smoothed = self.smooth(arr, method='smoothnet')

        video_name = os.path.basename(video_path)
        out_name = video_name.rsplit('.',1)[0] + '_pose.json'
        out_path = f"{MOUNT_PATH}/results/{out_name}"
        os.makedirs(os.path.dirname(out_path), exist_ok=True)
        data = {'video': video_name, 'frames': []}
        for i, frame in enumerate(smoothed):
            data['frames'].append({'frame': i, 'keypoints': [{'x': float(x), 'y': float(y), 'z': float(z)} for x,y,z in frame]})
        with open(out_path, 'w') as f:
            json.dump(data, f)
        return out_path

# 4. Local entrypoint for CLI invocation
@app.local_entrypoint()
def main(video: str):
    input_path = video if video.startswith(MOUNT_PATH) else f"{MOUNT_PATH}/{video}"
    result = PoseEstimator().process(input_path)
    print(f"✅ Completed: {result}")
