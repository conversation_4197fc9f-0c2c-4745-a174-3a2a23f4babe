"""
Quick test script to run pose inference without SmoothNet
"""
import modal

# Run the pose estimation without SmoothNet
def test_without_smoothnet():
    # The full command to test
    test_command = """
    modal run pose_inference.py::app.cls.process_video \
        --analysis-id "test-coordinate-fix-no-smoothnet" \
        --video-path "Michael_test_side.mp4" \
        --view-type "side"
    """
    
    print("To test without SmoothNet:")
    print("1. Comment out lines 483-517 in pose_inference.py (_apply_smoothnet method)")
    print("2. Replace with: return frames_data")
    print("3. Run:", test_command)
    
    print("\nThis will bypass SmoothNet and return raw BlazePose coordinates")
    print("If coordinates are correct (center ~540,960), it confirms SmoothNet is the issue")

if __name__ == "__main__":
    test_without_smoothnet()