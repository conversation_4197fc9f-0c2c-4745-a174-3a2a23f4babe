"""
Test S3 connectivity for Modal
"""

import modal
import os

app = modal.App(
    name="maxwattz-s3-test",
    secrets=[modal.Secret.from_name("maxwattz-videos")],
    volumes={
      "/mnt/s3": modal.CloudBucketMount(
        "arn:aws:s3:us-east-1:043309327841:accesspoint/biometricvideos",
        secret=modal.Secret.from_name("maxwattz-videos")
      )
    }
)

@app.function()
def test_s3_access():
    """Test basic S3 bucket access"""
    import boto3

    print("Testing S3 access...")

    # List directories in the mounted S3 bucket
    mount_path = "/mnt/s3"

    try:
        # List top-level directories
        contents = os.listdir(mount_path)
        print(f"✅ S3 bucket mounted successfully!")
        print(f"Found {len(contents)} items in bucket root:")

        for item in contents[:10]:  # Show first 10 items
            item_path = os.path.join(mount_path, item)
            if os.path.isdir(item_path):
                print(f"  📁 {item}/")
            else:
                print(f"  📄 {item}")

        # Check for expected directories
        expected_dirs = [
            "maxwattz-running-videos-raw-side",
            "maxwattz-running-videos-raw-rear",
            "maxwattz-running-metrics-side",
            "maxwattz-running-metrics-rear"
        ]

        print("\nChecking for expected directories:")
        for dir_name in expected_dirs:
            dir_path = os.path.join(mount_path, dir_name)
            if os.path.exists(dir_path):
                print(f"  ✅ {dir_name}")
                # Count files in directory
                try:
                    files = os.listdir(dir_path)
                    print(f"     Contains {len(files)} files")
                except:
                    pass
            else:
                print(f"  ❌ {dir_name} (not found)")

        # Test write access
        test_file = os.path.join(mount_path, "test-write-access.txt")
        try:
            with open(test_file, 'w') as f:
                f.write("Modal S3 write test successful!")
            print("\n✅ Write access confirmed")

            # Clean up
            os.remove(test_file)
            print("✅ Cleanup successful")
        except Exception as e:
            print(f"\n❌ Write access failed: {e}")

        return {"success": True, "message": "S3 access working correctly"}

    except Exception as e:
        print(f"❌ Error accessing S3: {e}")
        return {"success": False, "error": str(e)}

@app.local_entrypoint()
def main():
    """Run the S3 test"""
    result = test_s3_access.remote()
    print(f"\nTest result: {result}")

if __name__ == "__main__":
    main()
