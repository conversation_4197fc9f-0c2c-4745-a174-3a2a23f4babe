#!/usr/bin/env python3
"""
Test the updated SmoothNet implementation on Modal
"""

import modal
import boto3
import json
import sys
from datetime import datetime

# Import the Modal app directly
sys.path.append('.')
from pose_inference import app, PoseEstimator

@app.local_entrypoint()
def test_smoothnet():
    print("=== SmoothNet Modal Test ===")
    print(f"Time: {datetime.now()}")
    
    # Test parameters using correct structure
    s3_video_key = "maxwattz-running-videos-raw-side/Michael_test_side.mp4"
    test_id = f"smoothnet-test-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
    
    test_params = {
        'video_s3_key': s3_video_key,
        'analysis_id': test_id,
        'view_type': 'side',
        'disable_smoothnet': False,  # Enable SmoothNet
    }
    
    print(f"\nProcessing video: {s3_video_key}")
    print("Using updated SmoothNet with:")
    print("- ✅ Tensor format (1, T, 99)")
    print("- ✅ World coordinates with root-relative")
    print("- ✅ 32-frame sliding window")
    print("- ✅ Official config (RES_HIDDEN=16, BLOCKS=1)")
    print("- ✅ Coordinate validation")
    
    print(f"\nTest ID: {test_id}")
    
    # Process video
    print("\nCalling Modal pose estimation...")
    try:
        # Call the remote function using correct method name
        estimator = PoseEstimator()
        result = estimator.process.remote(**test_params)
        
        print(f"\n✅ Processing complete!")
        print(f"Output saved to: {result.get('output_s3_key', 'Unknown')}")
        
        # Download and check results
        s3_client = boto3.client('s3')
        local_path = f"pose-detection/src/analysis-app/{test_id}_pose.json"
        
        if result.get('output_s3_key'):
            print(f"\nDownloading results to: {local_path}")
            s3_client.download_file(
                'maxwattz-videos',
                result['output_s3_key'],
                local_path
            )
            
            # Load and analyze results
            with open(local_path, 'r') as f:
                data = json.load(f)
            
            print(f"\n=== Results Analysis ===")
            print(f"Video: {data['video']}")
            print(f"Dimensions: {data['videoWidth']}x{data['videoHeight']}")
            print(f"FPS: {data['fps']}")
            print(f"Frames: {len(data['frames'])}")
            print(f"Model: {data['modelType']}")
            
            # Check first few frames for coordinate validation
            print(f"\n=== Coordinate Check ===")
            for i in range(min(3, len(data['frames']))):
                frame = data['frames'][i]
                keypoints = frame['keypoints']
                
                # Calculate center of mass
                valid_kps = [kp for kp in keypoints if kp['score'] > 0.5]
                if valid_kps:
                    avg_x = sum(kp['x'] for kp in valid_kps) / len(valid_kps)
                    avg_y = sum(kp['y'] for kp in valid_kps) / len(valid_kps)
                    
                    print(f"\nFrame {frame['frameNumber']}:")
                    print(f"  Center of mass: ({avg_x:.1f}, {avg_y:.1f})")
                    print(f"  Expected center: (~{data['videoWidth']/2:.0f}, {data['videoHeight']/2:.0f})")
                    
                    # Check if coordinates are near center
                    x_deviation = abs(avg_x - data['videoWidth']/2) / data['videoWidth']
                    y_deviation = abs(avg_y - data['videoHeight']/2) / data['videoHeight']
                    
                    if x_deviation < 0.1 and y_deviation < 0.1:
                        print(f"  ✅ Coordinates properly centered!")
                    else:
                        print(f"  ⚠️  Deviation: X={x_deviation*100:.1f}%, Y={y_deviation*100:.1f}%")
            
            print(f"\n✅ Test complete! Results saved to: {local_path}")
            print(f"\nTo view in browser, update dev-app-smoothnet.html line 695:")
            print(f"const localFile = './{test_id}_pose.json';")
            
            return local_path
        else:
            print(f"\n⚠️ No output S3 key in result")
            return None
        
    except Exception as e:
        print(f"\n❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    result_path = test_smoothnet()