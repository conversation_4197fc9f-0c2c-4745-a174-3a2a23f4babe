# Project Insights

I need to give you some context to what we are doing. **WE ARE IN PLANNING MODE RIGHT NOW.
-This is a repo provided by TensorFlow. The directory had all their models. I removed a lot of them. However, I wasn't sure about a few.
-This is a singular project that we are creating to test the use case of BlazePose. We are ONLY using BlazePose. No fallback model.
-We had some issues with a prior build that was missing several function, imports, etc.. So we are starting fresh with a forked version so that I know we have everything.
-Now that we have everything, we are going to implement the customization we applied to the previous attempt.
-The previous attempt simply integrated the model into our web app. So we now have user height inputs to better help with the scaling. We have 2 seperate views. A side view and a rear view of the runner. *We are only working on SideView right now*. We will be running metrics and analysis off the videos.
-You can view the @README_ProjectOverview.md This file is from the other directory. It describes the files that are in blazepose-only-analysis-v2-viscode on my desktop.
*IMPORTANT TO NOTE* We are not corrupting any of the files in this directory. We do need to customize a few, as you'll see from the @CustomFiles_V2_Directory.md
Again the files mentioned in this document are in the blazepose-only-analysis-v2-viscode directory that is our previous attempt.
-You can see from the Screenshots folder in this directory, @Screenshots/UploadScreen.png We have 2 uploads from users. A side view video and a rear view video. (Let's only focus on SideView until we can get this working.) We won't worry about the metrics or anything just yet.
-Also in the screenshots is @Screenshots/SuccessfulPose.png This is the general idea. To get a skeleton overlay of the uploaded user in the video. Using WebGL playback right now. Later we will process the videos on our end. For now, MVP is just getting this working.
-@Screenshots/SideViewTab.png is from the UI I just had Lovable create. It's better than the one in the blazepose-only-analysis-v2-viscode directory. You should have the screenshot page file in this directory. @ResultsPanel.tsx.
-Let's not forget @Screenshots/ProcessingScreen.png right now, this is just a processing screen. Later once we are processing off WebGL, we'll add more to this. @ProcessingPanel.tsx\
-This file (Outside of the screenshots and few uploads for reference) does not have any of the custom files. I provided you with the UI files to better help you understand what we are trying to do. As well as the screenshots. What we have to do, is take what we have in this directory, and work towards the Analysis Tool Page UI. However, not to make the previous mistake, we are going to not alter the TensorFlow pipeline for BlazePose. There will have to be adjustments to some files. Video size outputs, etc.. User Height inputs that might alter how the pose detection works. If we use user height to assist the pipeline, that could cause problems. So with that said, I want to add new pieces one at a time to make sure we are constantly functioning before adding other parts. For instance the metrics are just statics representaitons. We'll dial that in once the overlay is working using BlazePose Full capturing x,y, and z outputs from the user's pose.
  **Assumptions, the user will always be in frame. And their full body will be close to the center. You will have user inputs of "Height", "Male/Female", and weight. The Height to assist with appropriate scaling. And Male/Female if you need to use any baseline level assumptions about limb length.
-For the Pose Detection Pipeline. We are NOT using a fallback model. If it has no detection, then we need to adjust the confidence level.
-UNDER NO CIRCUMSTANCES ARE WE TO USE HARDCODED VARIABLES for confidence or for user's height, etc.. ALL MUST BE CALCULATED.
-Assume eight 1080 × 1920 "HD" at 30FPS or 2160 × 3840 "4k" at 30FPS video uploads.\
-My thought was we'd reference the other file to examine how we previously attempted the feature. But only implement what was done if it doesn't break anything or alter our directories
  funcitonality. But each one will be added at a single time, to validate we are still functioning
*Now that I have given you this context, I want you to review this information and ask me any clarifying questions*
    ⎿  Read README_ProjectOverview.md (386 lines)
    ⎿  Read CustomFiles_V2_Directory.md (306 lines)
    ⎿  Read Screenshots/UploadScreen.png (107.8KB)
    ⎿  Read Screenshots/SuccessfulPose.png (1.1MB)
    ⎿  Read Screenshots/SideViewTab.png (425.4KB)
    ⎿  Read ResultsPanel.tsx (400 lines)
    ⎿  Read Screenshots/ProcessingScreen.png (53.5KB)
    ⎿  Read ProcessingPanel.tsx (73 lines)\

# Your Previous Clarifying Questions

1.Model Integration Approach: You mentioned not corrupting the TensorFlow files but needing customizations. Should we:
-Create a new pose-detection variant (like pose-detection-custom) that imports from the main pose-detection?
-Or work within the existing pose-detection structure but add our custom components alongside?
2.Video Processing Pipeline: For the initial MVP with WebGL playback:
-Should we focus on real-time overlay during video playback (like the screenshots show)?
-Do we need to store/export the pose data, or just display the overlay for now?\
3.User Height Integration: You mentioned user height for scaling but warned against altering the pipeline. Should we:
-Apply height scaling only to the visual overlay (post-detection)?
      -Or use it in coordinate transformation after BlazePose outputs?
    4.Confidence Thresholds: You emphasized no hardcoded values. Should we:
      -Calculate dynamic confidence thresholds based on video quality/lighting?
      -Start with reasonable defaults that can be adjusted programmatically?
    5.Development Sequence: For adding features incrementally, would this order work:
      -First: Get basic BlazePose Full detection working in this tfjs-models structure
      -Second: Add video player with basic overlay
      -Third: Add user height scaling and UI components
      -Fourth: Add the metrics calculations (as static displays initially)
    6.File Structure: Should we create our web application components in a new directory within this tfjs-models repo, or keep the pose-detection pure and build the app layer separately?
    7.Testing Strategy: How do you want to validate each step? Do you have test videos ready, or should we start with the demo videos from the pose-detection package?
-You: Once I understand your preferences on these points, I can create a detailed implementation plan using the TodoWrite tool.

# My Resoponses

1- We are going to work within the existing pose-detection structure but add our custom components alongside. If need be, create the new file, and we'll archive the original, in case we needto review something from that file later.
2- For now, yes, real-time overlay during video playback. We just need to verify we are able to capture the pose data. Once we get this working, we'll add in the export to the Supabase. Those tables are there already, but there's no point sending potentially incorrect data. And then we'll set up a ML pipeline to ingest the videos to make improvements. But first we need it to
  work in the simple WebGL approach.
3- Yes, I see how that's conflicting.. We are going to use it in coordinate transformation after BlazePose outputs. This is one of those items that we'll need to work with caution. I don't want to alter the pipeline. BUT this will definitely make the output joint angles more precise.
4- Start with 0.5. Once the file is working. We can implement a verification file. That pre reviews the uploaded video to notify the user that the lighting is poor, etc.. For now we'll be using the same test video. I mentioned "no hardcoded values" because in our previous attempt there was a fallback hardcoded threshold that was causing a bug. *NO FALLBACK HARDCODED VALUES*
5- Yes I like that approach.
6- Let's create the web application components in a new directory within this repo. This works because we'll later use MoveNet too, which is why I kept it in the directory. So we can also swap out pose models or front end without having to do major updates.
7- Yes, we will be using the same video that I've used across all attempts. And have used it in other apps to verify my results. My video upload will be the one we test on. And we'll run the dev environment to upload it to validate each step.
  ⎿  Read README_ProjectOverview.md (386 lines)
  ⎿  Read CustomFiles_V2_Directory.md (306 lines)
  ⎿  Read Screenshots/UploadScreen.png (107.8KB)
  ⎿  Read Screenshots/SuccessfulPose.png (1.1MB)
  ⎿  Read Screenshots/SideViewTab.png (425.4KB)
  ⎿  Read ResultsPanel.tsx (400 lines)
  ⎿  Read Screenshots/ProcessingScreen.png (53.5KB)
  ⎿  Read ProcessingPanel.tsx (73 lines)

# BlazePose Full Implementation Plan

## Phase 1: Foundation Setup

1.Create web application structure within pose-detection directory
2.Set up basic development server and build configuration
3.Verify BlazePose Full model can load without modifications

## Phase 2: Core Detection Pipeline

1.Implement basic BlazePose Full detection using existing tfjs-models structure
2.Create minimal video player with canvas overlay
3.Test detection with confidence threshold of 0.5
4.Verify 39 keypoints are being detected

## Phase 3: User Height Integration

1.Add height-based coordinate scaling post-detection
2.Implement proper aspect ratio handling
3.Test scaling accuracy with known reference points

## Phase 4: UI Integration

1.Integrate provided UI components (upload, processing, results)
2.Connect video player to UI workflow
3.Add basic pose data visualization

## Phase 5: Testing & Refinement

1.Test with reference video
2.Adjust confidence thresholds if needed
3.Add static metrics display
4.Validate full pipeline functionality

Each phase will be tested independently before proceeding to ensure stability.

## Your Followup Questions

I've reviewed all three documents and created a task list based on your implementation plan. Here are my clarifying questions:

1.Directory Structure & Integration:
-You mentioned creating the web app components in a new directory within this repo. Should I create something like pose-detection/demo/analysis-tool/ or pose-detection/src/analysis-app/? What
   naming convention would you prefer?

2.Custom File Integration Strategy:
-Looking at the CustomFiles_V2_Directory.md, I see significant customizations were made to core files like tensors_to_detections.ts and detector_result.ts. Should I:
-Start with the vanilla tfjs-models files and add customizations incrementally?
-Or immediately port over the custom implementations from the previous attempt?

3.BlazePose Full Model Configuration:
-The previous implementation had extensive customizations for 39 keypoints. Should I first verify the stock BlazePose Full works, then add the 39-keypoint support, or implement the full
  39-keypoint version from the start?

4.Shared Calculators Approach:
-Many shared calculators were heavily modified. Since you want to avoid corrupting the pipeline, should I:
-Create custom versions alongside the originals (e.g., tensors_to_detections_custom.ts)?
-Or modify the originals but archive the original versions first?

5.Development Environment:
-Should I set up the development environment in the pose-detection directory with its own package.json, or use the root tfjs-models setup?
-Do you want me to use the same tech stack (Vite, React, shadcn-ui) as the previous implementation?

6.Testing Video:
-You mentioned you have a specific test video. What are its specifications (resolution, fps, duration)? This will help ensure the implementation handles it correctly from the start.

7.Performance Monitoring:
-The previous implementation had extensive performance monitoring. Should this be included from the beginning or added later?

  Once I understand these preferences, I can begin with Phase 1: reviewing the existing structure and creating the foundation.

# My Followup Responses

1- pose-detection/src/analysis-app/ looks good. Keep in mind we pruned the Master TFJS-Models repo to remove *MOST* of the other models from TensorFlow. However, I didn't want to potentially remove something important. So keep in mind there are other models in this repo and other files that could have lingered from other models. blazepose_tfjs is the only one we need to focus on.
2-The custom implementations from the previous attempt are for reference only. Being that we never got a working version of the previous attempt, we're starting fresh. Those files were meant to give you context to what we are doing. And to guide you if need be on what the desired outcome is. However, they could potentially contain errors or bugs. So we are going to start with the vanilla tfjs-models files and add customizations incrementatlly.
3-Explain this further. The BlazePose Full model should have those incorporated already in the pipeline. We are not changing the pipeline. So as it sits right now, we shouldn't need to add the 39-keypoint support. However, we are going to need to verify that the 39 keypoints are being detected. And if not, then we'll need to add the support. Our first task might be to do a deep dive on the pipeline as it sits right now.
4-We can create the custom version alongside the originals. And then later migrate the originals to an archive directory.
5-Unless we need to set up the dev environment in a specific way, we can use the root tfjs-models setup. And use the same tech stack as the previous implementation.
6-The test video is 1080 x 1920, 30FPS, 10 seconds long. (10 seconds is the standard we are going to have users upload). Keep in mind, we will be implementing a in app camera capture soon. So we'll need to make sure we know there could be both 1080 x 1920 "HD" at 30FPS or 2160 x 3840 "4k" at 30FPS video uploads. We'll even potentially have 60 FPS. And the LiDAR capacity on both HD and 4K.
7-We'll add the monitoring as we proceed. Right now we should have a "working" version. But as we adjust and customize, we'll add the monitoring.
