import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import VideoPlayer from '@/components/VideoPlayer';
interface VideoFile {
  file: File;
  url: string;
  name: string;
}
interface ResultsPanelProps {
  sideVideo: VideoFile | null;
  rearVideo: VideoFile | null;
  onNewAnalysis: () => void;
  analysisMode: '3D';
  videoSetup: 'Treadmill';
  overlayStyle: string;
  analysisQuality: string;
  userHeight: {
    feet: number;
    inches: number;
  };
  viewType: 'side' | 'rear' | 'coming-soon';
}
const ResultsPanel: React.FC<ResultsPanelProps> = ({
  sideVideo,
  rearVideo,
  onNewAnalysis,
  analysisMode,
  videoSetup,
  overlayStyle,
  analysisQuality,
  userHeight,
  viewType
}) => {
  return <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">

        {/* Video Analysis */}
        <div className="grid grid-cols-1 lg:grid-cols-7 gap-2">
          {/* Left Column - Video */}
          <div className="lg:col-span-3">
            {viewType === 'side' && sideVideo && (
              <Card>
                <CardContent className="p-2 flex justify-center items-center">
                  <VideoPlayer 
                    videoUrl={sideVideo.url} 
                    analysisType="running" 
                    viewType="side" 
                    analysisMode={analysisMode} 
                    videoSetup={videoSetup} 
                    overlayStyle={overlayStyle} 
                    userHeight={userHeight} 
                    onPoseData={data => console.log('Side view pose data:', data)} 
                  />
                </CardContent>
              </Card>
            )}

            {viewType === 'rear' && rearVideo && (
              <Card>
                <CardContent className="p-2 flex justify-center items-center">
                  <VideoPlayer 
                    videoUrl={rearVideo.url} 
                    analysisType="running" 
                    viewType="rear" 
                    analysisMode={analysisMode} 
                    videoSetup={videoSetup} 
                    overlayStyle={overlayStyle} 
                    userHeight={userHeight} 
                    onPoseData={data => console.log('Rear view pose data:', data)} 
                  />
                </CardContent>
              </Card>
            )}

            {viewType === 'coming-soon' && (
              <Card>
                <CardContent className="p-8 text-center">
                  <h3 className="text-xl font-semibold mb-4">Coming Soon</h3>
                  <p className="text-gray-600">Additional analysis features will be available here.</p>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Column - Metrics */}
          <div className="lg:col-span-4 space-y-4">
            {viewType === 'side' && (
              <>
                {/* Movement Analysis Group - Side View */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Movement Analysis</h3>
                    
                    {/* Stride Length */}
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <Activity className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Stride Length</span>
                            <span className="text-sm font-medium text-green-600">Excellent</span>
                          </div>
                          <div className="text-sm text-gray-600">0.16m avg</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Optimal stride length maintained</p>
                    </div>

                    {/* Foot Strike */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Target className="w-5 h-5 text-orange-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Foot Strike</span>
                            <span className="text-sm font-medium text-orange-600">Good</span>
                          </div>
                          <div className="text-sm text-gray-600">Midfoot landing</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-500 h-2 rounded-full" style={{ width: '74%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Consider slight heel strike adjustment</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Posture Analysis Group - Side View */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Posture & Form</h3>
                    
                    {/* Posture */}
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <User className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Posture</span>
                            <span className="text-sm font-medium text-green-600">Excellent</span>
                          </div>
                          <div className="text-sm text-gray-600">Upright alignment</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '96%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Excellent spine alignment maintained</p>
                    </div>

                    {/* Knee Flexion */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <Zap className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Knee Drive</span>
                            <span className="text-sm font-medium text-green-600">Excellent</span>
                          </div>
                          <div className="text-sm text-gray-600">139° peak flexion</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '88%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Optimal knee drive through stance</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Stability Analysis Group - Side View */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Stability & Balance</h3>
                    
                    {/* Pronation */}
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                          <RotateCcw className="w-5 h-5 text-red-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Pronation</span>
                            <span className="text-sm font-medium text-red-600">Needs Work</span>
                          </div>
                          <div className="text-sm text-gray-600">Overpronation detected</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-red-500 h-2 rounded-full" style={{ width: '45%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Consider motion control shoes or orthotics</p>
                    </div>

                    {/* Balance */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Scale className="w-5 h-5 text-orange-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Balance</span>
                            <span className="text-sm font-medium text-orange-600">Good</span>
                          </div>
                          <div className="text-sm text-gray-600">Stable core</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-500 h-2 rounded-full" style={{ width: '78%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Minor lateral movement detected</p>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}

            {viewType === 'rear' && (
              <>
                {/* Movement Analysis Group - Rear View */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Movement Analysis</h3>
                    
                    {/* Cadence */}
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <Activity className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Cadence</span>
                            <span className="text-sm font-medium text-green-600">Excellent</span>
                          </div>
                          <div className="text-sm text-gray-600">180 steps/min</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '90%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Optimal cadence for efficiency</p>
                    </div>

                    {/* Ground Contact Time */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Target className="w-5 h-5 text-orange-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Ground Contact</span>
                            <span className="text-sm font-medium text-orange-600">Good</span>
                          </div>
                          <div className="text-sm text-gray-600">245ms avg</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-500 h-2 rounded-full" style={{ width: '72%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Slightly longer contact time detected</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Posture Analysis Group - Rear View */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Posture & Form</h3>
                    
                    {/* Hip Alignment */}
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <User className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Hip Alignment</span>
                            <span className="text-sm font-medium text-green-600">Excellent</span>
                          </div>
                          <div className="text-sm text-gray-600">Level pelvis</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '94%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Consistent hip level throughout gait</p>
                    </div>

                    {/* Shoulder Level */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                          <Zap className="w-5 h-5 text-orange-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Shoulder Level</span>
                            <span className="text-sm font-medium text-orange-600">Good</span>
                          </div>
                          <div className="text-sm text-gray-600">Minor tilt detected</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-orange-500 h-2 rounded-full" style={{ width: '76%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Left shoulder slightly higher</p>
                    </div>
                  </CardContent>
                </Card>

                {/* Stability Analysis Group - Rear View */}
                <Card>
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold mb-4 text-gray-800">Stability & Balance</h3>
                    
                    {/* Left/Right Balance */}
                    <div className="space-y-3 mb-6">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                          <RotateCcw className="w-5 h-5 text-red-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Left/Right Balance</span>
                            <span className="text-sm font-medium text-red-600">Needs Work</span>
                          </div>
                          <div className="text-sm text-gray-600">52% left, 48% right</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-red-500 h-2 rounded-full" style={{ width: '48%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Favoring left side slightly</p>
                    </div>

                    {/* Pelvic Stability */}
                    <div className="space-y-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <Scale className="w-5 h-5 text-green-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="font-medium text-gray-800">Pelvic Stability</span>
                            <span className="text-sm font-medium text-green-600">Excellent</span>
                          </div>
                          <div className="text-sm text-gray-600">Minimal rotation</div>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div className="bg-green-500 h-2 rounded-full" style={{ width: '91%' }}></div>
                      </div>
                      <p className="text-xs text-gray-500">Strong core stability maintained</p>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}

            {viewType === 'coming-soon' && (
              <Card>
                <CardContent className="p-8 text-center">
                  <h3 className="text-xl font-semibold mb-4">Coming Soon</h3>
                  <p className="text-gray-600">Analysis metrics will be available here.</p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>


        {/* New Analysis Button */}
        <div className="text-center">
          <button onClick={onNewAnalysis} className="inline-flex items-center gap-3 px-8 py-4 rounded-lg text-lg font-semibold bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all">
            <RotateCcw className="w-5 h-5" />
            Start New Analysis
          </button>
        </div>
      </div>
    </div>;
};
import { RotateCcw, Activity, Target, User, Zap, Scale } from 'lucide-react';
export default ResultsPanel;