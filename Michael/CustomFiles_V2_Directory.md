# Custom Files Analysis
**blazepose-only-analysis-v2-viscode vs tfjs-models-master Reference**

This document identifies all files that have been customized from the reference implementation in the tfjs-models-master directory. Any file NOT listed here should be considered a direct copy of the reference implementation.

---

## ✅ **HEAVILY CUSTOMIZED FILES**

### 1. **Core BlazePose TFJS Implementation**

#### `src/blazepose_tfjs/constants.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/blazepose_tfjs/constants.ts`
- **Status**: **HEAVILY CUSTOMIZED** (542 lines vs 180 lines)
- **Key Modifications**:
  - Added `BLAZEPOSE_FULL_KEYPOINTS` array (39 landmarks) with detailed names
  - Extended `BLAZEPOSE_FULL_MODEL_TENSOR_SPECS` for Full model tensor dimensions
  - Added comprehensive model version management with URL validation
  - Added model URL validation and fallback functions
  - Added anchor configuration validation functions
  - Added performance monitoring constants (PERFORMANCE_LIMITS)
  - Custom tensor dimension validation functions
  - Updated anchor configuration to match reference (aspect ratios, strides, etc.)

#### `src/blazepose_tfjs/detector.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/blazepose_tfjs/detector.ts`
- **Status**: **CUSTOMIZED**
- **Key Modifications**:
  - Different import paths (custom `blazepose_constants` vs reference `constants`)
  - Added custom performance monitoring imports
  - Added custom tensor processing imports
  - Added pipeline trace logging for debugging

#### `src/blazepose_tfjs/detector_utils.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/blazepose_tfjs/detector_utils.ts`
- **Status**: **CUSTOMIZED**
- **Key Modifications**:
  - Enhanced model configuration validation
  - Added custom model type validation
  - Added debugging and logging capabilities

---

## 🔧 **MODIFIED SHARED CALCULATORS**

### 2. **Tensor Processing Files**

#### `src/shared/calculators/tensors_to_detections.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/shared/calculators/tensors_to_detections.ts`
- **Status**: **HEAVILY CUSTOMIZED**
- **Key Modifications**:
  - Added performance monitoring integration
  - Added memory management for tensor operations
  - Added detection optimization features
  - Enhanced error handling and logging
  - Added coordinate transformation fixes

#### `src/shared/calculators/detector_result.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/shared/calculators/detector_result.ts`
- **Status**: **HEAVILY CUSTOMIZED**
- **Key Modifications**:
  - Added comprehensive tensor validation
  - Added performance monitoring integration
  - Added memory management
  - Added coordinate transformation debugging
  - Fixed coordinate order swapping bug (xCenter, yCenter)

#### `src/shared/calculators/create_ssd_anchors.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/shared/calculators/create_ssd_anchors.ts`
- **Status**: **CUSTOMIZED**
- **Key Modifications**:
  - Added dynamic feature map calculation
  - Added extensive debugging and logging
  - Added coordinate validation
  - Added support for empty feature map arrays

#### `src/shared/calculators/calculate_alignment_points_rects.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/shared/calculators/calculate_alignment_points_rects.ts`
- **Status**: **CUSTOMIZED**
- **Key Modifications**:
  - Added ROI validation to prevent negative coordinates
  - Expanded keypoint validation range from ±0.5 to ±2.0
  - Added face keypoint extraction functionality
  - Enhanced error handling

#### `src/shared/calculators/tensors_to_landmarks.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/shared/calculators/tensors_to_landmarks.ts`
- **Status**: **CUSTOMIZED**
- **Key Modifications**:
  - Fixed score extraction from 5-dimensional tensor data
  - Applied sigmoid activation to visibility scores
  - Enhanced tensor processing for Full model

#### `src/shared/calculators/normalized_keypoints_to_keypoints.ts`
- **Reference**: `tfjs-models-master/pose-detection/src/shared/calculators/normalized_keypoints_to_keypoints.ts`
- **Status**: **CUSTOMIZED**
- **Key Modifications**:
  - Fixed hardcoded fallback coordinates (0.5) causing center clustering
  - Implemented validation with -0.5 to 1.5 range for off-screen detection
  - Added comprehensive coordinate transformation logic

---

## 🚀 **COMPLETELY CUSTOM FILES** (Not in Reference)

### 3. **Custom Tensor Processing**

#### `src/shared/calculators/tensors_to_detections_reference.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Complete reference implementation of decodeBoxes logic with 230 lines of tensor-based coordinate decoding

#### `src/shared/calculators/blazepose_tensor_processor.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Custom tensor processing utilities for BlazePose

#### `src/shared/calculators/tensor_utils.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Memory management utilities for tensor operations

#### `src/shared/calculators/blazepose_constants.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Custom keypoint definitions and skeletal connections for BlazePose Full model

#### `src/shared/calculators/roi_processing.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Region of interest processing for pose detection

#### `src/shared/calculators/tensors_to_pose_landmarks_and_segmentation.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Combined processing for pose landmarks and segmentation

### 4. **Custom Interfaces**

#### `src/shared/calculators/interfaces/tensor_interfaces.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Custom tensor interface definitions

### 5. **Custom Filters**

#### `src/shared/filters/pose_stability_filter.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Custom pose stability filtering

### 6. **Performance Monitoring**

#### `src/blazepose_tfjs/performance_monitor.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Performance monitoring system for detection pipeline

---

## 🎨 **ENTIRELY CUSTOM DIRECTORIES**

### 7. **Custom Utilities Directory** (`src/utils/`)
**All files in this directory are custom implementations:**

- `blazePoseRenderer.ts` - Custom rendering utilities
- `bounding_box_tracker.ts` - Bounding box tracking
- `canvas_debug.ts` - Canvas debugging utilities
- `detection_optimizer.ts` - Detection optimization
- `keypoint_tracker.ts` - Keypoint tracking
- `logging_system.ts` - Comprehensive logging system
- `memory_manager.ts` - Memory management system
- `poseCalculations.ts` - Pose calculation utilities
- `poseRenderer.ts` - Pose rendering
- `pose_drawing.ts` - Pose drawing utilities
- `sideViewRenderer.ts` - Side view rendering
- `tracker.ts` - General tracking utilities
- `tracker_utils.ts` - Tracking helper functions
- `types.ts` - Custom type definitions
- `interfaces/common_interfaces.ts` - Custom common interfaces
- `interfaces/config_interfaces.ts` - Custom configuration interfaces

### 8. **React Components Directory** (`src/components/`)
**All files in this directory are custom React components:**

- `SideViewBlazePoseOverlay.tsx` - Main pose detection component
- `PoseOverlay.tsx` - Pose overlay routing component
- `VideoPlayer.tsx` - Video player with pose integration
- `ConfigurationPanel.tsx` - Configuration UI
- `AnglePanel.tsx` - Angle analysis panel
- `BlazePoseOverlay.tsx` - BlazePose overlay component
- `ProcessingPanel.tsx` - Processing status panel
- `ResultsPanel.tsx` - Results display panel
- `UploadPanel.tsx` - File upload interface
- `ui/` directory - Complete UI component library (shadcn-ui based)

### 9. **React Hooks Directory** (`src/hooks/`)
**All files in this directory are custom React hooks:**

- `useBlazePoseDetection.ts` - Main pose detection hook
- `use-toast.ts` - Toast notification hook
- `use-mobile.tsx` - Mobile detection hook

### 10. **Custom Application Files**

#### `src/pages/Index.tsx`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Main application page component

#### `src/App.tsx`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Root application component

#### `src/types.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Application-wide TypeScript type definitions

#### `src/pose_detector.ts`
- **Reference**: None (Custom implementation)
- **Status**: **CUSTOM**
- **Purpose**: Pose detector interface definitions

---

## 📝 **FILES THAT SHOULD BE DIRECT COPIES**

**Important**: All files NOT listed above should be considered direct copies of the reference implementation. If any of these files have been modified from the reference, they should be reverted to match the reference exactly:

### **Shared Calculators (Should Match Reference):**
- `association_norm_rect.ts`
- `calculate_inverse_matrix.ts`
- `calculate_landmark_projection.ts`
- `calculate_score_copy.ts`
- `calculate_world_landmark_projection.ts`
- `constants.ts`
- `convert_image_to_tensor.ts`
- `detection_projection.ts`
- `detection_to_rect.ts`
- `get_object_scale.ts`
- `get_rotated_sub_rect_to_rect_transformation_matrix.ts`
- `image_utils.ts`
- `is_video.ts`
- `keypoints_to_normalized_keypoints.ts`
- `landmarks_refinement.ts`
- `landmarks_to_detection.ts`
- `mask_util.ts`
- `non_max_suppression.ts`
- `refine_landmarks_from_heatmap.ts`
- `remove_detection_letterbox.ts`
- `remove_landmark_letterbox.ts`
- `render_util.ts`
- `segmentation_smoothing.ts`
- `shift_image_value.ts`
- `sigmoid.ts`
- `split_detection_result.ts`
- `tensors_to_segmentation.ts`
- `transform_rect.ts`

### **Interface Files (Should Match Reference):**
- `interfaces/common_interfaces.ts`
- `interfaces/config_interfaces.ts`
- `interfaces/shape_interfaces.ts`

### **Filter Files (Should Match Reference):**
- `keypoints_one_euro_filter.ts`
- `keypoints_smoothing.ts`
- `keypoints_velocity_filter.ts`
- `low_pass_filter.ts`
- `one_euro_filter.ts`
- `relative_velocity_filter.ts`
- `visibility_smoothing.ts`

### **BlazePose TFJS Files (Should Match Reference):**
- `types.ts` (needs verification)

---

## 🔍 **VERIFICATION NEEDED**

### **Files That May Have Hidden Modifications:**
These files should be compared byte-by-byte with the reference to ensure they haven't been inadvertently modified:

1. All files in the "Should Match Reference" section above
2. Any interface files that may have been extended
3. Any calculator files that may have subtle logging or performance changes

---

## 📊 **SUMMARY**

**Total Custom/Modified Files**: 50+
- **Heavily Customized**: 6 core files
- **Modified**: 12 calculator files
- **Completely Custom**: 30+ files
- **Custom Directories**: 3 entire directories (utils, components, hooks)

**Approach for Bug Fixing**:
1. **Preserve Custom Files**: All files listed in this document represent intentional customizations
2. **Verify Reference Copies**: Compare all unlisted files with reference to ensure they match exactly
3. **Focus on Modified Files**: The broadcasting error likely stems from modifications in the "Modified" section
4. **Systematic Replacement**: Consider replacing modified files with reference versions one by one to isolate the issue

This analysis provides a clear roadmap for identifying which files are custom implementations versus which should match the reference exactly.