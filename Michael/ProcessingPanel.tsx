
import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';

interface ProcessingPanelProps {
  analysisMode?: '2D' | '3D';
}

const ProcessingPanel: React.FC<ProcessingPanelProps> = ({ analysisMode = '2D' }) => {
  const [progress, setProgress] = useState(0);
  const [frame, setFrame] = useState(0);
  const [elapsed, setElapsed] = useState(0);

  const totalFrames = 232;
  const estimatedTotal = 15; // seconds

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = Math.min(prev + 2, 100);
        setFrame(Math.floor((newProgress / 100) * totalFrames));
        setElapsed(Math.floor((newProgress / 100) * estimatedTotal));
        return newProgress;
      });
    }, 100);

    return () => clearInterval(interval);
  }, []);

  const remaining = Math.max(0, estimatedTotal - elapsed);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-6">
      <Card className="w-full max-w-2xl">
        <CardContent className="p-8">
          <div className="space-y-6">
            <div className="text-center space-y-2">
              <h1 className="text-3xl font-bold text-gray-900">{analysisMode} Pose Processing</h1>
              <div className="text-lg text-gray-600">
                Status: <span className="font-semibold text-blue-600">PROCESSING</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between text-sm text-gray-600">
                <span>Processing {analysisMode} pose at 3.70s</span>
                <span>Frame: {frame} / {totalFrames}</span>
              </div>
              
              <div className="w-full bg-gray-200 rounded-full h-4 overflow-hidden">
                <div 
                  className="h-full bg-gradient-to-r from-blue-500 to-green-500 transition-all duration-300 ease-out"
                  style={{ width: `${progress}%` }}
                />
              </div>

              <div className="flex justify-between text-sm text-gray-600">
                <span>{progress}% Complete</span>
                <div className="flex gap-4">
                  <span>Time Remaining: 0:{remaining.toString().padStart(2, '0')}</span>
                  <span>Elapsed: 0:{elapsed.toString().padStart(2, '0')}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProcessingPanel;
