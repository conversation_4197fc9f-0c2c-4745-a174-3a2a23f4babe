# BlazePose Skeletal Overlay for Runner Analysis

## 🏃‍♂️ Project Overview

This is a **BlazePose skeletal overlay project** designed for analyzing runner videos to capture precise x, y, z coordinates for biomechanical metrics. The application uses Google's BlazePose Full model to provide real-time 3D pose detection and analysis, specifically optimized for treadmill running analysis.

### Primary Use Case
- **Runner Video Analysis**: Capture and analyze running form through video
- **3D Coordinate Extraction**: Extract precise x, y, z coordinates from pose keypoints
- **Biomechanical Metrics**: Generate running metrics and form analysis
- **Treadmill Optimization**: Designed for controlled treadmill environments

### Current Focus
- **SideView Video Playback**: Primary implementation focuses on side-view camera angles
- **Real-time Pose Detection**: Live skeletal overlay during video playback
- **Enhanced Visualization**: Medical-grade skeletal connections and keypoint rendering

---

## 🔄 Visual Data Pipeline

The application follows a comprehensive data processing pipeline from video input to pose analysis output:

```
Video Upload → Video Player → BlazePose Detection → TensorFlow.js Processing
     ↓              ↓                ↓                        ↓
Processing     Pose Overlay    Detection Hook         Model Loading
     ↓              ↓                ↓                        ↓
Pose Detection → Landmark Extraction → 3D Coordinate Calculation
     ↓                    ↓                        ↓
224x224 Input      256x256 Input           39 Keypoints + 3D
     ↓                    ↓                        ↓
Smoothing & Filtering → Canvas Overlay → Pose Data Output
     ↓                    ↓                        ↓
Keypoint Smoothing   Skeletal Rendering    2D/3D Coordinates
Visibility Filter    Medical Connections   Visibility Scores
Pose Stability       Real-time Display     Biomechanical Data
```

### Processing Steps:
1. **Video Input** → User uploads treadmill running video
2. **Pose Detection** → BlazePose detector identifies human poses (224x224 input)
3. **Landmark Extraction** → Landmark model extracts 39 keypoints (256x256 input)
4. **3D Projection** → Converts 2D landmarks to 3D world coordinates
5. **Smoothing Pipeline** → Applies temporal smoothing and stability filters
6. **Visualization** → Renders skeletal overlay on video canvas
7. **Data Export** → Outputs structured pose data with coordinates and metrics

---

## 🏗️ Component Architecture

### Core Components

#### 1. **VideoPlayer** (`src/components/VideoPlayer.tsx`)
- **Purpose**: Main video playback interface with pose overlay integration
- **Props**:
  - `videoUrl: string` - Path to uploaded video file
  - `analysisType: 'running'` - Fixed to running analysis
  - `viewType: 'side' | 'rear'` - Camera angle (currently side-view only)
  - `userHeight: {feet: number, inches: number}` - User height for scaling
  - `onPoseData: (data) => void` - Callback for pose data output
- **Renders**: Video element with overlay canvas and playback controls
- **Integration**: Hosts the PoseOverlay component for real-time analysis

#### 2. **PoseOverlay** (`src/components/PoseOverlay.tsx`)
- **Purpose**: Routing component that selects appropriate pose detection overlay
- **Props**: Analysis configuration and video reference
- **Responsibility**: Routes to SideViewBlazePoseOverlay based on viewType
- **Output**: Renders appropriate pose detection component

#### 3. **SideViewBlazePoseOverlay** (`src/components/SideViewBlazePoseOverlay.tsx`)
- **Purpose**: Main pose detection and visualization component for side-view analysis
- **Props**:
  - `videoRef: React.RefObject<HTMLVideoElement>` - Reference to video element
  - `userHeight: {feet: number, inches: number}` - User height for coordinate scaling
  - `onPoseData: (data) => void` - Pose data callback
- **Renders**: Canvas overlay with skeletal connections and keypoints
- **Features**:
  - Real-time pose detection using BlazePose Full model
  - Enhanced skeletal rendering with medical-grade connections
  - Error handling with circuit breaker pattern
  - Performance monitoring and frame counting

#### 4. **ConfigurationPanel** (`src/components/ConfigurationPanel.tsx`)
- **Purpose**: User interface for configuring analysis parameters
- **Props**: Height settings, overlay style, analysis quality, video setup
- **Renders**: Form controls for user height, technical settings, and analysis options
- **Integration**: Passes configuration to pose detection components

### Core Hooks

#### 5. **useBlazePoseDetection** (`src/hooks/useBlazePoseDetection.ts`)
- **Purpose**: Custom hook managing BlazePose model lifecycle and pose detection
- **Parameters**: `modelQuality: 'Full' | 'Heavy'` (defaults to 'Full')
- **Returns**:
  - `detector: PoseDetector | null` - Initialized BlazePose detector
  - `isInitialized: boolean` - Model loading status
  - `debugInfo: string` - Real-time debugging information
  - `detectPoses: (video) => Promise<Pose[]>` - Pose detection function
- **Features**:
  - Automatic model loading and initialization
  - Enhanced smoothing and filtering pipeline
  - Tensor memory management and cleanup
  - Error handling and recovery

### Utility Modules

#### 6. **BlazePose Constants** (`src/shared/calculators/blazepose_constants.ts`)
- **Purpose**: Configuration constants and keypoint definitions
- **Exports**:
  - `BLAZEPOSE_KEYPOINTS` - 39 keypoint definitions with names
  - `BLAZEPOSE_CONNECTIONS` - Skeletal connection pairs
  - `BLAZEPOSE_FULL_CONFIG` - Model configuration parameters

#### 7. **Pose Calculations** (`src/utils/poseCalculations.ts`)
- **Purpose**: Biomechanical calculations and pose data processing
- **Functions**:
  - `processPoseData()` - Converts raw poses to structured data
  - `calculateRunningMetrics()` - Future biomechanical analysis

---

## 👤 User Experience Walkthrough

### Step 1: Video Upload

- **User Action**: Navigate to upload panel and select treadmill running video
- **System Response**: Video file is validated and preview is generated
- **Requirements**: Video should show runner from side view, 5 feet distance
- **Output**: Video file loaded into application state

### Step 2: Configuration

- **User Action**: Set height (feet/inches) and analysis parameters
- **Available Options**:
  - User height for coordinate scaling
  - Analysis quality (Full/Heavy model)
  - Overlay style (Enhanced)
  - Video setup (Treadmill)
- **System Response**: Configuration validated and stored

### Step 3: Analysis Initiation

- **User Action**: Click "Start 3D Analysis"
- **System Response**:
  - BlazePose model begins loading (may take 10-30 seconds)
  - Progress indicators show initialization status
  - Video player interface appears

### Step 4: Real-time Analysis

- **User Experience**:
  - Video plays with real-time skeletal overlay
  - Green skeletal connections appear over runner
  - Keypoints rendered as colored circles
  - Debug information shows detection status
- **System Processing**:
  - 30 FPS pose detection (when possible)
  - Real-time coordinate extraction
  - Smoothing filters applied to reduce jitter
  - Pose data continuously generated

### Step 5: Data Output

- **Real-time Feedback**:
  - Frame count and pose detection statistics
  - Visibility scores for keypoints
  - 3D coordinate availability status
- **Data Structure**: Each frame generates:
  ```typescript
  {
    frameNumber: number,
    timestamp: number,
    pose: {
      keypoints: Array<{x, y, z, score, name}>,
      keypoints3D: Array<{x, y, z, score, name}>
    },
    visibilityStats: {
      total2D: number,
      visible2D: number,
      total3D: number,
      visible3D: number
    }
  }
  ```

---

## 🔧 Technical Implementation Details

### BlazePose Model Configuration

- **Model Type**: BlazePose Full (39 keypoints + 3D coordinates)
- **Input Sizes**:
  - Detector: 224x224 pixels
  - Landmark: 256x256 pixels
- **Output**: 39 keypoints with x, y, z coordinates and visibility scores
- **3D Capabilities**: Full 3D world coordinate projection

### Key Functions

#### Pose Detection Pipeline
```typescript
// Main detection function in useBlazePoseDetection.ts
const detectPoses = async (video: HTMLVideoElement) => {
  // 1. Validate video readiness
  // 2. Convert video frame to tensor
  // 3. Run BlazePose detection
  // 4. Apply smoothing filters
  // 5. Return structured pose data
}
```

#### Rendering Pipeline
```typescript
// Canvas rendering in SideViewBlazePoseOverlay.tsx
const drawPoseOverlay = (poses, canvas, video) => {
  // 1. Clear canvas (optional in test mode)
  // 2. Scale coordinates to canvas size
  // 3. Draw keypoints as circles
  // 4. Draw skeletal connections
  // 5. Apply visual enhancements
}
```

### Performance Considerations

#### Known Performance Issues
1. **Slow Initialization**: BlazePose model loading can take 10-30 seconds
2. **Memory Usage**: TensorFlow.js operations require significant GPU memory
3. **Frame Rate**: Target 30 FPS, may drop under heavy processing load
4. **Browser Compatibility**: Requires modern browser with WebGL support

#### Optimization Strategies
- **Tensor Memory Management**: Automatic cleanup of intermediate tensors
- **Smoothing Filters**: Reduce jitter while maintaining responsiveness
- **Error Recovery**: Circuit breaker pattern prevents infinite loops
- **Selective Rendering**: Optional canvas clearing for performance testing

### Logging and Debugging

#### Debug Information Display
- **Real-time Status**: Model initialization, detection active/waiting
- **Performance Metrics**: Frame count, pose detection count, processing time
- **Error Handling**: Graceful degradation with user-friendly error messages
- **Memory Monitoring**: Tensor disposal and memory cleanup tracking

#### Logging Levels
```typescript
// Configurable logging in constants
const ENABLE_DETAILED_LOGGING = false; // Reduces bandwidth usage
const ENABLE_PERFORMANCE_LOGGING = true; // Frame rate monitoring
const ENABLE_ERROR_LOGGING = true; // Error tracking and recovery
```

---

## 🚀 Setup and Usage Instructions

### Prerequisites

- **Node.js**: Version 18+ required
- **Modern Browser**: Chrome, Firefox, Safari with WebGL support
- **Hardware**: GPU acceleration recommended for optimal performance

### Installation

1. **Clone Repository**
   ```bash
   cd Desktop/blazepose-only-analysis-v2-viscode
   ```

2. **Install Dependencies**
   ```bash
   npm install
   ```

3. **Start Development Server**
   ```bash
   npm run dev
   ```

4. **Access Application**
   - Open browser to `http://localhost:8080`
   - Application will be available at the local development server

### Usage Instructions

1. **Prepare Video**
   - Record runner on treadmill from side view
   - Maintain 5-foot distance from subject
   - Ensure good lighting and minimal background clutter
   - Recommended resolution: 1080p or higher

2. **Upload and Configure**
   - Upload video file through the interface
   - Set accurate user height for coordinate scaling
   - Select analysis quality (Full recommended)

3. **Run Analysis**
   - Click "Start 3D Analysis"
   - Wait for model initialization (10-30 seconds)
   - Play video to begin real-time pose detection

4. **Monitor Results**
   - Watch skeletal overlay for detection quality
   - Monitor debug panel for performance metrics
   - Pose data automatically generated during playback

### Known Issues and Limitations

#### Environment Compatibility
- **Lovable Environment**: Project has compatibility issues with Lovable platform
- **Local Development**: Recommended to run locally for best performance
- **GPU Requirements**: Requires WebGL-capable graphics for TensorFlow.js

#### Performance Limitations
- **Initialization Time**: Model loading is slow on first run
- **Memory Usage**: High GPU memory requirements may cause browser slowdown
- **Frame Rate**: May not achieve consistent 30 FPS on lower-end hardware

#### Current Limitations
- **Single View**: Only side-view analysis currently implemented
- **Single Person**: Optimized for single runner detection
- **Treadmill Focus**: Designed specifically for treadmill running analysis

### Troubleshooting

#### Common Issues
1. **Slow Loading**: Clear browser cache, ensure stable internet connection
2. **Detection Failures**: Check video quality, lighting, and subject visibility
3. **Performance Issues**: Close other browser tabs, ensure GPU acceleration enabled
4. **Memory Errors**: Refresh page to clear TensorFlow.js memory

#### Debug Mode
- Enable test mode for non-clearing canvas overlay
- Monitor console for detailed logging information
- Check debug panel for real-time status updates

---

## 📊 Future Development

### Planned Features
- **Rear View Analysis**: Additional camera angle support
- **Multi-Person Detection**: Support for multiple runners
- **Biomechanical Metrics**: Stride analysis, cadence calculation, ground contact time
- **Export Functionality**: CSV/JSON data export for further analysis
- **Real-time Feedback**: Live form correction suggestions

### Technical Roadmap
- **Performance Optimization**: Reduce initialization time and memory usage
- **Model Upgrades**: Integration with newer BlazePose model versions
- **Cloud Processing**: Optional server-side processing for enhanced performance
- **Mobile Support**: Responsive design for tablet and mobile analysis

---

## 🛠️ Technology Stack

This project is built with:

- **Frontend Framework**: React 18.3.1 with TypeScript
- **Build Tool**: Vite 5.4.1 with SWC for fast compilation
- **UI Components**: shadcn-ui with Radix UI primitives
- **Styling**: Tailwind CSS 3.4.11 with custom animations
- **Pose Detection**:
  - TensorFlow.js 4.22.0 with WebGPU backend support
  - @tensorflow-models/pose-detection 2.1.3
  - Custom BlazePose Full model implementation
- **State Management**: React Query (TanStack Query) 5.56.2
- **Routing**: React Router DOM 6.26.2
- **Development**: ESLint, TypeScript 5.5.3, Lovable integration

### Key Dependencies
- **@mediapipe/pose**: MediaPipe pose detection models
- **@tensorflow/tfjs-backend-webgpu**: GPU acceleration for pose detection
- **lucide-react**: Icon library for UI components
- **recharts**: Data visualization for future metrics display
- **react-hook-form**: Form handling for configuration panels

---

*This documentation reflects the current state of the BlazePose skeletal overlay project optimized for runner analysis. The project focuses on side-view treadmill analysis with real-time 3D pose detection and coordinate extraction. For technical support or contributions, refer to the project's development team.*
