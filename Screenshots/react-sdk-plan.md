 ✅ Clean separation - main app imports only what it needs✅ Preserve working code - nothing gets
  touched in the base directory✅ Easy maintenance - SDK can be versioned and updated
  independently✅ Clear interface - well-defined API between TFJS work and React integration

  React SDK Folder Structure Outline

  Location: /tfjs-models/pose-detection/src/analysis-app/react-sdk/

  Directory Structure:

  react-sdk/
  ├── package.json                    # SDK dependencies & exports
  ├── tsconfig.json                   # TypeScript config for SDK
  ├── index.ts                        # Main export file
  ├── README.md                       # SDK usage documentation
  │
  ├── components/                     # React Components
  │   ├── VideoAnalysis.tsx          # Main orchestrator component
  │   ├── PoseDetector.tsx           # TFJS BlazePose wrapper
  │   ├── ResultsPage.tsx            # Results with Side/Rear tabs
  │   ├── ProcessedVideoPlayer.tsx   # Video player with pose overlay
  │   ├── UploadManager.tsx          # File upload handling
  │   ├── MetricsDisplay.tsx         # Running metrics visualization
  │   └── TabsContainer.tsx          # Tab switching component
  │
  ├── hooks/                          # React Hooks
  │   ├── useBlazePose.ts            # Pose detection logic
  │   ├── usePoseAnalysis.ts         # Analysis state management
  │   ├── useVideoUpload.ts          # Upload state & validation
  │   ├── useModalIntegration.ts     # Modal processing integration
  │   └── useVideoCanvas.ts          # Video/canvas sync logic
  │
  ├── utils/                          # Utility Functions
  │   ├── coordinates.ts             # From existing utils (copied)
  │   ├── runningMetrics.ts          # From existing utils (copied)
  │   ├── videoProcessing.ts         # Video/canvas sync from dev-app-smoothnet
  │   ├── modalIntegration.ts        # Modal API integration
  │   └── blazePoseHelpers.ts        # TFJS BlazePose utilities
  │
  ├── types/                          # TypeScript Definitions
  │   ├── index.ts                   # Main types export
  │   ├── pose.ts                    # Pose detection types
  │   ├── analysis.ts                # Analysis result types
  │   ├── video.ts                   # Video processing types
  │   └── modal.ts                   # Modal integration types
  │
  ├── styles/                         # Component Styles
  │   ├── components.css             # SDK component styles
  │   ├── video-player.css           # Video player specific styles
  │   └── results.css                # Results page styles
  │
  └── config/                         # Configuration
      ├── blazepose.ts               # BlazePose model configuration
      ├── modal.ts                   # Modal processing config
      └── constants.ts               # SDK constants

  Key Files to Port/Create:

  From dev-app-smoothnet.html:
  - Video/canvas sync logic → utils/videoProcessing.ts
  - Coordinate scaling logic → utils/coordinates.ts (enhanced)
  - Modal data processing → utils/modalIntegration.ts

  From existing components:
  - BlazePose detection logic → hooks/useBlazePose.ts
  - Running metrics → utils/runningMetrics.ts
  - Type definitions → types/

  New React implementations:
  - All TSX components in components/
  - React hooks in hooks/
  - Proper TypeScript throughout

  Package.json Dependencies:

  {
    "name": "@peakinsight/biometrics-sdk",
    "dependencies": {
      "@tensorflow/tfjs": "^4.15.0",
      "@tensorflow/tfjs-backend-webgl": "^4.15.0",
      "@tensorflow-models/pose-detection": "^2.1.0",
      "react": "^18.0.0",
      "react-dom": "^18.0.0"
    },
    "peerDependencies": {
      "react": ">=18.0.0",
      "react-dom": ">=18.0.0"
    }
  }

  Main Export (index.ts):

  // Components
  export { VideoAnalysis } from './components/VideoAnalysis';
  export { ResultsPage } from './components/ResultsPage';
  export { PoseDetector } from './components/PoseDetector';

  // Hooks
  export { useBlazePose } from './hooks/useBlazePose';
  export { usePoseAnalysis } from './hooks/usePoseAnalysis';

  // Types
  export * from './types';

  // Utils (if needed by main app)
  export { RunningMetricsCalculator } from './utils/runningMetrics';

  Usage by Main App:

  // In your Next.js app
  import { VideoAnalysis, ResultsPage, useBlazePose } from '@/lib/react-sdk';

  Does this structure make sense? This gives us:
  - Clean, modular SDK that your main app can import
  - All working logic preserved and enhanced
  - Proper TypeScript throughout
  - Easy to maintain and version separately
  - Clear separation between SDK and existing test/development files

 The user wants to create a new "React SDK" folder within the current
  tfjs-models/pose-detection/src/analysis-app directory. This makes perfect sense - they want to:

  1. Keep all the existing working code untouched
  2. Create a clean, separate React SDK that can be imported by their main app
  3. Only include the necessary files for the React components
  4. Have proper dependencies and structure for the React SDK

  This is actually a great approach because:
  - It preserves all the working test files and development work
  - Creates a clean interface between the working TFJS code and the React integration
  - Makes it easy to maintain and version the React SDK separately
  - Allows the main app to import just what it needs

  Let me think through what needs to go in this React SDK folder:

  1. Core React Components:
    - VideoAnalysis component (main orchestrator)
    - PoseDetector component (TFJS BlazePose wrapper)
    - ResultsPage component (with tabs)
    - UploadManager component
    - ProcessedVideoPlayer component
  2. Hooks:
    - useBlazePose (pose detection logic)
    - usePoseAnalysis (analysis state management)
    - useVideoUpload (upload handling)
    - useModalIntegration (Modal processing)
  3. Utilities:
    - coordinateTransforms (from existing utils)
    - runningMetrics (from existing utils)
    - videoProcessing utilities
    - Modal integration utilities
  4. Types:
    - All TypeScript interfaces
    - Pose data types
    - Analysis result types
  5. Assets/Styles:
    - Component-specific CSS
    - Any required assets
  6. Package Configuration:
    - package.json for the SDK
    - tsconfig.json for TypeScript
    - Export configuration
  7. Key Logic Ported From:
    - dev-app-smoothnet.html video/canvas sync logic

  I'll focus on integrating critical components from existing work, ensuring seamless modal
  handling, precise BlazePose detection, and robust coordinate processing. The key is creating a
  flexible, modular SDK that allows granular imports while maintaining core functionality from the
  original implementation.

  The integration will involve carefully extracting modal management logic, implementing precise
  pose detection mechanisms, and developing sophisticated coordinate scaling techniques. I'll
  ensure each component can be independently imported and used with minimal configuration overhead.

⏺ I'll create a comprehensive todo list to track our React SDK implementation phases. Let me
  examine the current state and create a structured PRD format.
  ⎿  Invalid tool parameters

⏺ Update Todos
  ⎿  ☐ Phase 1: SDK Foundation Setup - Create folder structure, package.json, and TypeScript config
     ☐ Phase 2: Type Definitions - Create comprehensive TypeScript interfaces for poses, metrics,
       and analysis data
     ☐ Phase 3: ProcessedVideoPlayer Component - Port video/canvas sync logic from
       dev-app-smoothnet.html
     ☐ Phase 4: useBlazePose Hook - Create React hook wrapping existing BlazePose detection logic
     ☐ Phase 5: ResultsPage Component - Main results display with tabs, metrics, and video
       integration
     ☐ Phase 6: Metrics Components - TabsContainer and MetricsDisplay with side/rear view
       calculations
     ☐ Phase 7: Utility Functions - Coordinate transformations, metrics calculations, and data
       processing
     ☐ Phase 8: Supabase Integration - Database hooks and API calls for analysis data
     ☐ Phase 9: Styling System - Tailwind CSS classes and component styles matching main app
     ☐ Phase 10: SDK Exports - Create clean export interface and documentation for main app
       integration
     ☐ Phase 11: Integration Testing - Test SDK components in isolation and with main Next.js app

⏺ List(.)
  ⎿  Listed 88 paths (ctrl+r to expand)

⏺ List(react-sdk)
  ⎿  Listed 2 paths (ctrl+r to expand)

⏺ I don't see the react-sdk folder. Let me check if it exists or create it. Here's our React SDK
  Implementation PRD with detailed phases:

  React SDK Implementation - Product Requirements Document

  Phase 1: SDK Foundation Setup

  Status: Pending | Priority: High | Dependencies: None

  Deliverables:
  - Create /react-sdk folder structure
  - Setup package.json with dependencies (React, TypeScript, Tailwind)
  - Configure tsconfig.json for TypeScript
  - Create index.ts main export file

  Phase 2: Type Definitions

  Status: Pending | Priority: High | Dependencies: Phase 1

  Deliverables:
  - types/pose.ts - BlazePose keypoint interfaces
  - types/analysis.ts - Analysis data structures
  - types/metrics.ts - Running metrics interfaces
  - types/supabase.ts - Database table types

  Phase 3: ProcessedVideoPlayer Component

  Status: Pending | Priority: High | Dependencies: Phase 2

  Deliverables:
  - Extract video/canvas sync logic from dev-app-smoothnet.html:100-150
  - Create React component with pose overlay rendering
  - Implement video controls and playback synchronization

  Phase 4: useBlazePose Hook

  Status: Pending | Priority: High | Dependencies: Phase 2

  Deliverables:
  - Wrap existing BlazePose detector logic in React hook
  - Handle tensor memory management
  - Provide pose detection state management

  Phase 5: ResultsPage Component

  Status: Pending | Priority: Medium | Dependencies: Phase 3, 4

  Deliverables:
  - Main results display component
  - Integration with ProcessedVideoPlayer
  - Loading and error states

  Phase 6: Metrics Components

  Status: Pending | Priority: Medium | Dependencies: Phase 2, 5

  Deliverables:
  - TabsContainer for Side/Rear view switching
  - MetricsDisplay with calculated running metrics
  - Metric status indicators (good/warning/needs-attention)

  Phase 7: Utility Functions

  Status: Pending | Priority: Medium | Dependencies: Phase 2

  Deliverables:
  - Port coordinate transformation logic
  - Running metrics calculations (stride, cadence, etc.)
  - Data processing utilities

  Phase 8: Supabase Integration

  Status: Pending | Priority: Medium | Dependencies: Phase 2

  Deliverables:
  - Database hooks for analysis data
  - API integration with Modal processing
  - Real-time status updates
