"""
Debug script to understand the dimension mismatch in SmoothNet pipeline
"""

# Simulate the coordinate transformation pipeline
def debug_coordinate_pipeline():
    print("=== DEBUGGING COORDINATE PIPELINE ===\n")
    
    # Simulate MediaPipe normalized output for center-frame keypoint
    mediapipe_norm_x = 0.5  # Center of frame (normalized)
    mediapipe_norm_y = 0.5  # Center of frame (normalized)
    print(f"1. MediaPipe normalized output: ({mediapipe_norm_x}, {mediapipe_norm_y})")
    
    # Video dimensions from metadata (after MOV rotation fix)
    metadata_width = 1080   # From our Modal output
    metadata_height = 1920  # From our Modal output
    print(f"2. Metadata dimensions: {metadata_width}x{metadata_height}")
    
    # Step 1: Convert to pixels in _process_frame
    pixel_x_step1 = mediapipe_norm_x * metadata_width
    pixel_y_step1 = mediapipe_norm_y * metadata_height
    print(f"3. After _process_frame (norm→pixel): ({pixel_x_step1}, {pixel_y_step1})")
    
    # Step 2: Convert back to normalized in _convert_to_smoothnet_format
    norm_x_step2 = pixel_x_step1 / metadata_width
    norm_y_step2 = pixel_y_step1 / metadata_height
    print(f"4. Before SmoothNet (pixel→norm): ({norm_x_step2}, {norm_y_step2})")
    
    # Step 3: SmoothNet processing (should preserve normalized range)
    # Assuming SmoothNet doesn't change scale (just temporal smoothing)
    smoothnet_out_x = norm_x_step2  # No change expected
    smoothnet_out_y = norm_y_step2  # No change expected
    print(f"5. After SmoothNet (temporal smoothing): ({smoothnet_out_x}, {smoothnet_out_y})")
    
    # Step 4: Convert back to pixels in _convert_from_smoothnet_format
    final_pixel_x = smoothnet_out_x * metadata_width
    final_pixel_y = smoothnet_out_y * metadata_height
    print(f"6. Final pixel coordinates: ({final_pixel_x}, {final_pixel_y})")
    
    print(f"\n=== EXPECTED vs ACTUAL ===")
    print(f"Expected center: ({metadata_width/2}, {metadata_height/2})")
    print(f"Calculated center: ({final_pixel_x}, {final_pixel_y})")
    print(f"Should be identical: {final_pixel_x == metadata_width/2 and final_pixel_y == metadata_height/2}")
    
    print(f"\n=== ACTUAL MODAL RESULTS ANALYSIS ===")
    # From our debug script results
    actual_center_x = 40  # Approximate from debug output
    actual_center_y = 95  # Approximate from debug output
    
    print(f"Actual Modal output center: ({actual_center_x}, {actual_center_y})")
    
    # Calculate what the effective input dimensions would be to get this output
    # If normalized coordinate 0.5 becomes pixel coordinate 40:
    # 0.5 * effective_width = 40 → effective_width = 80
    effective_width = actual_center_x * 2
    effective_height = actual_center_y * 2
    print(f"Implied effective dimensions: {effective_width}x{effective_height}")
    
    # Ratio compared to actual video
    width_ratio = effective_width / metadata_width
    height_ratio = effective_height / metadata_height
    print(f"Scale factors: width={width_ratio:.4f}, height={height_ratio:.4f}")
    
    print(f"\n=== HYPOTHESIS ===")
    if width_ratio < 0.1 and height_ratio < 0.1:
        print("🚨 MAJOR SCALING ISSUE DETECTED!")
        print("The coordinates are being processed as if the video is ~10x smaller")
        print("Possible causes:")
        print("- Wrong dimensions passed to SmoothNet conversion")
        print("- Frame dimensions used instead of metadata dimensions")
        print("- SmoothNet model has implicit scaling assumptions")
        print("- Tensor processing is corrupting coordinate values")
    
    # Check if dimensions look like they might be frame shape instead of metadata
    print(f"\n=== FRAME vs METADATA DIMENSION CHECK ===")
    print("If frame.shape[:2] was used instead of metadata dimensions:")
    # For MOV files, frame might be 1920x1080 (landscape) vs metadata 1080x1920 (portrait)
    frame_height = 1920  # Possible frame dimension
    frame_width = 1080   # Possible frame dimension
    
    # What would happen if we used frame dimensions backwards?
    test_pixel_x = mediapipe_norm_x * frame_width   # 0.5 * 1080 = 540
    test_pixel_y = mediapipe_norm_y * frame_height  # 0.5 * 1920 = 960
    
    # Then normalized using metadata dimensions
    test_norm_x = test_pixel_x / metadata_width  # 540 / 1080 = 0.5 ✓
    test_norm_y = test_pixel_y / metadata_height # 960 / 1920 = 0.5 ✓
    
    # Then back to pixels using metadata
    final_test_x = test_norm_x * metadata_width  # 0.5 * 1080 = 540
    final_test_y = test_norm_y * metadata_height # 0.5 * 1920 = 960
    
    print(f"Frame dimension test result: ({final_test_x}, {final_test_y})")
    print("This would give correct results, so frame/metadata mismatch is NOT the issue")

if __name__ == "__main__":
    debug_coordinate_pipeline()