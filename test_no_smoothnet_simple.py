#!/usr/bin/env python3
"""
Simple test to run pose inference without SmoothNet
"""

# Run this command directly:
print("""
To test without SmoothNet, run this command:

modal run pose_inference.py \\
    --video "Michael_test_side.mp4" \\
    --analysis-id "test-no-smoothnet-fix" \\
    --view-type "side" \\
    --disable-smoothnet

This will:
1. Process the video with SmoothNet DISABLED
2. Output results to S3
3. You can then download and check if coordinates are correct (~540,960 instead of ~40,95)

After running, download the results:
aws s3 cp s3://maxwattz-videos/pose-outputs/test-no-smoothnet-fix_pose.json ./test_no_smoothnet_results.json

Then check the coordinates with:
python check_roi.py  # After updating it to use test_no_smoothnet_results.json
""")