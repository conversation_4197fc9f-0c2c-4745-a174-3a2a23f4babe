#!/usr/bin/env python3
"""
Test the coordinate clamping fix by examining the processed JSON
"""

import json
import sys

def analyze_coordinates(json_file):
    """Analyze coordinate data from processed JSON"""
    
    with open(json_file, 'r') as f:
        data = json.load(f)
    
    print(f"📊 Analyzing: {data.get('video', 'Unknown')}")
    print(f"   Model: {data.get('modelType', 'Unknown')}")
    print(f"   View: {data.get('viewType', 'Unknown')}")
    print(f"   Video dimensions: {data.get('videoWidth', 0)}×{data.get('videoHeight', 0)}")
    print(f"   Total frames: {len(data.get('frames', []))}")
    print()
    
    # Analyze coordinates
    negative_count = 0
    out_of_bounds_count = 0
    samples = []
    
    frames = data.get('frames', [])
    video_width = data.get('videoWidth', 1080)
    video_height = data.get('videoHeight', 1920)
    
    for frame_idx, frame in enumerate(frames[:5]):  # Check first 5 frames
        keypoints = frame.get('keypoints', [])
        
        for kp_idx, kp in enumerate(keypoints[:5]):  # Check first 5 keypoints
            x = kp.get('x', 0)
            y = kp.get('y', 0)
            
            # Check for negative coordinates
            if x < 0 or y < 0:
                negative_count += 1
                samples.append({
                    'frame': frame_idx,
                    'keypoint': kp.get('name', f'kp_{kp_idx}'),
                    'x': x,
                    'y': y,
                    'issue': 'NEGATIVE'
                })
            
            # Check for out-of-bounds
            elif x > video_width or y > video_height:
                out_of_bounds_count += 1
                samples.append({
                    'frame': frame_idx,
                    'keypoint': kp.get('name', f'kp_{kp_idx}'),
                    'x': x,
                    'y': y,
                    'issue': 'OUT_OF_BOUNDS'
                })
    
    # Report findings
    print("🔍 Coordinate Analysis:")
    print(f"   Negative coordinates found: {negative_count}")
    print(f"   Out-of-bounds coordinates found: {out_of_bounds_count}")
    print()
    
    if negative_count > 0 or out_of_bounds_count > 0:
        print("❌ ISSUES FOUND:")
        for sample in samples[:10]:  # Show first 10 issues
            print(f"   Frame {sample['frame']}, {sample['keypoint']}: "
                  f"({sample['x']:.1f}, {sample['y']:.1f}) - {sample['issue']}")
    else:
        print("✅ All coordinates are within valid bounds!")
    
    # Show sample coordinates from first frame
    print("\n📍 Sample coordinates from frame 0:")
    if frames:
        for kp in frames[0].get('keypoints', [])[:5]:
            print(f"   {kp.get('name', 'unknown')}: ({kp.get('x', 0):.1f}, {kp.get('y', 0):.1f})")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python test_coordinate_fix.py <json_file>")
        print("Example: python test_coordinate_fix.py pose-detection/src/analysis-app/test-results.json")
        sys.exit(1)
    
    analyze_coordinates(sys.argv[1])