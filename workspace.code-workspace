{
  "folders": [
    {
      "name": "Common",
      "path": "."
    },
    {
      "name": "Blazeface",
      "path": "blazeface"
    },
    {
      "name": "FaceLandmarksDetection",
      "path": "face-landmarks-detection"
    },
    {
      "name": "HandPose",
      "path": "handpose"
    },
    {
      "name": "PoseDetection",
      "path": "pose-detection"
    },
    {
      "name": "Posenet",
      "path": "posenet"
    },
    {
      "name": "Bodypix",
      "path": "body-pix"
    },
    {
      "name": "CocoSsd",
      "path": "coco-ssd"
    },
    {
      "name": "Deeplab",
      "path": "deeplab"
    },
    {
      "name": "Mobilenet",
      "path": "mobilenet"
    },
    {
      "name": "QnA",
      "path": "qna"
    },
    {
      "name": "SpeechCommands",
      "path": "speech-commands"
    },
    {
      "name": "Toxicity",
      "path": "toxicity"
    },
    {
      "name": "UniversalSentenceEncoder",
      "path": "universal-sentence-encoder"
    },
    {
      "name": "Knn",
      "path": "knn-classifier"
    }
  ],
  "settings": {
    "search.exclude": {
      "**/node_modules": true,
      "**/coverage/": true,
      "**/dist/": true,
      "**/yarn.lock": true,
      "**/.rpt2_cache/": true,
      "**/.yalc/**/*": true,
      "**/.cache/**/*": true
    },
    "tslint.configFile": "tslint.json",
    "files.trimTrailingWhitespace": true,
    "editor.tabSize": 2,
    "editor.insertSpaces": true,
    "[typescript]": {
      "editor.formatOnSave": true
    },
    "[javascript]": {
      "editor.formatOnSave": true
    },
    "[cpp]": {
      "editor.formatOnSave": true
    },
    "editor.defaultFormatter": "xaver.clang-format",
    "editor.rulers": [80],
    "clang-format.style": "Google",
    "files.insertFinalNewline": true,
    "editor.detectIndentation": false,
    "editor.wrappingIndent": "none",
    "typescript.tsdk": "${workspaceRoot}/node_modules/typescript/lib",
    "clang-format.executable": "${workspaceRoot}/node_modules/.bin/clang-format"
  },
  "extensions": {
    "recommendations": [
      // Formats typescript, javascript and c++ code.
      "xaver.clang-format",
      // Lints typescipt code.
      "ms-vscode.vscode-typescript-tslint-plugin",
      // Running custom linters on save.
      "emeraldwalk.RunOnSave"
    ]
  }
}
