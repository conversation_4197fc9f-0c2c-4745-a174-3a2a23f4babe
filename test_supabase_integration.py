#!/usr/bin/env python3
"""
Test Supabase Integration with micha<PERSON>@maxwattz.com
Tests the complete database flow for Modal video processing
"""

import modal
import uuid
from datetime import datetime

# Import the app from pose_inference
from pose_inference import app, PoseEstimator

def test_supabase_integration():
    """Test the full Supabase integration flow"""
    
    print("🧪 Testing Supabase <NAME_EMAIL>")
    print("-" * 60)
    
    # Test parameters
    test_email = "<EMAIL>"
    test_analysis_id = str(uuid.uuid4())
    test_view = "side"
    test_video = "Michael_test_side.mp4"  # Using the test video from earlier
    
    print(f"📧 Test Email: {test_email}")
    print(f"🆔 Test Analysis ID: {test_analysis_id}")
    print(f"👁️ Test View: {test_view}")
    print(f"🎬 Test Video: {test_video}")
    print("-" * 60)
    
    # Run the processing with Supabase integration
    print("\n🚀 Starting Modal processing with Supabase updates...")
    
    with app.run():
        result = PoseEstimator().process.remote(
            video_s3_key=f"maxwattz-running-videos-raw-{test_view}/{test_video}",
            analysis_id=test_analysis_id,
            view_type=test_view,
            queue_id=None,  # Let it create a new queue entry
            video_id=None,  # Let it create a test video record
            user_email=test_email,
            height_inches=70.0,  # 5'10"
            disable_smoothnet=False
        )
    
    print("\n📊 Processing Results:")
    print(f"   Success: {result.get('success', False)}")
    print(f"   Output S3 Key: {result.get('output_s3_key', 'N/A')}")
    print(f"   Frames Processed: {result.get('frames_processed', 0)}")
    print(f"   Processing Time: {result.get('processing_time', 0):.2f}s")
    
    if result.get('success'):
        print("\n✅ Test PASSED! Processing completed successfully.")
        print("\n🔍 Check Supabase tables for:")
        print(f"   - bio_modal_processing_queue: New entry with status 'completed'")
        print(f"   - bio_run_analysis: New/updated entry for analysis_id: {test_analysis_id}")
        print(f"   - bio_run_videos: Test video record (if created)")
        print(f"   - All records should have user_email: {test_email}")
    else:
        print("\n❌ Test FAILED!")
        print(f"   Error: {result.get('error', 'Unknown error')}")
    
    return result

if __name__ == "__main__":
    result = test_supabase_integration()